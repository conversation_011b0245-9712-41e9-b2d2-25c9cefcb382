<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">

    <springProperty scope="context" name="LOG_HOME" source="log.path"/>

    <!-- 日志最大的历史14天 -->
    <property name="maxHistory" value="14"/>

    <!--输出到控制台-->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder charset="UTF-8">
            <pattern>
                %d{HH:mm:ss.SSS} [%X{requestId}] [%X{requestUrl}] [%X{token}] [%thread] %-5level %logger{36} - %msg%n
            </pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
    </appender>

    <!--输出到文件-->
    <appender name="INFO_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/zkqy-lims-admin.log</file>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/zkqy-lims-admin.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>${maxHistory}</maxHistory>
        </rollingPolicy>
        <encoder charset="UTF-8">
            <pattern>
                %d{HH:mm:ss.SSS} [%X{requestId}] [%X{requestUrl}] [%X{token}] [%thread] %-5level %logger{36} - %msg%n
            </pattern>
        </encoder>
    </appender>

    <logger name="java.sql" level="error" additivity="false">
        <appender-ref ref="INFO_FILE"/>
    </logger>

    <root level="DEBUG">
        <appender-ref ref="console"/>
        <appender-ref ref="INFO_FILE"/>
    </root>
</configuration>