SUCCESS=成功
ERROR_OPERATE=操作失败



#@NotBlank
javax.validation.constraints.NotBlank.message = 字段 {0} 为空
#@NotNull
javax.validation.constraints.NotNull.message = 字段 {0} 为空
#@Length
org.hibernate.validator.constraints.Length.message = 字段 {0} 长度不对

javax.validation.constraints.Min.message = 字段 {0} 值错误
javax.validation.constraints.password.regexp.message=密码至少6位字符，支持数字、字母和除空格外的特殊字符，且必须同时包含数字和大小写字母

NOT_FOUND_HANDLER=请求资源地址不存在
METHOD_NOT_SUPPORT=请求方法错误
MEDIA_TYPE_NOT_SUPPORT=请求类型不合法
PARAM_LOSE=参数缺失
PARAM_TYPE_NOT_SUPPORT=参数类型不匹配

FILE_UPLOAD_ERROR=文件上传失败
UPLOAD_FILE_CONFIG_ISNULL_ERROR=文件类型没有配置
UPLOAD_OVER_SIZE=上传的文件超过规定文件的个数
UPLOAD_FILE_SUF_ERROR=上传的文件类型不对
UPLOAD_OVER_LIMIT=上传的文件超过规定文件的大小
UPLOAD_NEED_FILE=请选择上传文件
FILE_NOT_EXIST=文件不存在

DATA_WAS_AUDITING_CANT_EDIT_OR_DELETE = 此数据正在审核中，不允许操作

NOT_TOKEN=当前会话未登录
INVALID_TOKEN=token 无效
TOKEN_TIMEOUT=token 已过期
BE_REPLACED=token 已被顶下线
KICK_OUT=token 已被踢下线
TOKEN_FREEZE=token 已被冻结
NO_PREFIX=未按照指定前缀提交 token

CAPTCHA_NOT_EXIST=验证码无效

NO_PERMISSION=没有权限操作
NO_ROLE=没有角色操作


PLA_USER_NOT_EXISTS=账号不存在
PLA_USER_CAN_NOT_LOGIN=用户不允许登录
PLA_USER_PWD_ERROR=密码安全性太低，请设置6-12位数字/字母组合的密码
PLA_USER_PWD_NOTSAME_ERROR=原密码输入错误
PLA_USER_EMAIL_NOT_EXISTS=邮箱不存在
PLA_USER_CAPTCHA_SEND_FREQUENT=验证码发送太频繁
PLA_USER_CAPTCHA_NOT_EXIST=验证码不存在
PLA_USER_LOGIN_PASSWORD_ERROR=密码错误
PLA_USER_EMAIL_BIND_EXISTS=邮箱已经被绑定
PLA_USER_MOBILE_BIND_EXISTS=手机号码已经被绑定
PLA_USER_NAME_BIND_EXISTS=用户名称存在
PLA_USER_EMPLOYEE_ID_EXISTS=该员工工号存在



PLA_ROLE_NOT_EXISTS=权限不存在
PLA_ROLE_NOT_ADMIN=非运营账号权限
SEND_TEMPLATE_NOT_EXIST=发送模板不存在
ROLE_NAME_EXISTS=角色名称存在

DEPT_HAS_CHILDREN = 部门存在子部门，不允许删除
DEPT_NOT_EXISTS = 部门不存在


PARENT_CATEGORY_NOT_EXISTS = 父类别不存在
CATEGORY_NOT_EXISTS = 类别不存在
CATEGORY_HAS_CHILDREN = 类别存在子类别，不允许删除

PARENT_TYPE_NOT_EXISTS = 父类型不存在
TYPE_HAS_CHILDREN = 类型存在子类型，不允许删除

BUSINESS_NOT_EXISTS =  当前业务不存在
JSON_CONVERT_EXCEPTION = JSON 转换异常

PASS_TO_PREVIOUS_NODE = 通过的节点在当前节点之前，不允许操作
REJECT_TO_SUFFIX_NODE = 驳回的节点在当前节点之后，不允许操作

FLOW_EXCEPTION = 流程异常
DictDataNotExist = 字典数据不存在

MENU_HAS_CHILD = 当前菜单存在子菜单，不允许删除
MENU_NOT_EXIST = 当前菜单不存在



SYSTEM_FILE_TYPE_NOT_EXIST = 文件类型不存在
SYSTEM_FILE_PARENT_TYPE_NOT_EXIST = 文件类型父类型不存在
SYSTEM_FILE_TYPE_NAME_EXISTS = 文件类型名称存在
SYSTEM_FILE_TYPE_HAS_CHILDREN = 文件类型存在子类型，不允许删除
SYSTEM_FILE_TYPE_HAS_FILE = 文件类型存在文件，不允许删除
SYSTEM_FILE_NOT_EXIST = 文件不存在

YEAR_CANT_BE_GREATER_THAN_CURRENT_YEAR = 年份不能大于当前年


ENTRUST_ORDER_NOT_EXIST = 委托单不存在
ENTRUST_ORDER_STATUS_ERROR = 委托单状态错误
ENTRUST_ORDER_CUSTOMER_INFO_INCOMPLETE = 委托单客户信息不完整
ENTRUST_ORDER_SAMPLE_INFO_INCOMPLETE = 委托单样本信息不完整
ENTRUST_ORDER_ENTRUST_INFO_INCOMPLETE = 委托单委托信息不完整
ENTRUST_ORDER_REPORT_INFO_INCOMPLETE = 委托单报告信息不完整
ENTRUST_ORDER_INVOICE_INFO_INCOMPLETE = 委托单发票信息不完整

STORE_POSITION_NOT_EXIST = 存储位置不存在
STORE_POSITION_PARENT_NOT_EXIST = 父存储位置不存在
STORE_POSITION_NAME_EXISTS = 存储位置名称存在
STORE_POSITION_HAS_CHILDREN = 存储位置存在子位置，不允许删除

DICT_DATA_CATEGORY_NOT_EXIST = 字典分类不存在
DICT_DATA_NOT_EXIST = 字典数据不存在
DICT_DATA_PARENT_NOT_EXIST = 字典数据父类不存在
DICT_DATA_WAS_EXISTS = 字典数据已经存在
DICT_DATA_HAS_CHILDREN = 字典数据存在子类型，不允许删除
DICT_DATA_IS_NOT_VISIBLE = 字典数据不可视完

FILE_RECORD_NOT_EXIST = 文件记录不存在

DETECTION_METHOD_NOT_EXIST = 检测方法不存在
DETECTION_METHOD_BIND_PROJECT = 检测方法已关联检测项目，不允许删除

DETECTION_PROJECT_NOT_EXIST = 检测项目不存在
DETECTION_PROJECT_BIND_ENTRUST_ORDER = 检测项目已关联委托单，不允许删除

EXECUTION_STANDARD_NOT_EXIST = 执行标准不存在

TEMPLATE_FILE_NOT_EXIST = 模板文件不存在

SAMPLE_PLATE_NUM_NOT_CONSISTENT = 样本板号不一致

EQUIPMENT_NOT_EXIST = 设备不存在

REAGENT_NOT_EXIST = 试剂不存在
REAGENT_HAS_SPECIFICATIONS = 试剂存在规格数据，不允许删除
REAGENT_SPECIFICATION_NOT_EXIST = 试剂规格不存在
INVALID_REAGENT_BRAND_DICT = 无效的试剂品牌字典值
INVALID_REAGENT_UNIT_DICT = 无效的试剂计量单位字典值
INVALID_REAGENT_STORAGE_CONDITION_DICT = 无效的试剂存储条件字典值

CONSUMABLE_NOT_EXIST = 耗材不存在
CONSUMABLE_HAS_SPECIFICATIONS = 耗材存在规格数据，不允许删除
CONSUMABLE_SPECIFICATION_NOT_EXIST = 耗材规格不存在
INVALID_CONSUMABLE_BRAND_DICT = 无效的耗材品牌字典值
INVALID_CONSUMABLE_UNIT_DICT = 无效的耗材计量单位字典值
INVALID_CONSUMABLE_STORAGE_CONDITION_DICT = 无效的耗材存储条件字典值
INVALID_CONSUMABLE_MATERIAL_DICT = 无效的耗材材质字典值

FLOW_DEFINITION_NOT_EXIST = 流程不存在