SUCCESS=Operation success
ERROR_OPERATE=Operation failed

#@NotBlank
javax.validation.constraints.NotBlank.message = Field {0} is empty
#@NotNull
javax.validation.constraints.NotNull.message = Field {0} is empty
#@Length
org.hibernate.validator.constraints.Length.message = Field {0} : Invalid field length
javax.validation.constraints.Min.message = field {0}: Invalid value
javax.validation.constraints.password.regexp.message=The password should be at least 6 characters, supporting numbers, letters and special characters except Spaces, and must contain both numbers and upper and lower case letters
NOT_FOUND_HANDLER=Request resource URL does not exist
METHOD_NOT_SUPPORT=Request method error
MEDIA_TYPE_NOT_SUPPORT=Invalid request type
PARAM_LOSE=Missing parameter
PARAM_TYPE_NOT_SUPPORT=Parameter type mismatch


FILE_UPLOAD_ERROR=Failed to upload file
UPLOAD_FILE_CONFIG_ISNULL_ERROR=File type is not configured
UPLOAD_OVER_SIZE=Maximum number of files exceeded
UPLOAD_FILE_SUF_ERROR=Uploaded file type is wrong
UPLOAD_OVER_LIMIT=File size limit exceeded
UPLOAD_NEED_FILE=Please select upload file
FILE_NOT_EXIST=File not exist

DATA_WAS_AUDITING_CANT_EDIT_OR_DELETE = Data was auditing and cannot be edited or deleted

NOT_TOKEN=Session not logged in
INVALID_TOKEN=Invalid token
TOKEN_TIMEOUT=Token has expired
BE_REPLACED=Token has been forcibly logged out
KICK_OUT=Token has been forcibly kicked out
TOKEN_FREEZE=Token has been frozen
NO_PREFIX=Token submitted without the specified prefix


CAPTCHA_NOT_EXIST=Invalid captcha 

NO_PERMISSION=No permission to operate
NO_ROLE=No role to operate


PLA_USER_NOT_EXISTS=Account does not exist
PLA_USER_CAN_NOT_LOGIN=User is not allowed to login
PLA_USER_PWD_ERROR=Password security is too low, please set a 6-12 digit/letter combination password
PLA_USER_PWD_NOTSAME_ERROR=Old password input error
PLA_USER_EMAIL_NOT_EXISTS=Email does not exist
PLA_USER_CAPTCHA_SEND_FREQUENT=Too many verification code sent
PLA_USER_CAPTCHA_NOT_EXIST=Verification code does not exist
PLA_USER_LOGIN_PASSWORD_ERROR=Password error
PLA_USER_EMAIL_BIND_EXISTS=The email you specified is already in use. Please enter new email
PLA_USER_MOBILE_BIND_EXISTS=The phone you specified is already in use. Please enter new phone
PLA_USER_NAME_BIND_EXISTS=User name exists
PLA_USER_EMPLOYEE_ID_EXISTS=Employee id exists

PLA_ROLE_NOT_EXISTS=Permission does not exist
PLA_ROLE_NOT_ADMIN=Non-operating account permissions
ROLE_NAME_EXISTS=Role name already exists
SEND_TEMPLATE_NOT_EXIST=Send template does not exist


DEPT_HAS_CHILDREN=Department has sub-departments and cannot be deleted
DEPT_NOT_EXISTS=Department does not exist


PARENT_CATEGORY_NOT_EXISTS=Parent category does not exist
CATEGORY_NOT_EXISTS=Category does not exist
CATEGORY_HAS_CHILDREN=Category has sub-categories and cannot be deleted

PARENT_TYPE_NOT_EXISTS=Parent type does not exist
TYPE_HAS_CHILDREN=Type has sub-types and cannot be deleted

BUSINESS_NOT_EXISTS=Business does not exist
JSON_CONVERT_EXCEPTION=JSON conversion exception

PASS_TO_PREVIOUS_NODE = Pass to previous node, operation forbidden
REJECT_TO_SUFFIX_NODE = Reject to suffix node, operation forbidden

FLOW_EXCEPTION = Flow exception

DictDataNotExist = DictData does not exist

MENU_HAS_CHILD = Menu has sub-menus and cannot be deleted


MENU_NOT_EXIST = Menu does not exist


SYSTEM_FILE_TYPE_NOT_EXIST = File type does not exist
SYSTEM_FILE_PARENT_TYPE_NOT_EXIST = Parent file type does not exist
SYSTEM_FILE_TYPE_NAME_EXISTS = File type name already exists
SYSTEM_FILE_TYPE_HAS_CHILDREN = File type has sub-types and cannot be deleted
SYSTEM_FILE_TYPE_HAS_FILE = File type has files and cannot be deleted
SYSTEM_FILE_NOT_EXIST = File does not exist

YEAR_CANT_BE_GREATER_THAN_CURRENT_YEAR = Year cannot be greater than current year


ENTRUST_ORDER_NOT_EXIST = Entrust order does not exist
ENTRUST_ORDER_STATUS_ERROR = Entrust order status error
ENTRUST_ORDER_CUSTOMER_INFO_INCOMPLETE = Entrust order customer info incomplete
ENTRUST_ORDER_SAMPLE_INFO_INCOMPLETE = Entrust order sample info incomplete
ENTRUST_ORDER_ENTRUST_INFO_INCOMPLETE = Entrust order entrust info incomplete
ENTRUST_ORDER_REPORT_INFO_INCOMPLETE = Entrust order report info incomplete
ENTRUST_ORDER_INVOICE_INFO_INCOMPLETE = Entrust order invoice info incomplete

STORE_POSITION_NOT_EXIST = Store position does not exist
STORE_POSITION_PARENT_NOT_EXIST = Store position parent does not exist
STORE_POSITION_NAME_EXISTS = Store position name already exists
STORE_POSITION_HAS_CHILDREN = Store position has sub-positions and cannot be deleted

DICT_DATA_CATEGORY_NOT_EXIST = Dict data category does not exist
DICT_DATA_NOT_EXIST = Dict data does not exist
DICT_DATA_PARENT_NOT_EXIST = Dict data parent does not exist
DICT_DATA_WAS_EXISTS = Dict data already exists
DICT_DATA_HAS_CHILDREN = Dict data has sub-data and cannot be deleted
DICT_DATA_IS_NOT_VISIBLE = Dict data is not visible

FILE_RECORD_NOT_EXIST = File record does not exist

DETECTION_METHOD_NOT_EXIST = Detection method does not exist
DETECTION_METHOD_BIND_PROJECT = Detection method is bound to project and cannot be deleted

DETECTION_PROJECT_NOT_EXIST = Detection project does not exist
DETECTION_PROJECT_BIND_ENTRUST_ORDER = Detection project is bound to entrust order and cannot be deleted

EXECUTION_STANDARD_NOT_EXIST = Execution standard does not exist

TEMPLATE_FILE_NOT_EXIST = Template file does not exist

SAMPLE_PLATE_NUM_NOT_CONSISTENT = Sample plate_num inconsistent

EQUIPMENT_NOT_EXIST = Equipment does not exist

FLOW_DEFINITION_NOT_EXIST = Flow definition does not exist

REAGENT_NOT_EXIST = Reagent does not exist
REAGENT_HAS_SPECIFICATIONS = Reagent has specifications and cannot be deleted
REAGENT_SPECIFICATION_NOT_EXIST = Reagent specification does not exist
INVALID_REAGENT_BRAND_DICT = Invalid reagent brand dict
INVALID_REAGENT_UNIT_DICT = Invalid reagent unit dict
INVALID_REAGENT_STORAGE_CONDITION_DICT = Invalid reagent storage condition dict

CONSUMABLE_NOT_EXIST = Consumable does not exist
CONSUMABLE_HAS_SPECIFICATIONS = Consumable has specifications and cannot be deleted
CONSUMABLE_SPECIFICATION_NOT_EXIST = Consumable specification does not exist
INVALID_CONSUMABLE_BRAND_DICT = Invalid consumable brand dict
INVALID_CONSUMABLE_UNIT_DICT = Invalid consumable unit dict
INVALID_CONSUMABLE_STORAGE_CONDITION_DICT = Invalid consumable storage condition dict
INVALID_CONSUMABLE_MATERIAL_DICT = Invalid consumable material dict