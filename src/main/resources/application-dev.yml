server:
  port: 8467

# 数据源配置
spring:
  datasource:
    dynamic:
      primary: master_1 #设置默认的数据源或者数据源组,默认值即为master
      strict: false #严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      datasource:
        master_1:
          url: *************************************************************************************************
          username: root
          password: root20250428QS@@
          driver-class-name: com.mysql.cj.jdbc.Driver
        slave_1:
          url: *************************************************************************************************
          username: root
          password: root20250428QS@@
          driver-class-name: com.mysql.cj.jdbc.Driver
  redis:
    host: ***************
    port: 8379
    password: lanhuredis0527QWE!
    jedis:
      pool:
        max-active: 8
        max-wait: -1
        max-idle: 500
        min-idle: 0
  config:
    import: binlog4j.yml



log:
  path: logs
  print:
    request: true
    response: false

oss:
  accessKeyId:
  accessKeySecret:
  endPoint:

  #测试地址 外网
  internalEndPoint:

  bucketName:
  domain:
  imageUrl:



token:
  period: 5184000000
  secret: 25f9e794323b4525f9e794323b4525f9e794323b45
  #过期时间
  expire: 1800
  #续签时间
  renew: 300

pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql


minio:
  access-key: B6aRw9mjVYQesHNZclGH
  secret-key: Tsb8u5RZyPgxvkQtnMBJ5qsoMWPYWQLo6HYPlZeq
  end-point: http://192.1683.63:9000
  bucket-name: public


# 滑块验证码配置， 详细请看 cloud.tianai.captcha.autoconfiguration.ImageCaptchaProperties 类
captcha:
  # 如果项目中使用到了redis，滑块验证码会自动把验证码数据存到redis中， 这里配置redis的key的前缀,默认是captcha:slider
  prefix: captcha
  # 验证码过期时间，默认是2分钟,单位毫秒， 可以根据自身业务进行调整
  expire:
    # 默认缓存时间 2分钟
    default: 30000
    # 针对 点选验证码 过期时间设置为 2分钟， 因为点选验证码验证比较慢，把过期时间调整大一些
    WORD_IMAGE_CLICK: 60000
  # 使用加载系统自带的资源， 默认是 false(这里系统的默认资源包含 滑动验证码模板/旋转验证码模板,如果想使用系统的模板，这里设置为true)
  init-default-resource: true
  # 缓存控制， 默认为false不开启
  local-cache-enabled: true
  # 缓存开启后，验证码会提前缓存一些生成好的验证数据， 默认是20
  local-cache-size: 20
  # 缓存开启后，缓存拉取失败后等待时间 默认是 5秒钟
  local-cache-wait-time: 5000
  # 缓存开启后，缓存检查间隔 默认是2秒钟
  local-cache-period: 2000
  secondary:
    # 二次验证， 默认false 不开启
    enabled: false
    # 二次验证过期时间， 默认 2分钟
    expire: 120000
    # 二次验证缓存key前缀，默认是 captcha:secondary
    keyPrefix: "captcha:secondary"
