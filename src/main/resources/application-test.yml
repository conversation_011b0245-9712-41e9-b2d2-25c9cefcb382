server:
  port: 5467

# 数据源配置
spring:
  datasource:
    dynamic:
      primary: master_1 #设置默认的数据源或者数据源组,默认值即为master
      strict: false #严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      datasource:
        master_1:
          url: ***********************************************************************************************
          username: lims
          password: lims20250428QS!@
          driver-class-name: com.mysql.cj.jdbc.Driver
        slave_1:
          url: ***********************************************************************************************
          username: lims
          password: lims20250428QS!@
          driver-class-name: com.mysql.cj.jdbc.Driver


log:
  path: logs
  print:
    request: true
    response: false

oss:
  accessKeyId:
  accessKeySecret:
  endPoint:

  #测试地址 外网
  internalEndPoint:

  bucketName:
  domain:
  imageUrl:


token:
  period: 5184000000
  secret: 11121212sdsdsasdadssssssssssssserrere