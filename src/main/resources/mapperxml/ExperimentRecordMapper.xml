<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanhu.lims.gateway.admin.mapper.ExperimentRecordMapper">
    <resultMap id="BaseResultMap" type="com.lanhu.lims.gateway.admin.model.ExperimentRecord">
        <!--@mbg.generated-->
        <!--@Table t_experiment_record-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="year" jdbcType="INTEGER" property="year"/>
        <result column="week_number" jdbcType="INTEGER" property="weekNumber"/>
        <result column="time_period" jdbcType="VARCHAR" property="timePeriod"/>
        <result column="experiment_content" jdbcType="LONGVARCHAR" property="experimentContent"/>
        <result column="attachment_url" jdbcType="VARCHAR" property="attachmentUrl"/>
        <result column="attachment_name" jdbcType="VARCHAR" property="attachmentName"/>
        <result column="is_effect" jdbcType="INTEGER" property="isEffect"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_name" jdbcType="VARCHAR" property="updateName"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, user_id, user_name, `year`, week_number, time_period, experiment_content, attachment_url,
        attachment_name, is_effect, create_by, create_name, create_time, update_by, update_name,
        update_time
    </sql>
</mapper>