<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanhu.lims.gateway.admin.mapper.RoleMapper">
  <resultMap id="BaseResultMap" type="com.lanhu.lims.gateway.admin.model.Role">
    <!--@mbg.generated-->
    <!--@Table t_role-->
    <id column="role_id" jdbcType="BIGINT" property="roleId" />
    <result column="role_name" jdbcType="VARCHAR" property="roleName" />
    <result column="is_effect" jdbcType="INTEGER" property="isEffect" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="data_scope" jdbcType="INTEGER" property="dataScope" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    role_id, role_name, is_effect, remark, create_time, create_by, update_time, update_by, 
    data_scope
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_role
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="role_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when role_id = #{item.roleId,jdbcType=BIGINT} then #{item.roleName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="is_effect = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when role_id = #{item.roleId,jdbcType=BIGINT} then #{item.isEffect,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when role_id = #{item.roleId,jdbcType=BIGINT} then #{item.remark,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when role_id = #{item.roleId,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when role_id = #{item.roleId,jdbcType=BIGINT} then #{item.createBy,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when role_id = #{item.roleId,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="update_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when role_id = #{item.roleId,jdbcType=BIGINT} then #{item.updateBy,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="data_scope = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when role_id = #{item.roleId,jdbcType=BIGINT} then #{item.dataScope,jdbcType=INTEGER}
        </foreach>
      </trim>
    </trim>
    where role_id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.roleId,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_role
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="role_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.roleName != null">
            when role_id = #{item.roleId,jdbcType=BIGINT} then #{item.roleName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="is_effect = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isEffect != null">
            when role_id = #{item.roleId,jdbcType=BIGINT} then #{item.isEffect,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.remark != null">
            when role_id = #{item.roleId,jdbcType=BIGINT} then #{item.remark,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when role_id = #{item.roleId,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createBy != null">
            when role_id = #{item.roleId,jdbcType=BIGINT} then #{item.createBy,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when role_id = #{item.roleId,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateBy != null">
            when role_id = #{item.roleId,jdbcType=BIGINT} then #{item.updateBy,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="data_scope = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.dataScope != null">
            when role_id = #{item.roleId,jdbcType=BIGINT} then #{item.dataScope,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
    </trim>
    where role_id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.roleId,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into t_role
    (role_id, role_name, is_effect, remark, create_time, create_by, update_time, update_by, 
      data_scope)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.roleId,jdbcType=BIGINT}, #{item.roleName,jdbcType=VARCHAR}, #{item.isEffect,jdbcType=INTEGER}, 
        #{item.remark,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.createBy,jdbcType=VARCHAR}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.updateBy,jdbcType=VARCHAR}, #{item.dataScope,jdbcType=INTEGER}
        )
    </foreach>
  </insert>
  <insert id="batchInsertOrUpdate" parameterType="map">
    <!--@mbg.generated-->
    insert into t_role
    (role_id, role_name, is_effect, remark, create_time, create_by, update_time, update_by, 
      data_scope)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.roleId,jdbcType=BIGINT}, #{item.roleName,jdbcType=VARCHAR}, #{item.isEffect,jdbcType=INTEGER}, 
        #{item.remark,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.createBy,jdbcType=VARCHAR}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.updateBy,jdbcType=VARCHAR}, #{item.dataScope,jdbcType=INTEGER}
        )
    </foreach>
    on duplicate key update 
    role_id=values(role_id),
    role_name=values(role_name),
    is_effect=values(is_effect),
    remark=values(remark),
    create_time=values(create_time),
    create_by=values(create_by),
    update_time=values(update_time),
    update_by=values(update_by),
    data_scope=values(data_scope)
  </insert>
  <insert id="insertOnDuplicateUpdate" parameterType="com.lanhu.lims.gateway.admin.model.Role">
    <!--@mbg.generated-->
    insert into t_role
    (role_id, role_name, is_effect, remark, create_time, create_by, update_time, update_by, 
      data_scope)
    values
    (#{roleId,jdbcType=BIGINT}, #{roleName,jdbcType=VARCHAR}, #{isEffect,jdbcType=INTEGER}, 
      #{remark,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{dataScope,jdbcType=INTEGER}
      )
    on duplicate key update 
    role_id = #{roleId,jdbcType=BIGINT}, 
    role_name = #{roleName,jdbcType=VARCHAR}, 
    is_effect = #{isEffect,jdbcType=INTEGER}, 
    remark = #{remark,jdbcType=VARCHAR}, 
    create_time = #{createTime,jdbcType=TIMESTAMP}, 
    create_by = #{createBy,jdbcType=VARCHAR}, 
    update_time = #{updateTime,jdbcType=TIMESTAMP}, 
    update_by = #{updateBy,jdbcType=VARCHAR}, 
    data_scope = #{dataScope,jdbcType=INTEGER}
  </insert>
  <insert id="insertOnDuplicateUpdateSelective" parameterType="com.lanhu.lims.gateway.admin.model.Role">
    <!--@mbg.generated-->
    insert into t_role
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="roleId != null">
        role_id,
      </if>
      <if test="roleName != null">
        role_name,
      </if>
      <if test="isEffect != null">
        is_effect,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="dataScope != null">
        data_scope,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="roleId != null">
        #{roleId,jdbcType=BIGINT},
      </if>
      <if test="roleName != null">
        #{roleName,jdbcType=VARCHAR},
      </if>
      <if test="isEffect != null">
        #{isEffect,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="dataScope != null">
        #{dataScope,jdbcType=INTEGER},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="roleId != null">
        role_id = #{roleId,jdbcType=BIGINT},
      </if>
      <if test="roleName != null">
        role_name = #{roleName,jdbcType=VARCHAR},
      </if>
      <if test="isEffect != null">
        is_effect = #{isEffect,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="dataScope != null">
        data_scope = #{dataScope,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>





  <select id="selectRoleListByUserId" resultMap="BaseResultMap">
    select
    distinct  rr.role_id,   rr.role_name
    from t_role rr
    left join t_admin_user_role ur on rr.role_id = ur.role_id
    where ur.admin_user_id = #{userId}  AND rr.is_effect = 0
  </select>


  <select id="selectAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_role
    where is_effect = 0
  </select>



  <select id="selectUserRoleList" resultMap="BaseResultMap">
    SELECT u.admin_user_id as user_id ,r.role_id as role_id, r.role_name as role_name
    FROM t_admin_user_role u LEFT JOIN t_role r ON u.role_id = r.role_id

    where 1=1

    <if test="userId != null">
      and u.admin_user_id = #{userId}
    </if>

    and r.is_effect =0


  </select>



</mapper>