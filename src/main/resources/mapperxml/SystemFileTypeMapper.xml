<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanhu.lims.gateway.admin.mapper.SystemFileTypeMapper">
    <resultMap id="BaseResultMap" type="com.lanhu.lims.gateway.admin.model.SystemFileType">
        <!--@mbg.generated-->
        <!--@Table t_system_file_type-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="type_name" jdbcType="VARCHAR" property="typeName"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="order_num" jdbcType="INTEGER" property="orderNum"/>
        <result column="is_effect" jdbcType="INTEGER" property="isEffect"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_name" jdbcType="VARCHAR" property="updateName"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, type_name, parent_id, order_num, is_effect, create_by, create_name, create_time,
        update_by, update_name, update_time
    </sql>
</mapper>