<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanhu.lims.gateway.admin.mapper.MenuMapper">
  <resultMap id="BaseResultMap" type="com.lanhu.lims.gateway.admin.model.Menu">
    <!--@mbg.generated-->
    <!--@Table t_menu-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="menu_name" jdbcType="VARCHAR" property="menuName" />
    <result column="menu_type" jdbcType="INTEGER" property="menuType" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="level" jdbcType="INTEGER" property="level" />
    <result column="ord" jdbcType="INTEGER" property="ord" />
    <result column="is_effect" jdbcType="INTEGER" property="isEffect" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="path" jdbcType="VARCHAR" property="path" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="component_name" jdbcType="VARCHAR" property="componentName" />
    <result column="visible" jdbcType="INTEGER" property="visible" />
    <result column="menu_name_translate" jdbcType="VARCHAR" property="menuNameTranslate" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="perms" jdbcType="VARCHAR" property="perms" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="title" jdbcType="VARCHAR" property="title" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, menu_name, menu_type, parent_id, `level`, ord, is_effect, create_time, update_time, 
    `path`, icon, component_name, visible, menu_name_translate, create_by, update_by, 
    remark, perms, create_name, update_name, title
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_menu
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="menu_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.menuName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="menu_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.menuType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="parent_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.parentId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="`level` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.level,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ord = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.ord,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="is_effect = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.isEffect,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="`path` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.path,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="icon = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.icon,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="component_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.componentName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="visible = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.visible,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="menu_name_translate = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.menuNameTranslate,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createBy,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="update_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateBy,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.remark,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="perms = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.perms,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="update_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="title = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.title,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_menu
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="menu_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.menuName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.menuName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="menu_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.menuType != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.menuType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="parent_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.parentId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.parentId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="`level` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.level != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.level,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ord = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.ord != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.ord,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="is_effect = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isEffect != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.isEffect,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="`path` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.path != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.path,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="icon = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.icon != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.icon,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="component_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.componentName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.componentName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="visible = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.visible != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.visible,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="menu_name_translate = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.menuNameTranslate != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.menuNameTranslate,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createBy,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateBy,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.remark != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.remark,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="perms = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.perms != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.perms,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="title = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.title != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.title,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_menu
    (menu_name, menu_type, parent_id, `level`, ord, is_effect, create_time, update_time, 
      `path`, icon, component_name, visible, menu_name_translate, create_by, update_by, 
      remark, perms, create_name, update_name, title)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.menuName,jdbcType=VARCHAR}, #{item.menuType,jdbcType=INTEGER}, #{item.parentId,jdbcType=BIGINT}, 
        #{item.level,jdbcType=INTEGER}, #{item.ord,jdbcType=INTEGER}, #{item.isEffect,jdbcType=INTEGER}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.path,jdbcType=VARCHAR}, 
        #{item.icon,jdbcType=VARCHAR}, #{item.componentName,jdbcType=VARCHAR}, #{item.visible,jdbcType=INTEGER}, 
        #{item.menuNameTranslate,jdbcType=VARCHAR}, #{item.createBy,jdbcType=BIGINT}, #{item.updateBy,jdbcType=BIGINT}, 
        #{item.remark,jdbcType=VARCHAR}, #{item.perms,jdbcType=VARCHAR}, #{item.createName,jdbcType=VARCHAR}, 
        #{item.updateName,jdbcType=VARCHAR}, #{item.title,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.lanhu.lims.gateway.admin.model.Menu" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_menu
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      menu_name,
      menu_type,
      parent_id,
      `level`,
      ord,
      is_effect,
      create_time,
      update_time,
      `path`,
      icon,
      component_name,
      visible,
      menu_name_translate,
      create_by,
      update_by,
      remark,
      perms,
      create_name,
      update_name,
      title,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{menuName,jdbcType=VARCHAR},
      #{menuType,jdbcType=INTEGER},
      #{parentId,jdbcType=BIGINT},
      #{level,jdbcType=INTEGER},
      #{ord,jdbcType=INTEGER},
      #{isEffect,jdbcType=INTEGER},
      #{createTime,jdbcType=TIMESTAMP},
      #{updateTime,jdbcType=TIMESTAMP},
      #{path,jdbcType=VARCHAR},
      #{icon,jdbcType=VARCHAR},
      #{componentName,jdbcType=VARCHAR},
      #{visible,jdbcType=INTEGER},
      #{menuNameTranslate,jdbcType=VARCHAR},
      #{createBy,jdbcType=BIGINT},
      #{updateBy,jdbcType=BIGINT},
      #{remark,jdbcType=VARCHAR},
      #{perms,jdbcType=VARCHAR},
      #{createName,jdbcType=VARCHAR},
      #{updateName,jdbcType=VARCHAR},
      #{title,jdbcType=VARCHAR},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      menu_name = #{menuName,jdbcType=VARCHAR},
      menu_type = #{menuType,jdbcType=INTEGER},
      parent_id = #{parentId,jdbcType=BIGINT},
      `level` = #{level,jdbcType=INTEGER},
      ord = #{ord,jdbcType=INTEGER},
      is_effect = #{isEffect,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      `path` = #{path,jdbcType=VARCHAR},
      icon = #{icon,jdbcType=VARCHAR},
      component_name = #{componentName,jdbcType=VARCHAR},
      visible = #{visible,jdbcType=INTEGER},
      menu_name_translate = #{menuNameTranslate,jdbcType=VARCHAR},
      create_by = #{createBy,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      remark = #{remark,jdbcType=VARCHAR},
      perms = #{perms,jdbcType=VARCHAR},
      create_name = #{createName,jdbcType=VARCHAR},
      update_name = #{updateName,jdbcType=VARCHAR},
      title = #{title,jdbcType=VARCHAR},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.lanhu.lims.gateway.admin.model.Menu" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_menu
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="menuName != null">
        menu_name,
      </if>
      <if test="menuType != null">
        menu_type,
      </if>
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="level != null">
        `level`,
      </if>
      <if test="ord != null">
        ord,
      </if>
      <if test="isEffect != null">
        is_effect,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="path != null">
        `path`,
      </if>
      <if test="icon != null">
        icon,
      </if>
      <if test="componentName != null">
        component_name,
      </if>
      <if test="visible != null">
        visible,
      </if>
      <if test="menuNameTranslate != null">
        menu_name_translate,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="perms != null">
        perms,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="title != null">
        title,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="menuName != null">
        #{menuName,jdbcType=VARCHAR},
      </if>
      <if test="menuType != null">
        #{menuType,jdbcType=INTEGER},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=BIGINT},
      </if>
      <if test="level != null">
        #{level,jdbcType=INTEGER},
      </if>
      <if test="ord != null">
        #{ord,jdbcType=INTEGER},
      </if>
      <if test="isEffect != null">
        #{isEffect,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="path != null">
        #{path,jdbcType=VARCHAR},
      </if>
      <if test="icon != null">
        #{icon,jdbcType=VARCHAR},
      </if>
      <if test="componentName != null">
        #{componentName,jdbcType=VARCHAR},
      </if>
      <if test="visible != null">
        #{visible,jdbcType=INTEGER},
      </if>
      <if test="menuNameTranslate != null">
        #{menuNameTranslate,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="perms != null">
        #{perms,jdbcType=VARCHAR},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="menuName != null">
        menu_name = #{menuName,jdbcType=VARCHAR},
      </if>
      <if test="menuType != null">
        menu_type = #{menuType,jdbcType=INTEGER},
      </if>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=BIGINT},
      </if>
      <if test="level != null">
        `level` = #{level,jdbcType=INTEGER},
      </if>
      <if test="ord != null">
        ord = #{ord,jdbcType=INTEGER},
      </if>
      <if test="isEffect != null">
        is_effect = #{isEffect,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="path != null">
        `path` = #{path,jdbcType=VARCHAR},
      </if>
      <if test="icon != null">
        icon = #{icon,jdbcType=VARCHAR},
      </if>
      <if test="componentName != null">
        component_name = #{componentName,jdbcType=VARCHAR},
      </if>
      <if test="visible != null">
        visible = #{visible,jdbcType=INTEGER},
      </if>
      <if test="menuNameTranslate != null">
        menu_name_translate = #{menuNameTranslate,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="perms != null">
        perms = #{perms,jdbcType=VARCHAR},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="selectMenuListByUserId" resultMap="BaseResultMap">
    select
    distinct  m.id,   m.menu_name,  m. menu_type,   m.parent_id,   m.`level`,   m.ord,   m.is_effect
    , m.path,m.title, m.icon, m.component_name, m.visible,m.menu_name_translate,m.perms
    from t_menu m
    left join t_role_menu rm on m.id = rm.menu_id
    left join t_admin_user_role ur on rm.role_id = ur.role_id
    where ur.admin_user_id = #{userId}  AND m.is_effect = 0
  </select>


  <select id="selectMenuList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_menu
    where
    is_effect=0
  </select>

</mapper>