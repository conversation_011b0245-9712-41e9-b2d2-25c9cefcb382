<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanhu.lims.gateway.admin.mapper.CaptchaMapper">
  <resultMap id="BaseResultMap" type="com.lanhu.lims.gateway.admin.model.Captcha">
    <!--@mbg.generated-->
    <!--@Table t_captcha-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="uid" jdbcType="VARCHAR" property="uid" />
    <result column="captcha" jdbcType="VARCHAR" property="captcha" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="captcha_mode" jdbcType="INTEGER" property="captchaMode" />
    <result column="captcha_period" jdbcType="INTEGER" property="captchaPeriod" />
    <result column="expire_time" jdbcType="TIMESTAMP" property="expireTime" />
    <result column="use_counter" jdbcType="INTEGER" property="useCounter" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `uid`, captcha, create_at, mobile, email, captcha_mode, captcha_period, expire_time, 
    use_counter
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_captcha
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`uid` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.uid,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="captcha = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.captcha,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_at = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createAt,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="mobile = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.mobile,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="email = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.email,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="captcha_mode = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.captchaMode,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="captcha_period = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.captchaPeriod,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="expire_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.expireTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="use_counter = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.useCounter,jdbcType=INTEGER}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into t_captcha
    (id, `uid`, captcha, create_at, mobile, email, captcha_mode, captcha_period, expire_time, 
      use_counter)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.uid,jdbcType=VARCHAR}, #{item.captcha,jdbcType=VARCHAR}, 
        #{item.createAt,jdbcType=TIMESTAMP}, #{item.mobile,jdbcType=VARCHAR}, #{item.email,jdbcType=VARCHAR}, 
        #{item.captchaMode,jdbcType=INTEGER}, #{item.captchaPeriod,jdbcType=INTEGER}, #{item.expireTime,jdbcType=TIMESTAMP}, 
        #{item.useCounter,jdbcType=INTEGER})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" parameterType="com.lanhu.lims.gateway.admin.model.Captcha">
    <!--@mbg.generated-->
    insert into t_captcha
    (id, `uid`, captcha, create_at, mobile, email, captcha_mode, captcha_period, expire_time, 
      use_counter)
    values
    (#{id,jdbcType=BIGINT}, #{uid,jdbcType=VARCHAR}, #{captcha,jdbcType=VARCHAR}, #{createAt,jdbcType=TIMESTAMP}, 
      #{mobile,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, #{captchaMode,jdbcType=INTEGER}, 
      #{captchaPeriod,jdbcType=INTEGER}, #{expireTime,jdbcType=TIMESTAMP}, #{useCounter,jdbcType=INTEGER}
      )
    on duplicate key update 
    id = #{id,jdbcType=BIGINT}, 
    `uid` = #{uid,jdbcType=VARCHAR}, 
    captcha = #{captcha,jdbcType=VARCHAR}, 
    create_at = #{createAt,jdbcType=TIMESTAMP}, 
    mobile = #{mobile,jdbcType=VARCHAR}, 
    email = #{email,jdbcType=VARCHAR}, 
    captcha_mode = #{captchaMode,jdbcType=INTEGER}, 
    captcha_period = #{captchaPeriod,jdbcType=INTEGER}, 
    expire_time = #{expireTime,jdbcType=TIMESTAMP}, 
    use_counter = #{useCounter,jdbcType=INTEGER}
  </insert>
  <insert id="insertOrUpdateSelective" parameterType="com.lanhu.lims.gateway.admin.model.Captcha">
    <!--@mbg.generated-->
    insert into t_captcha
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="uid != null">
        `uid`,
      </if>
      <if test="captcha != null">
        captcha,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="captchaMode != null">
        captcha_mode,
      </if>
      <if test="captchaPeriod != null">
        captcha_period,
      </if>
      <if test="expireTime != null">
        expire_time,
      </if>
      <if test="useCounter != null">
        use_counter,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="uid != null">
        #{uid,jdbcType=VARCHAR},
      </if>
      <if test="captcha != null">
        #{captcha,jdbcType=VARCHAR},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="captchaMode != null">
        #{captchaMode,jdbcType=INTEGER},
      </if>
      <if test="captchaPeriod != null">
        #{captchaPeriod,jdbcType=INTEGER},
      </if>
      <if test="expireTime != null">
        #{expireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="useCounter != null">
        #{useCounter,jdbcType=INTEGER},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="uid != null">
        `uid` = #{uid,jdbcType=VARCHAR},
      </if>
      <if test="captcha != null">
        captcha = #{captcha,jdbcType=VARCHAR},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="mobile != null">
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="captchaMode != null">
        captcha_mode = #{captchaMode,jdbcType=INTEGER},
      </if>
      <if test="captchaPeriod != null">
        captcha_period = #{captchaPeriod,jdbcType=INTEGER},
      </if>
      <if test="expireTime != null">
        expire_time = #{expireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="useCounter != null">
        use_counter = #{useCounter,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
</mapper>