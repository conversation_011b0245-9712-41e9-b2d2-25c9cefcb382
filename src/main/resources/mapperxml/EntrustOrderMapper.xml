<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanhu.lims.gateway.admin.mapper.EntrustOrderMapper">
  <resultMap id="BaseResultMap" type="com.lanhu.lims.gateway.admin.model.EntrustOrder">
    <!--@mbg.generated-->
    <!--@Table t_entrust_order-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="entrust_order_no" jdbcType="VARCHAR" property="entrustOrderNo" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="customer_id" jdbcType="BIGINT" property="customerId" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="contact_mobile" jdbcType="VARCHAR" property="contactMobile" />
    <result column="contact_name" jdbcType="VARCHAR" property="contactName" />
    <result column="contact_email" jdbcType="VARCHAR" property="contactEmail" />
    <result column="customer_address" jdbcType="VARCHAR" property="customerAddress" />
    <result column="require_desc" jdbcType="LONGVARCHAR" property="requireDesc" />
    <result column="is_effect" jdbcType="INTEGER" property="isEffect" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="order_amount" jdbcType="DECIMAL" property="orderAmount" />
    <result column="benchmark_amount" jdbcType="DECIMAL" property="benchmarkAmount" />
    <result column="delivery_mode" jdbcType="INTEGER" property="deliveryMode" />
    <result column="plan_complete_days" jdbcType="INTEGER" property="planCompleteDays" />
    <result column="plan_complete_time" jdbcType="TIMESTAMP" property="planCompleteTime" />
    <result column="inspect_item" jdbcType="VARCHAR" property="inspectItem" />
    <result column="report_format" jdbcType="INTEGER" property="reportFormat" />
    <result column="report_amount" jdbcType="INTEGER" property="reportAmount" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="close_time" jdbcType="TIMESTAMP" property="closeTime" />
    <result column="complete_by" jdbcType="BIGINT" property="completeBy" />
    <result column="complete_name" jdbcType="VARCHAR" property="completeName" />
    <result column="close_by" jdbcType="BIGINT" property="closeBy" />
    <result column="close_name" jdbcType="VARCHAR" property="closeName" />
    <result column="sample_amount" jdbcType="INTEGER" property="sampleAmount" />
    <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime" />
    <result column="audit_by" jdbcType="BIGINT" property="auditBy" />
    <result column="audit_remark" jdbcType="LONGVARCHAR" property="auditRemark" />
    <result column="audit_name" jdbcType="VARCHAR" property="auditName" />
    <result column="audit_status" jdbcType="INTEGER" property="auditStatus" />
    <result column="flow_instance_id" jdbcType="BIGINT" property="flowInstanceId" />
    <result column="close_reason" jdbcType="VARCHAR" property="closeReason" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, entrust_order_no, `status`, customer_id, customer_name, contact_mobile, contact_name, 
    contact_email, customer_address, require_desc, is_effect, create_by, create_name, 
    create_time, update_by, update_name, update_time, order_amount, benchmark_amount, 
    delivery_mode, plan_complete_days, plan_complete_time, inspect_item, report_format, 
    report_amount, remark, complete_time, close_time, complete_by, complete_name, close_by, 
    close_name, sample_amount, audit_time, audit_by, audit_remark, audit_name, audit_status, 
    flow_instance_id, close_reason
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_entrust_order
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="entrust_order_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.entrustOrderNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.status,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="customer_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.customerId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="customer_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.customerName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="contact_mobile = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.contactMobile,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="contact_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.contactName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="contact_email = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.contactEmail,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="customer_address = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.customerAddress,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="require_desc = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.requireDesc,jdbcType=LONGVARCHAR}
        </foreach>
      </trim>
      <trim prefix="is_effect = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.isEffect,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createBy,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="create_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="update_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateBy,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="update_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="order_amount = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.orderAmount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="benchmark_amount = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.benchmarkAmount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="delivery_mode = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.deliveryMode,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="plan_complete_days = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.planCompleteDays,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="plan_complete_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.planCompleteTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="inspect_item = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.inspectItem,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="report_format = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.reportFormat,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="report_amount = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.reportAmount,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.remark,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="complete_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.completeTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="close_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.closeTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="complete_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.completeBy,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="complete_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.completeName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="close_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.closeBy,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="close_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.closeName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="sample_amount = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.sampleAmount,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="audit_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.auditTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="audit_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.auditBy,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="audit_remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.auditRemark,jdbcType=LONGVARCHAR}
        </foreach>
      </trim>
      <trim prefix="audit_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.auditName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="audit_status = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.auditStatus,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="flow_instance_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.flowInstanceId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="close_reason = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.closeReason,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_entrust_order
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="entrust_order_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.entrustOrderNo != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.entrustOrderNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.status != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.status,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="customer_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.customerId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.customerId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="customer_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.customerName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.customerName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="contact_mobile = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.contactMobile != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.contactMobile,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="contact_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.contactName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.contactName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="contact_email = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.contactEmail != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.contactEmail,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="customer_address = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.customerAddress != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.customerAddress,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="require_desc = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.requireDesc != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.requireDesc,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="is_effect = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isEffect != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.isEffect,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createBy,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateBy,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="order_amount = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orderAmount != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.orderAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="benchmark_amount = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.benchmarkAmount != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.benchmarkAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="delivery_mode = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deliveryMode != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.deliveryMode,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="plan_complete_days = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.planCompleteDays != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.planCompleteDays,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="plan_complete_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.planCompleteTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.planCompleteTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="inspect_item = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.inspectItem != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.inspectItem,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="report_format = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.reportFormat != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.reportFormat,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="report_amount = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.reportAmount != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.reportAmount,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.remark != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.remark,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="complete_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.completeTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.completeTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="close_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.closeTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.closeTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="complete_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.completeBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.completeBy,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="complete_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.completeName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.completeName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="close_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.closeBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.closeBy,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="close_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.closeName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.closeName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="sample_amount = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.sampleAmount != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.sampleAmount,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="audit_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.auditTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.auditTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="audit_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.auditBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.auditBy,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="audit_remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.auditRemark != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.auditRemark,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="audit_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.auditName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.auditName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="audit_status = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.auditStatus != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.auditStatus,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="flow_instance_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.flowInstanceId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.flowInstanceId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="close_reason = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.closeReason != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.closeReason,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into t_entrust_order
    (id, entrust_order_no, `status`, customer_id, customer_name, contact_mobile, contact_name, 
      contact_email, customer_address, require_desc, is_effect, create_by, create_name, 
      create_time, update_by, update_name, update_time, order_amount, benchmark_amount, 
      delivery_mode, plan_complete_days, plan_complete_time, inspect_item, report_format, 
      report_amount, remark, complete_time, close_time, complete_by, complete_name, close_by, 
      close_name, sample_amount, audit_time, audit_by, audit_remark, audit_name, audit_status, 
      flow_instance_id, close_reason)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.entrustOrderNo,jdbcType=VARCHAR}, #{item.status,jdbcType=INTEGER}, 
        #{item.customerId,jdbcType=BIGINT}, #{item.customerName,jdbcType=VARCHAR}, #{item.contactMobile,jdbcType=VARCHAR}, 
        #{item.contactName,jdbcType=VARCHAR}, #{item.contactEmail,jdbcType=VARCHAR}, #{item.customerAddress,jdbcType=VARCHAR}, 
        #{item.requireDesc,jdbcType=LONGVARCHAR}, #{item.isEffect,jdbcType=INTEGER}, #{item.createBy,jdbcType=BIGINT}, 
        #{item.createName,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateBy,jdbcType=BIGINT}, 
        #{item.updateName,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.orderAmount,jdbcType=DECIMAL}, 
        #{item.benchmarkAmount,jdbcType=DECIMAL}, #{item.deliveryMode,jdbcType=INTEGER}, 
        #{item.planCompleteDays,jdbcType=INTEGER}, #{item.planCompleteTime,jdbcType=TIMESTAMP}, 
        #{item.inspectItem,jdbcType=VARCHAR}, #{item.reportFormat,jdbcType=INTEGER}, #{item.reportAmount,jdbcType=INTEGER}, 
        #{item.remark,jdbcType=VARCHAR}, #{item.completeTime,jdbcType=TIMESTAMP}, #{item.closeTime,jdbcType=TIMESTAMP}, 
        #{item.completeBy,jdbcType=BIGINT}, #{item.completeName,jdbcType=VARCHAR}, #{item.closeBy,jdbcType=BIGINT}, 
        #{item.closeName,jdbcType=VARCHAR}, #{item.sampleAmount,jdbcType=INTEGER}, #{item.auditTime,jdbcType=TIMESTAMP}, 
        #{item.auditBy,jdbcType=BIGINT}, #{item.auditRemark,jdbcType=LONGVARCHAR}, #{item.auditName,jdbcType=VARCHAR}, 
        #{item.auditStatus,jdbcType=INTEGER}, #{item.flowInstanceId,jdbcType=BIGINT}, #{item.closeReason,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertOrUpdate" parameterType="map">
    <!--@mbg.generated-->
    insert into t_entrust_order
    (id, entrust_order_no, `status`, customer_id, customer_name, contact_mobile, contact_name, 
      contact_email, customer_address, require_desc, is_effect, create_by, create_name, 
      create_time, update_by, update_name, update_time, order_amount, benchmark_amount, 
      delivery_mode, plan_complete_days, plan_complete_time, inspect_item, report_format, 
      report_amount, remark, complete_time, close_time, complete_by, complete_name, close_by, 
      close_name, sample_amount, audit_time, audit_by, audit_remark, audit_name, audit_status, 
      flow_instance_id, close_reason)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.entrustOrderNo,jdbcType=VARCHAR}, #{item.status,jdbcType=INTEGER}, 
        #{item.customerId,jdbcType=BIGINT}, #{item.customerName,jdbcType=VARCHAR}, #{item.contactMobile,jdbcType=VARCHAR}, 
        #{item.contactName,jdbcType=VARCHAR}, #{item.contactEmail,jdbcType=VARCHAR}, #{item.customerAddress,jdbcType=VARCHAR}, 
        #{item.requireDesc,jdbcType=LONGVARCHAR}, #{item.isEffect,jdbcType=INTEGER}, #{item.createBy,jdbcType=BIGINT}, 
        #{item.createName,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateBy,jdbcType=BIGINT}, 
        #{item.updateName,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.orderAmount,jdbcType=DECIMAL}, 
        #{item.benchmarkAmount,jdbcType=DECIMAL}, #{item.deliveryMode,jdbcType=INTEGER}, 
        #{item.planCompleteDays,jdbcType=INTEGER}, #{item.planCompleteTime,jdbcType=TIMESTAMP}, 
        #{item.inspectItem,jdbcType=VARCHAR}, #{item.reportFormat,jdbcType=INTEGER}, #{item.reportAmount,jdbcType=INTEGER}, 
        #{item.remark,jdbcType=VARCHAR}, #{item.completeTime,jdbcType=TIMESTAMP}, #{item.closeTime,jdbcType=TIMESTAMP}, 
        #{item.completeBy,jdbcType=BIGINT}, #{item.completeName,jdbcType=VARCHAR}, #{item.closeBy,jdbcType=BIGINT}, 
        #{item.closeName,jdbcType=VARCHAR}, #{item.sampleAmount,jdbcType=INTEGER}, #{item.auditTime,jdbcType=TIMESTAMP}, 
        #{item.auditBy,jdbcType=BIGINT}, #{item.auditRemark,jdbcType=LONGVARCHAR}, #{item.auditName,jdbcType=VARCHAR}, 
        #{item.auditStatus,jdbcType=INTEGER}, #{item.flowInstanceId,jdbcType=BIGINT}, #{item.closeReason,jdbcType=VARCHAR}
        )
    </foreach>
    on duplicate key update 
    id=values(id),
    entrust_order_no=values(entrust_order_no),
    status=values(status),
    customer_id=values(customer_id),
    customer_name=values(customer_name),
    contact_mobile=values(contact_mobile),
    contact_name=values(contact_name),
    contact_email=values(contact_email),
    customer_address=values(customer_address),
    require_desc=values(require_desc),
    is_effect=values(is_effect),
    create_by=values(create_by),
    create_name=values(create_name),
    create_time=values(create_time),
    update_by=values(update_by),
    update_name=values(update_name),
    update_time=values(update_time),
    order_amount=values(order_amount),
    benchmark_amount=values(benchmark_amount),
    delivery_mode=values(delivery_mode),
    plan_complete_days=values(plan_complete_days),
    plan_complete_time=values(plan_complete_time),
    inspect_item=values(inspect_item),
    report_format=values(report_format),
    report_amount=values(report_amount),
    remark=values(remark),
    complete_time=values(complete_time),
    close_time=values(close_time),
    complete_by=values(complete_by),
    complete_name=values(complete_name),
    close_by=values(close_by),
    close_name=values(close_name),
    sample_amount=values(sample_amount),
    audit_time=values(audit_time),
    audit_by=values(audit_by),
    audit_remark=values(audit_remark),
    audit_name=values(audit_name),
    audit_status=values(audit_status),
    flow_instance_id=values(flow_instance_id),
    close_reason=values(close_reason)
  </insert>
  <insert id="insertOnDuplicateUpdate" parameterType="com.lanhu.lims.gateway.admin.model.EntrustOrder">
    <!--@mbg.generated-->
    insert into t_entrust_order
    (id, entrust_order_no, `status`, customer_id, customer_name, contact_mobile, contact_name, 
      contact_email, customer_address, require_desc, is_effect, create_by, create_name, 
      create_time, update_by, update_name, update_time, order_amount, benchmark_amount, 
      delivery_mode, plan_complete_days, plan_complete_time, inspect_item, report_format, 
      report_amount, remark, complete_time, close_time, complete_by, complete_name, close_by, 
      close_name, sample_amount, audit_time, audit_by, audit_remark, audit_name, audit_status, 
      flow_instance_id, close_reason)
    values
    (#{id,jdbcType=BIGINT}, #{entrustOrderNo,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{customerId,jdbcType=BIGINT}, #{customerName,jdbcType=VARCHAR}, #{contactMobile,jdbcType=VARCHAR}, 
      #{contactName,jdbcType=VARCHAR}, #{contactEmail,jdbcType=VARCHAR}, #{customerAddress,jdbcType=VARCHAR}, 
      #{requireDesc,jdbcType=LONGVARCHAR}, #{isEffect,jdbcType=INTEGER}, #{createBy,jdbcType=BIGINT}, 
      #{createName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=BIGINT}, 
      #{updateName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{orderAmount,jdbcType=DECIMAL}, 
      #{benchmarkAmount,jdbcType=DECIMAL}, #{deliveryMode,jdbcType=INTEGER}, #{planCompleteDays,jdbcType=INTEGER}, 
      #{planCompleteTime,jdbcType=TIMESTAMP}, #{inspectItem,jdbcType=VARCHAR}, #{reportFormat,jdbcType=INTEGER}, 
      #{reportAmount,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, #{completeTime,jdbcType=TIMESTAMP}, 
      #{closeTime,jdbcType=TIMESTAMP}, #{completeBy,jdbcType=BIGINT}, #{completeName,jdbcType=VARCHAR}, 
      #{closeBy,jdbcType=BIGINT}, #{closeName,jdbcType=VARCHAR}, #{sampleAmount,jdbcType=INTEGER}, 
      #{auditTime,jdbcType=TIMESTAMP}, #{auditBy,jdbcType=BIGINT}, #{auditRemark,jdbcType=LONGVARCHAR}, 
      #{auditName,jdbcType=VARCHAR}, #{auditStatus,jdbcType=INTEGER}, #{flowInstanceId,jdbcType=BIGINT}, 
      #{closeReason,jdbcType=VARCHAR})
    on duplicate key update 
    id = #{id,jdbcType=BIGINT}, 
    entrust_order_no = #{entrustOrderNo,jdbcType=VARCHAR}, 
    `status` = #{status,jdbcType=INTEGER}, 
    customer_id = #{customerId,jdbcType=BIGINT}, 
    customer_name = #{customerName,jdbcType=VARCHAR}, 
    contact_mobile = #{contactMobile,jdbcType=VARCHAR}, 
    contact_name = #{contactName,jdbcType=VARCHAR}, 
    contact_email = #{contactEmail,jdbcType=VARCHAR}, 
    customer_address = #{customerAddress,jdbcType=VARCHAR}, 
    require_desc = #{requireDesc,jdbcType=LONGVARCHAR}, 
    is_effect = #{isEffect,jdbcType=INTEGER}, 
    create_by = #{createBy,jdbcType=BIGINT}, 
    create_name = #{createName,jdbcType=VARCHAR}, 
    create_time = #{createTime,jdbcType=TIMESTAMP}, 
    update_by = #{updateBy,jdbcType=BIGINT}, 
    update_name = #{updateName,jdbcType=VARCHAR}, 
    update_time = #{updateTime,jdbcType=TIMESTAMP}, 
    order_amount = #{orderAmount,jdbcType=DECIMAL}, 
    benchmark_amount = #{benchmarkAmount,jdbcType=DECIMAL}, 
    delivery_mode = #{deliveryMode,jdbcType=INTEGER}, 
    plan_complete_days = #{planCompleteDays,jdbcType=INTEGER}, 
    plan_complete_time = #{planCompleteTime,jdbcType=TIMESTAMP}, 
    inspect_item = #{inspectItem,jdbcType=VARCHAR}, 
    report_format = #{reportFormat,jdbcType=INTEGER}, 
    report_amount = #{reportAmount,jdbcType=INTEGER}, 
    remark = #{remark,jdbcType=VARCHAR}, 
    complete_time = #{completeTime,jdbcType=TIMESTAMP}, 
    close_time = #{closeTime,jdbcType=TIMESTAMP}, 
    complete_by = #{completeBy,jdbcType=BIGINT}, 
    complete_name = #{completeName,jdbcType=VARCHAR}, 
    close_by = #{closeBy,jdbcType=BIGINT}, 
    close_name = #{closeName,jdbcType=VARCHAR}, 
    sample_amount = #{sampleAmount,jdbcType=INTEGER}, 
    audit_time = #{auditTime,jdbcType=TIMESTAMP}, 
    audit_by = #{auditBy,jdbcType=BIGINT}, 
    audit_remark = #{auditRemark,jdbcType=LONGVARCHAR}, 
    audit_name = #{auditName,jdbcType=VARCHAR}, 
    audit_status = #{auditStatus,jdbcType=INTEGER}, 
    flow_instance_id = #{flowInstanceId,jdbcType=BIGINT}, 
    close_reason = #{closeReason,jdbcType=VARCHAR}
  </insert>
  <insert id="insertOnDuplicateUpdateSelective" parameterType="com.lanhu.lims.gateway.admin.model.EntrustOrder">
    <!--@mbg.generated-->
    insert into t_entrust_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="entrustOrderNo != null">
        entrust_order_no,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="contactMobile != null">
        contact_mobile,
      </if>
      <if test="contactName != null">
        contact_name,
      </if>
      <if test="contactEmail != null">
        contact_email,
      </if>
      <if test="customerAddress != null">
        customer_address,
      </if>
      <if test="requireDesc != null">
        require_desc,
      </if>
      <if test="isEffect != null">
        is_effect,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="orderAmount != null">
        order_amount,
      </if>
      <if test="benchmarkAmount != null">
        benchmark_amount,
      </if>
      <if test="deliveryMode != null">
        delivery_mode,
      </if>
      <if test="planCompleteDays != null">
        plan_complete_days,
      </if>
      <if test="planCompleteTime != null">
        plan_complete_time,
      </if>
      <if test="inspectItem != null">
        inspect_item,
      </if>
      <if test="reportFormat != null">
        report_format,
      </if>
      <if test="reportAmount != null">
        report_amount,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="completeTime != null">
        complete_time,
      </if>
      <if test="closeTime != null">
        close_time,
      </if>
      <if test="completeBy != null">
        complete_by,
      </if>
      <if test="completeName != null">
        complete_name,
      </if>
      <if test="closeBy != null">
        close_by,
      </if>
      <if test="closeName != null">
        close_name,
      </if>
      <if test="sampleAmount != null">
        sample_amount,
      </if>
      <if test="auditTime != null">
        audit_time,
      </if>
      <if test="auditBy != null">
        audit_by,
      </if>
      <if test="auditRemark != null">
        audit_remark,
      </if>
      <if test="auditName != null">
        audit_name,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="flowInstanceId != null">
        flow_instance_id,
      </if>
      <if test="closeReason != null">
        close_reason,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="entrustOrderNo != null">
        #{entrustOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=BIGINT},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="contactMobile != null">
        #{contactMobile,jdbcType=VARCHAR},
      </if>
      <if test="contactName != null">
        #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactEmail != null">
        #{contactEmail,jdbcType=VARCHAR},
      </if>
      <if test="customerAddress != null">
        #{customerAddress,jdbcType=VARCHAR},
      </if>
      <if test="requireDesc != null">
        #{requireDesc,jdbcType=LONGVARCHAR},
      </if>
      <if test="isEffect != null">
        #{isEffect,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderAmount != null">
        #{orderAmount,jdbcType=DECIMAL},
      </if>
      <if test="benchmarkAmount != null">
        #{benchmarkAmount,jdbcType=DECIMAL},
      </if>
      <if test="deliveryMode != null">
        #{deliveryMode,jdbcType=INTEGER},
      </if>
      <if test="planCompleteDays != null">
        #{planCompleteDays,jdbcType=INTEGER},
      </if>
      <if test="planCompleteTime != null">
        #{planCompleteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="inspectItem != null">
        #{inspectItem,jdbcType=VARCHAR},
      </if>
      <if test="reportFormat != null">
        #{reportFormat,jdbcType=INTEGER},
      </if>
      <if test="reportAmount != null">
        #{reportAmount,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="completeTime != null">
        #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="closeTime != null">
        #{closeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="completeBy != null">
        #{completeBy,jdbcType=BIGINT},
      </if>
      <if test="completeName != null">
        #{completeName,jdbcType=VARCHAR},
      </if>
      <if test="closeBy != null">
        #{closeBy,jdbcType=BIGINT},
      </if>
      <if test="closeName != null">
        #{closeName,jdbcType=VARCHAR},
      </if>
      <if test="sampleAmount != null">
        #{sampleAmount,jdbcType=INTEGER},
      </if>
      <if test="auditTime != null">
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditBy != null">
        #{auditBy,jdbcType=BIGINT},
      </if>
      <if test="auditRemark != null">
        #{auditRemark,jdbcType=LONGVARCHAR},
      </if>
      <if test="auditName != null">
        #{auditName,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="flowInstanceId != null">
        #{flowInstanceId,jdbcType=BIGINT},
      </if>
      <if test="closeReason != null">
        #{closeReason,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="entrustOrderNo != null">
        entrust_order_no = #{entrustOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=BIGINT},
      </if>
      <if test="customerName != null">
        customer_name = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="contactMobile != null">
        contact_mobile = #{contactMobile,jdbcType=VARCHAR},
      </if>
      <if test="contactName != null">
        contact_name = #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactEmail != null">
        contact_email = #{contactEmail,jdbcType=VARCHAR},
      </if>
      <if test="customerAddress != null">
        customer_address = #{customerAddress,jdbcType=VARCHAR},
      </if>
      <if test="requireDesc != null">
        require_desc = #{requireDesc,jdbcType=LONGVARCHAR},
      </if>
      <if test="isEffect != null">
        is_effect = #{isEffect,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderAmount != null">
        order_amount = #{orderAmount,jdbcType=DECIMAL},
      </if>
      <if test="benchmarkAmount != null">
        benchmark_amount = #{benchmarkAmount,jdbcType=DECIMAL},
      </if>
      <if test="deliveryMode != null">
        delivery_mode = #{deliveryMode,jdbcType=INTEGER},
      </if>
      <if test="planCompleteDays != null">
        plan_complete_days = #{planCompleteDays,jdbcType=INTEGER},
      </if>
      <if test="planCompleteTime != null">
        plan_complete_time = #{planCompleteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="inspectItem != null">
        inspect_item = #{inspectItem,jdbcType=VARCHAR},
      </if>
      <if test="reportFormat != null">
        report_format = #{reportFormat,jdbcType=INTEGER},
      </if>
      <if test="reportAmount != null">
        report_amount = #{reportAmount,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="completeTime != null">
        complete_time = #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="closeTime != null">
        close_time = #{closeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="completeBy != null">
        complete_by = #{completeBy,jdbcType=BIGINT},
      </if>
      <if test="completeName != null">
        complete_name = #{completeName,jdbcType=VARCHAR},
      </if>
      <if test="closeBy != null">
        close_by = #{closeBy,jdbcType=BIGINT},
      </if>
      <if test="closeName != null">
        close_name = #{closeName,jdbcType=VARCHAR},
      </if>
      <if test="sampleAmount != null">
        sample_amount = #{sampleAmount,jdbcType=INTEGER},
      </if>
      <if test="auditTime != null">
        audit_time = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditBy != null">
        audit_by = #{auditBy,jdbcType=BIGINT},
      </if>
      <if test="auditRemark != null">
        audit_remark = #{auditRemark,jdbcType=LONGVARCHAR},
      </if>
      <if test="auditName != null">
        audit_name = #{auditName,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="flowInstanceId != null">
        flow_instance_id = #{flowInstanceId,jdbcType=BIGINT},
      </if>
      <if test="closeReason != null">
        close_reason = #{closeReason,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>