<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanhu.lims.gateway.admin.mapper.AdminUserRoleMapper">
  <resultMap id="BaseResultMap" type="com.lanhu.lims.gateway.admin.model.AdminUserRole">
    <!--@mbg.generated-->
    <!--@Table t_admin_user_role-->
    <result column="admin_user_id" jdbcType="BIGINT" property="adminUserId" />
    <result column="role_id" jdbcType="BIGINT" property="roleId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    admin_user_id, role_id
  </sql>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into t_admin_user_role
    (admin_user_id, role_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.adminUserId,jdbcType=BIGINT}, #{item.roleId,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertOrUpdate" parameterType="map">
    <!--@mbg.generated-->
    insert into t_admin_user_role
    (admin_user_id, role_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.adminUserId,jdbcType=BIGINT}, #{item.roleId,jdbcType=BIGINT})
    </foreach>
    on duplicate key update 
    admin_user_id=values(admin_user_id),
    role_id=values(role_id)
  </insert>
  <insert id="insertOnDuplicateUpdate" parameterType="com.lanhu.lims.gateway.admin.model.AdminUserRole">
    <!--@mbg.generated-->
    insert into t_admin_user_role
    (admin_user_id, role_id)
    values
    (#{adminUserId,jdbcType=BIGINT}, #{roleId,jdbcType=BIGINT})
    on duplicate key update 
    admin_user_id = #{adminUserId,jdbcType=BIGINT}, 
    role_id = #{roleId,jdbcType=BIGINT}
  </insert>
  <insert id="insertOnDuplicateUpdateSelective" parameterType="com.lanhu.lims.gateway.admin.model.AdminUserRole">
    <!--@mbg.generated-->
    insert into t_admin_user_role
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="adminUserId != null">
        admin_user_id,
      </if>
      <if test="roleId != null">
        role_id,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="adminUserId != null">
        #{adminUserId,jdbcType=BIGINT},
      </if>
      <if test="roleId != null">
        #{roleId,jdbcType=BIGINT},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="adminUserId != null">
        admin_user_id = #{adminUserId,jdbcType=BIGINT},
      </if>
      <if test="roleId != null">
        role_id = #{roleId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>

  <delete id="deleteByUserId">
    delete from t_admin_user_role
    where
    admin_user_id = #{userId,jdbcType=VARCHAR}
  </delete>
</mapper>