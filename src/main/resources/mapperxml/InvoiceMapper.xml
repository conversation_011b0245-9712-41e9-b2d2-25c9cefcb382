<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanhu.lims.gateway.admin.mapper.InvoiceMapper">
  <resultMap id="BaseResultMap" type="com.lanhu.lims.gateway.admin.model.Invoice">
    <!--@mbg.generated-->
    <!--@Table t_invoice-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="entrust_order_no" jdbcType="VARCHAR" property="entrustOrderNo" />
    <result column="entrust_order_id" jdbcType="BIGINT" property="entrustOrderId" />
    <result column="is_effect" jdbcType="INTEGER" property="isEffect" />
    <result column="invoice_type" jdbcType="INTEGER" property="invoiceType" />
    <result column="title_type" jdbcType="INTEGER" property="titleType" />
    <result column="tax_number" jdbcType="VARCHAR" property="taxNumber" />
    <result column="invoice_title" jdbcType="VARCHAR" property="invoiceTitle" />
    <result column="deposit_bank_name" jdbcType="VARCHAR" property="depositBankName" />
    <result column="deposit_bank_code" jdbcType="VARCHAR" property="depositBankCode" />
    <result column="deposit_account" jdbcType="VARCHAR" property="depositAccount" />
    <result column="company_telephone" jdbcType="VARCHAR" property="companyTelephone" />
    <result column="company_address" jdbcType="VARCHAR" property="companyAddress" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="deposit_bank_address" jdbcType="VARCHAR" property="depositBankAddress" />
    <result column="deposit_bank_region" jdbcType="VARCHAR" property="depositBankRegion" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, entrust_order_no, entrust_order_id, is_effect, invoice_type, title_type, tax_number, 
    invoice_title, deposit_bank_name, deposit_bank_code, deposit_account, company_telephone, 
    company_address, create_by, create_name, create_time, update_by, update_time, update_name, 
    deposit_bank_address, deposit_bank_region
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_invoice
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="entrust_order_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.entrustOrderNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="entrust_order_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.entrustOrderId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="is_effect = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.isEffect,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="invoice_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.invoiceType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="title_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.titleType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="tax_number = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.taxNumber,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="invoice_title = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.invoiceTitle,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="deposit_bank_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.depositBankName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="deposit_bank_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.depositBankCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="deposit_account = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.depositAccount,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="company_telephone = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.companyTelephone,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="company_address = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.companyAddress,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createBy,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="create_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="update_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateBy,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="update_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="deposit_bank_address = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.depositBankAddress,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="deposit_bank_region = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.depositBankRegion,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_invoice
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="entrust_order_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.entrustOrderNo != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.entrustOrderNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="entrust_order_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.entrustOrderId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.entrustOrderId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="is_effect = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isEffect != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.isEffect,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="invoice_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceType != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.invoiceType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="title_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.titleType != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.titleType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="tax_number = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.taxNumber != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.taxNumber,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="invoice_title = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceTitle != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.invoiceTitle,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="deposit_bank_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.depositBankName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.depositBankName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="deposit_bank_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.depositBankCode != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.depositBankCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="deposit_account = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.depositAccount != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.depositAccount,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="company_telephone = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.companyTelephone != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.companyTelephone,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="company_address = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.companyAddress != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.companyAddress,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createBy,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateBy,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="deposit_bank_address = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.depositBankAddress != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.depositBankAddress,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="deposit_bank_region = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.depositBankRegion != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.depositBankRegion,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into t_invoice
    (id, entrust_order_no, entrust_order_id, is_effect, invoice_type, title_type, tax_number, 
      invoice_title, deposit_bank_name, deposit_bank_code, deposit_account, company_telephone, 
      company_address, create_by, create_name, create_time, update_by, update_time, update_name, 
      deposit_bank_address, deposit_bank_region)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.entrustOrderNo,jdbcType=VARCHAR}, #{item.entrustOrderId,jdbcType=BIGINT}, 
        #{item.isEffect,jdbcType=INTEGER}, #{item.invoiceType,jdbcType=INTEGER}, #{item.titleType,jdbcType=INTEGER}, 
        #{item.taxNumber,jdbcType=VARCHAR}, #{item.invoiceTitle,jdbcType=VARCHAR}, #{item.depositBankName,jdbcType=VARCHAR}, 
        #{item.depositBankCode,jdbcType=VARCHAR}, #{item.depositAccount,jdbcType=VARCHAR}, 
        #{item.companyTelephone,jdbcType=VARCHAR}, #{item.companyAddress,jdbcType=VARCHAR}, 
        #{item.createBy,jdbcType=BIGINT}, #{item.createName,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateBy,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.updateName,jdbcType=VARCHAR}, 
        #{item.depositBankAddress,jdbcType=VARCHAR}, #{item.depositBankRegion,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertOrUpdate" parameterType="map">
    <!--@mbg.generated-->
    insert into t_invoice
    (id, entrust_order_no, entrust_order_id, is_effect, invoice_type, title_type, tax_number, 
      invoice_title, deposit_bank_name, deposit_bank_code, deposit_account, company_telephone, 
      company_address, create_by, create_name, create_time, update_by, update_time, update_name, 
      deposit_bank_address, deposit_bank_region)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.entrustOrderNo,jdbcType=VARCHAR}, #{item.entrustOrderId,jdbcType=BIGINT}, 
        #{item.isEffect,jdbcType=INTEGER}, #{item.invoiceType,jdbcType=INTEGER}, #{item.titleType,jdbcType=INTEGER}, 
        #{item.taxNumber,jdbcType=VARCHAR}, #{item.invoiceTitle,jdbcType=VARCHAR}, #{item.depositBankName,jdbcType=VARCHAR}, 
        #{item.depositBankCode,jdbcType=VARCHAR}, #{item.depositAccount,jdbcType=VARCHAR}, 
        #{item.companyTelephone,jdbcType=VARCHAR}, #{item.companyAddress,jdbcType=VARCHAR}, 
        #{item.createBy,jdbcType=BIGINT}, #{item.createName,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateBy,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.updateName,jdbcType=VARCHAR}, 
        #{item.depositBankAddress,jdbcType=VARCHAR}, #{item.depositBankRegion,jdbcType=VARCHAR}
        )
    </foreach>
    on duplicate key update 
    id=values(id),
    entrust_order_no=values(entrust_order_no),
    entrust_order_id=values(entrust_order_id),
    is_effect=values(is_effect),
    invoice_type=values(invoice_type),
    title_type=values(title_type),
    tax_number=values(tax_number),
    invoice_title=values(invoice_title),
    deposit_bank_name=values(deposit_bank_name),
    deposit_bank_code=values(deposit_bank_code),
    deposit_account=values(deposit_account),
    company_telephone=values(company_telephone),
    company_address=values(company_address),
    create_by=values(create_by),
    create_name=values(create_name),
    create_time=values(create_time),
    update_by=values(update_by),
    update_time=values(update_time),
    update_name=values(update_name),
    deposit_bank_address=values(deposit_bank_address),
    deposit_bank_region=values(deposit_bank_region)
  </insert>
  <insert id="insertOnDuplicateUpdate" parameterType="com.lanhu.lims.gateway.admin.model.Invoice">
    <!--@mbg.generated-->
    insert into t_invoice
    (id, entrust_order_no, entrust_order_id, is_effect, invoice_type, title_type, tax_number, 
      invoice_title, deposit_bank_name, deposit_bank_code, deposit_account, company_telephone, 
      company_address, create_by, create_name, create_time, update_by, update_time, update_name, 
      deposit_bank_address, deposit_bank_region)
    values
    (#{id,jdbcType=BIGINT}, #{entrustOrderNo,jdbcType=VARCHAR}, #{entrustOrderId,jdbcType=BIGINT}, 
      #{isEffect,jdbcType=INTEGER}, #{invoiceType,jdbcType=INTEGER}, #{titleType,jdbcType=INTEGER}, 
      #{taxNumber,jdbcType=VARCHAR}, #{invoiceTitle,jdbcType=VARCHAR}, #{depositBankName,jdbcType=VARCHAR}, 
      #{depositBankCode,jdbcType=VARCHAR}, #{depositAccount,jdbcType=VARCHAR}, #{companyTelephone,jdbcType=VARCHAR}, 
      #{companyAddress,jdbcType=VARCHAR}, #{createBy,jdbcType=BIGINT}, #{createName,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{updateName,jdbcType=VARCHAR}, #{depositBankAddress,jdbcType=VARCHAR}, #{depositBankRegion,jdbcType=VARCHAR}
      )
    on duplicate key update 
    id = #{id,jdbcType=BIGINT}, 
    entrust_order_no = #{entrustOrderNo,jdbcType=VARCHAR}, 
    entrust_order_id = #{entrustOrderId,jdbcType=BIGINT}, 
    is_effect = #{isEffect,jdbcType=INTEGER}, 
    invoice_type = #{invoiceType,jdbcType=INTEGER}, 
    title_type = #{titleType,jdbcType=INTEGER}, 
    tax_number = #{taxNumber,jdbcType=VARCHAR}, 
    invoice_title = #{invoiceTitle,jdbcType=VARCHAR}, 
    deposit_bank_name = #{depositBankName,jdbcType=VARCHAR}, 
    deposit_bank_code = #{depositBankCode,jdbcType=VARCHAR}, 
    deposit_account = #{depositAccount,jdbcType=VARCHAR}, 
    company_telephone = #{companyTelephone,jdbcType=VARCHAR}, 
    company_address = #{companyAddress,jdbcType=VARCHAR}, 
    create_by = #{createBy,jdbcType=BIGINT}, 
    create_name = #{createName,jdbcType=VARCHAR}, 
    create_time = #{createTime,jdbcType=TIMESTAMP}, 
    update_by = #{updateBy,jdbcType=BIGINT}, 
    update_time = #{updateTime,jdbcType=TIMESTAMP}, 
    update_name = #{updateName,jdbcType=VARCHAR}, 
    deposit_bank_address = #{depositBankAddress,jdbcType=VARCHAR}, 
    deposit_bank_region = #{depositBankRegion,jdbcType=VARCHAR}
  </insert>
  <insert id="insertOnDuplicateUpdateSelective" parameterType="com.lanhu.lims.gateway.admin.model.Invoice">
    <!--@mbg.generated-->
    insert into t_invoice
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="entrustOrderNo != null">
        entrust_order_no,
      </if>
      <if test="entrustOrderId != null">
        entrust_order_id,
      </if>
      <if test="isEffect != null">
        is_effect,
      </if>
      <if test="invoiceType != null">
        invoice_type,
      </if>
      <if test="titleType != null">
        title_type,
      </if>
      <if test="taxNumber != null">
        tax_number,
      </if>
      <if test="invoiceTitle != null">
        invoice_title,
      </if>
      <if test="depositBankName != null">
        deposit_bank_name,
      </if>
      <if test="depositBankCode != null">
        deposit_bank_code,
      </if>
      <if test="depositAccount != null">
        deposit_account,
      </if>
      <if test="companyTelephone != null">
        company_telephone,
      </if>
      <if test="companyAddress != null">
        company_address,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="depositBankAddress != null">
        deposit_bank_address,
      </if>
      <if test="depositBankRegion != null">
        deposit_bank_region,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="entrustOrderNo != null">
        #{entrustOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="entrustOrderId != null">
        #{entrustOrderId,jdbcType=BIGINT},
      </if>
      <if test="isEffect != null">
        #{isEffect,jdbcType=INTEGER},
      </if>
      <if test="invoiceType != null">
        #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="titleType != null">
        #{titleType,jdbcType=INTEGER},
      </if>
      <if test="taxNumber != null">
        #{taxNumber,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTitle != null">
        #{invoiceTitle,jdbcType=VARCHAR},
      </if>
      <if test="depositBankName != null">
        #{depositBankName,jdbcType=VARCHAR},
      </if>
      <if test="depositBankCode != null">
        #{depositBankCode,jdbcType=VARCHAR},
      </if>
      <if test="depositAccount != null">
        #{depositAccount,jdbcType=VARCHAR},
      </if>
      <if test="companyTelephone != null">
        #{companyTelephone,jdbcType=VARCHAR},
      </if>
      <if test="companyAddress != null">
        #{companyAddress,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="depositBankAddress != null">
        #{depositBankAddress,jdbcType=VARCHAR},
      </if>
      <if test="depositBankRegion != null">
        #{depositBankRegion,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="entrustOrderNo != null">
        entrust_order_no = #{entrustOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="entrustOrderId != null">
        entrust_order_id = #{entrustOrderId,jdbcType=BIGINT},
      </if>
      <if test="isEffect != null">
        is_effect = #{isEffect,jdbcType=INTEGER},
      </if>
      <if test="invoiceType != null">
        invoice_type = #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="titleType != null">
        title_type = #{titleType,jdbcType=INTEGER},
      </if>
      <if test="taxNumber != null">
        tax_number = #{taxNumber,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTitle != null">
        invoice_title = #{invoiceTitle,jdbcType=VARCHAR},
      </if>
      <if test="depositBankName != null">
        deposit_bank_name = #{depositBankName,jdbcType=VARCHAR},
      </if>
      <if test="depositBankCode != null">
        deposit_bank_code = #{depositBankCode,jdbcType=VARCHAR},
      </if>
      <if test="depositAccount != null">
        deposit_account = #{depositAccount,jdbcType=VARCHAR},
      </if>
      <if test="companyTelephone != null">
        company_telephone = #{companyTelephone,jdbcType=VARCHAR},
      </if>
      <if test="companyAddress != null">
        company_address = #{companyAddress,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="depositBankAddress != null">
        deposit_bank_address = #{depositBankAddress,jdbcType=VARCHAR},
      </if>
      <if test="depositBankRegion != null">
        deposit_bank_region = #{depositBankRegion,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>