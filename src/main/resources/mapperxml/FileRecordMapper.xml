<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanhu.lims.gateway.admin.mapper.FileRecordMapper">
    <resultMap id="BaseResultMap" type="com.lanhu.lims.gateway.admin.model.FileRecord">
        <!--@mbg.generated-->
        <!--@Table t_file_record-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="batch_id" jdbcType="BIGINT" property="batchId"/>
        <result column="attachment_url" jdbcType="VARCHAR" property="attachmentUrl"/>
        <result column="attachment_name" jdbcType="VARCHAR" property="attachmentName"/>
        <result column="attachment_suffix" jdbcType="VARCHAR" property="attachmentSuffix"/>
        <result column="attachment_size" jdbcType="BIGINT" property="attachmentSize"/>
        <result column="file_acl" jdbcType="INTEGER" property="fileAcl"/>
        <result column="is_effect" jdbcType="INTEGER" property="isEffect"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_name" jdbcType="VARCHAR" property="updateName"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, batch_id, attachment_url, attachment_name, attachment_suffix, attachment_size,
        file_acl, is_effect, create_by, create_name, create_time, update_by, update_name,
        update_time
    </sql>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into t_file_record
        (id, batch_id, attachment_url, attachment_name, attachment_suffix, attachment_size,
        file_acl, is_effect, create_by, create_name, create_time, update_by, update_name,
        update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.batchId,jdbcType=BIGINT}, #{item.attachmentUrl,jdbcType=VARCHAR},
            #{item.attachmentName,jdbcType=VARCHAR}, #{item.attachmentSuffix,jdbcType=VARCHAR},
            #{item.attachmentSize,jdbcType=BIGINT}, #{item.fileAcl,jdbcType=INTEGER}, #{item.isEffect,jdbcType=INTEGER},
            #{item.createBy,jdbcType=BIGINT}, #{item.createName,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateBy,jdbcType=BIGINT}, #{item.updateName,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>
</mapper>