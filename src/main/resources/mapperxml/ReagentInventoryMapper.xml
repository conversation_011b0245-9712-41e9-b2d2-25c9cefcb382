<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanhu.lims.gateway.admin.mapper.ReagentInventoryMapper">
    <resultMap id="BaseResultMap" type="com.lanhu.lims.gateway.admin.model.ReagentInventory">
        <!--@mbg.generated-->
        <!--@Table t_reagent_inventory-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="reagent_bottle_id" jdbcType="VARCHAR" property="reagentBottleId"/>
        <result column="reagent_id" jdbcType="BIGINT" property="reagentId"/>
        <result column="reagent_specification_id" jdbcType="BIGINT" property="reagentSpecificationId"/>
        <result column="store_position" jdbcType="VARCHAR" property="storePosition"/>
        <result column="inventory_total" jdbcType="DECIMAL" property="inventoryTotal"/>
        <result column="inventory_balance" jdbcType="DECIMAL" property="inventoryBalance"/>
        <result column="is_effect" jdbcType="INTEGER" property="isEffect"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_name" jdbcType="VARCHAR" property="updateName"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, reagent_bottle_id, reagent_id, reagent_specification_id, store_position, inventory_total,
        inventory_balance, is_effect, create_by, create_name, create_time, update_by, update_name,
        update_time
    </sql>
</mapper>