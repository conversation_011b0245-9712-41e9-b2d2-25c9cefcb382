<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanhu.lims.gateway.admin.mapper.EquipmentMapper">
  <resultMap id="BaseResultMap" type="com.lanhu.lims.gateway.admin.model.Equipment">
    <!--@mbg.generated-->
    <!--@Table t_equipment-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="equipment_code" jdbcType="VARCHAR" property="equipmentCode" />
    <result column="equipment_name" jdbcType="VARCHAR" property="equipmentName" />
    <result column="usage" jdbcType="VARCHAR" property="usage" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="model" jdbcType="VARCHAR" property="model" />
    <result column="ownership_status" jdbcType="INTEGER" property="ownershipStatus" />
    <result column="inspect_item" jdbcType="VARCHAR" property="inspectItem" />
    <result column="consignee" jdbcType="VARCHAR" property="consignee" />
    <result column="receipt_date" jdbcType="VARCHAR" property="receiptDate" />
    <result column="maintenance_user" jdbcType="VARCHAR" property="maintenanceUser" />
    <result column="last_maintenance_date" jdbcType="TIMESTAMP" property="lastMaintenanceDate" />
    <result column="next_maintenance_date" jdbcType="TIMESTAMP" property="nextMaintenanceDate" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="is_effect" jdbcType="INTEGER" property="isEffect" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="audit_status" jdbcType="INTEGER" property="auditStatus" />
    <result column="audit_by" jdbcType="BIGINT" property="auditBy" />
    <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime" />
    <result column="audit_name" jdbcType="VARCHAR" property="auditName" />
    <result column="audit_remark" jdbcType="LONGVARCHAR" property="auditRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, equipment_code, equipment_name, `usage`, brand, model, ownership_status, inspect_item, 
    consignee, receipt_date, maintenance_user, last_maintenance_date, next_maintenance_date, 
    `status`, is_effect, create_by, create_name, create_time, update_by, update_name, 
    update_time, audit_status, audit_by, audit_time, audit_name, audit_remark
  </sql>
</mapper>