<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanhu.lims.gateway.admin.mapper.RoleMenuMapper">
  <resultMap id="BaseResultMap" type="com.lanhu.lims.gateway.admin.model.RoleMenu">
    <!--@mbg.generated-->
    <!--@Table t_role_menu-->
    <result column="role_id" jdbcType="BIGINT" property="roleId" />
    <result column="menu_id" jdbcType="BIGINT" property="menuId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    role_id, menu_id
  </sql>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into t_role_menu
    (role_id, menu_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.roleId,jdbcType=BIGINT}, #{item.menuId,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertOrUpdate" parameterType="map">
    <!--@mbg.generated-->
    insert into t_role_menu
    (role_id, menu_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.roleId,jdbcType=BIGINT}, #{item.menuId,jdbcType=BIGINT})
    </foreach>
    on duplicate key update 
    role_id=values(role_id),
    menu_id=values(menu_id)
  </insert>
  <insert id="insertOnDuplicateUpdate" parameterType="com.lanhu.lims.gateway.admin.model.RoleMenu">
    <!--@mbg.generated-->
    insert into t_role_menu
    (role_id, menu_id)
    values
    (#{roleId,jdbcType=BIGINT}, #{menuId,jdbcType=BIGINT})
    on duplicate key update 
    role_id = #{roleId,jdbcType=BIGINT}, 
    menu_id = #{menuId,jdbcType=BIGINT}
  </insert>
  <insert id="insertOnDuplicateUpdateSelective" parameterType="com.lanhu.lims.gateway.admin.model.RoleMenu">
    <!--@mbg.generated-->
    insert into t_role_menu
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="roleId != null">
        role_id,
      </if>
      <if test="menuId != null">
        menu_id,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="roleId != null">
        #{roleId,jdbcType=BIGINT},
      </if>
      <if test="menuId != null">
        #{menuId,jdbcType=BIGINT},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="roleId != null">
        role_id = #{roleId,jdbcType=BIGINT},
      </if>
      <if test="menuId != null">
        menu_id = #{menuId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>

  <select id="selectListByRoleId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_role_menu
    where role_id = #{roleId,jdbcType=BIGINT}
  </select>

</mapper>