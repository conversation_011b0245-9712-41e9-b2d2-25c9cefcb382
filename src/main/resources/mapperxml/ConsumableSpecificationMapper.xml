<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanhu.lims.gateway.admin.mapper.ConsumableSpecificationMapper">
    <resultMap id="BaseResultMap" type="com.lanhu.lims.gateway.admin.model.ConsumableSpecification">
        <!--@mbg.generated-->
        <!--@Table t_consumable_specification-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="consumable_id" jdbcType="BIGINT" property="consumableId"/>
        <result column="brand" jdbcType="VARCHAR" property="brand"/>
        <result column="catalog_number" jdbcType="VARCHAR" property="catalogNumber"/>
        <result column="specification" jdbcType="VARCHAR" property="specification"/>
        <result column="unit" jdbcType="VARCHAR" property="unit"/>
        <result column="storage_condition" jdbcType="VARCHAR" property="storageCondition"/>
        <result column="material" jdbcType="VARCHAR" property="material"/>
        <result column="is_effect" jdbcType="INTEGER" property="isEffect"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_name" jdbcType="VARCHAR" property="updateName"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="audit_status" jdbcType="INTEGER" property="auditStatus"/>
        <result column="audit_by" jdbcType="BIGINT" property="auditBy"/>
        <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime"/>
        <result column="audit_name" jdbcType="VARCHAR" property="auditName"/>
        <result column="audit_remark" jdbcType="LONGVARCHAR" property="auditRemark"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, consumable_id, brand, catalog_number, specification, unit, storage_condition,
        material, is_effect, create_by, create_name, create_time, update_by, update_name,
        update_time, audit_status, audit_by, audit_time, audit_name, audit_remark
    </sql>
</mapper>