<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanhu.lims.gateway.admin.mapper.DictDataMapper">
    <resultMap id="BaseResultMap" type="com.lanhu.lims.gateway.admin.model.DictData">
        <!--@mbg.generated-->
        <!--@Table t_dict_data-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="dict_sort" jdbcType="INTEGER" property="dictSort"/>
        <result column="dict_data" jdbcType="LONGVARCHAR" property="dictData"/>
        <result column="dict_label" jdbcType="VARCHAR" property="dictLabel"/>
        <result column="dict_label_en" jdbcType="VARCHAR" property="dictLabelEn"/>
        <result column="dict_value" jdbcType="VARCHAR" property="dictValue"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="category_id" jdbcType="INTEGER" property="categoryId"/>
        <result column="mapping_label" jdbcType="VARCHAR" property="mappingLabel"/>
        <result column="mapping_value" jdbcType="VARCHAR" property="mappingValue"/>
        <result column="visible" jdbcType="INTEGER" property="visible"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="is_effect" jdbcType="INTEGER" property="isEffect"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_name" jdbcType="VARCHAR" property="updateName"/>
        <result column="audit_status" jdbcType="INTEGER" property="auditStatus"/>
        <result column="audit_by" jdbcType="BIGINT" property="auditBy"/>
        <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime"/>
        <result column="audit_name" jdbcType="VARCHAR" property="auditName"/>
        <result column="audit_remark" jdbcType="LONGVARCHAR" property="auditRemark"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, dict_sort, dict_data, dict_label, dict_label_en, dict_value, parent_id, category_id,
        mapping_label, mapping_value, visible, `status`, is_effect, create_time, create_by,
        create_name, update_time, update_by, update_name, audit_status, audit_by, audit_time,
        audit_name, audit_remark
    </sql>

    <!-- 根据字典类型和值列表查询有效的字典数据 -->
    <select id="selectValidDictDataByTypeAndValues" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List"/>
        FROM t_dict_data d
        WHERE d.is_effect = 0
          AND d.status = 1
          AND d.dict_value IN
          <foreach collection="values" item="value" open="(" separator="," close=")">
              #{value}
          </foreach>
          AND EXISTS (
              SELECT 1 FROM t_dict_data parent
              WHERE parent.id = d.parent_id
                AND parent.dict_value = #{dictType}
                AND parent.is_effect = 0
                AND parent.status = 1
          )
        ORDER BY d.dict_sort ASC, d.create_time ASC
    </select>
</mapper>