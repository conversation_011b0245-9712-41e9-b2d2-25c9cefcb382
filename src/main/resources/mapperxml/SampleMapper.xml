<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanhu.lims.gateway.admin.mapper.SampleMapper">
  <resultMap id="BaseResultMap" type="com.lanhu.lims.gateway.admin.model.Sample">
    <!--@mbg.generated-->
    <!--@Table t_sample-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sample_code" jdbcType="VARCHAR" property="sampleCode" />
    <result column="barcode" jdbcType="VARCHAR" property="barcode" />
    <result column="entrust_order_id" jdbcType="BIGINT" property="entrustOrderId" />
    <result column="entrust_order_no" jdbcType="VARCHAR" property="entrustOrderNo" />
    <result column="sample_name" jdbcType="VARCHAR" property="sampleName" />
    <result column="is_effect" jdbcType="INTEGER" property="isEffect" />
    <result column="sample_type" jdbcType="VARCHAR" property="sampleType" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="species_name" jdbcType="VARCHAR" property="speciesName" />
    <result column="sample_form" jdbcType="INTEGER" property="sampleForm" />
    <result column="position_num" jdbcType="VARCHAR" property="positionNum" />
    <result column="plate_num" jdbcType="VARCHAR" property="plateNum" />
    <result column="inspect_item" jdbcType="VARCHAR" property="inspectItem" />
    <result column="store_location" jdbcType="VARCHAR" property="storeLocation" />
    <result column="store_time" jdbcType="TIMESTAMP" property="storeTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="expire_time" jdbcType="TIMESTAMP" property="expireTime" />
    <result column="destory_time" jdbcType="TIMESTAMP" property="destoryTime" />
    <result column="destory_by" jdbcType="BIGINT" property="destoryBy" />
    <result column="destory_name" jdbcType="VARCHAR" property="destoryName" />
    <result column="complete_by" jdbcType="BIGINT" property="completeBy" />
    <result column="complate_name" jdbcType="VARCHAR" property="complateName" />
    <result column="store_by" jdbcType="BIGINT" property="storeBy" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="destory_apply_id" jdbcType="BIGINT" property="destoryApplyId" />
    <result column="close_by" jdbcType="BIGINT" property="closeBy" />
    <result column="close_name" jdbcType="VARCHAR" property="closeName" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, sample_code, barcode, entrust_order_id, entrust_order_no, sample_name, is_effect, 
    sample_type, remark, create_by, create_name, create_time, update_by, update_time, 
    update_name, species_name, sample_form, position_num, plate_num, inspect_item, store_location, 
    store_time, `status`, complete_time, expire_time, destory_time, destory_by, destory_name, 
    complete_by, complate_name, store_by, store_name, destory_apply_id, close_by, close_name, 
    parent_id
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_sample
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="sample_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.sampleCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="barcode = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.barcode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="entrust_order_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.entrustOrderId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="entrust_order_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.entrustOrderNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="sample_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.sampleName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="is_effect = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.isEffect,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="sample_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.sampleType,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.remark,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createBy,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="create_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="update_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateBy,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="update_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="species_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.speciesName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="sample_form = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.sampleForm,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="position_num = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.positionNum,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="plate_num = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.plateNum,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="inspect_item = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.inspectItem,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="store_location = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.storeLocation,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="store_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.storeTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.status,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="complete_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.completeTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="expire_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.expireTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="destory_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.destoryTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="destory_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.destoryBy,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="destory_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.destoryName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="complete_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.completeBy,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="complate_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.complateName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="store_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.storeBy,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="store_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.storeName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="destory_apply_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.destoryApplyId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="close_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.closeBy,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="close_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.closeName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="parent_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.parentId,jdbcType=BIGINT}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_sample
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="sample_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.sampleCode != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.sampleCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="barcode = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.barcode != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.barcode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="entrust_order_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.entrustOrderId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.entrustOrderId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="entrust_order_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.entrustOrderNo != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.entrustOrderNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="sample_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.sampleName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.sampleName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="is_effect = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isEffect != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.isEffect,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="sample_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.sampleType != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.sampleType,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.remark != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.remark,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createBy,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateBy,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="species_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.speciesName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.speciesName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="sample_form = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.sampleForm != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.sampleForm,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="position_num = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.positionNum != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.positionNum,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="plate_num = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.plateNum != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.plateNum,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="inspect_item = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.inspectItem != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.inspectItem,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="store_location = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.storeLocation != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.storeLocation,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="store_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.storeTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.storeTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.status != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.status,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="complete_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.completeTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.completeTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="expire_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.expireTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.expireTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="destory_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.destoryTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.destoryTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="destory_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.destoryBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.destoryBy,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="destory_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.destoryName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.destoryName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="complete_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.completeBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.completeBy,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="complate_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.complateName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.complateName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="store_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.storeBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.storeBy,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="store_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.storeName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.storeName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="destory_apply_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.destoryApplyId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.destoryApplyId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="close_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.closeBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.closeBy,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="close_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.closeName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.closeName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="parent_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.parentId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.parentId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into t_sample
    (id, sample_code, barcode, entrust_order_id, entrust_order_no, sample_name, is_effect, 
      sample_type, remark, create_by, create_name, create_time, update_by, update_time, 
      update_name, species_name, sample_form, position_num, plate_num, inspect_item, 
      store_location, store_time, `status`, complete_time, expire_time, destory_time, 
      destory_by, destory_name, complete_by, complate_name, store_by, store_name, destory_apply_id, 
      close_by, close_name, parent_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.sampleCode,jdbcType=VARCHAR}, #{item.barcode,jdbcType=VARCHAR}, 
        #{item.entrustOrderId,jdbcType=BIGINT}, #{item.entrustOrderNo,jdbcType=VARCHAR}, 
        #{item.sampleName,jdbcType=VARCHAR}, #{item.isEffect,jdbcType=INTEGER}, #{item.sampleType,jdbcType=VARCHAR}, 
        #{item.remark,jdbcType=VARCHAR}, #{item.createBy,jdbcType=BIGINT}, #{item.createName,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateBy,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.updateName,jdbcType=VARCHAR}, #{item.speciesName,jdbcType=VARCHAR}, #{item.sampleForm,jdbcType=INTEGER}, 
        #{item.positionNum,jdbcType=VARCHAR}, #{item.plateNum,jdbcType=VARCHAR}, #{item.inspectItem,jdbcType=VARCHAR}, 
        #{item.storeLocation,jdbcType=VARCHAR}, #{item.storeTime,jdbcType=TIMESTAMP}, #{item.status,jdbcType=INTEGER}, 
        #{item.completeTime,jdbcType=TIMESTAMP}, #{item.expireTime,jdbcType=TIMESTAMP}, 
        #{item.destoryTime,jdbcType=TIMESTAMP}, #{item.destoryBy,jdbcType=BIGINT}, #{item.destoryName,jdbcType=VARCHAR}, 
        #{item.completeBy,jdbcType=BIGINT}, #{item.complateName,jdbcType=VARCHAR}, #{item.storeBy,jdbcType=BIGINT}, 
        #{item.storeName,jdbcType=VARCHAR}, #{item.destoryApplyId,jdbcType=BIGINT}, #{item.closeBy,jdbcType=BIGINT}, 
        #{item.closeName,jdbcType=VARCHAR}, #{item.parentId,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertOrUpdate" parameterType="map">
    <!--@mbg.generated-->
    insert into t_sample
    (id, sample_code, barcode, entrust_order_id, entrust_order_no, sample_name, is_effect, 
      sample_type, remark, create_by, create_name, create_time, update_by, update_time, 
      update_name, species_name, sample_form, position_num, plate_num, inspect_item, 
      store_location, store_time, `status`, complete_time, expire_time, destory_time, 
      destory_by, destory_name, complete_by, complate_name, store_by, store_name, destory_apply_id, 
      close_by, close_name, parent_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.sampleCode,jdbcType=VARCHAR}, #{item.barcode,jdbcType=VARCHAR}, 
        #{item.entrustOrderId,jdbcType=BIGINT}, #{item.entrustOrderNo,jdbcType=VARCHAR}, 
        #{item.sampleName,jdbcType=VARCHAR}, #{item.isEffect,jdbcType=INTEGER}, #{item.sampleType,jdbcType=VARCHAR}, 
        #{item.remark,jdbcType=VARCHAR}, #{item.createBy,jdbcType=BIGINT}, #{item.createName,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateBy,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.updateName,jdbcType=VARCHAR}, #{item.speciesName,jdbcType=VARCHAR}, #{item.sampleForm,jdbcType=INTEGER}, 
        #{item.positionNum,jdbcType=VARCHAR}, #{item.plateNum,jdbcType=VARCHAR}, #{item.inspectItem,jdbcType=VARCHAR}, 
        #{item.storeLocation,jdbcType=VARCHAR}, #{item.storeTime,jdbcType=TIMESTAMP}, #{item.status,jdbcType=INTEGER}, 
        #{item.completeTime,jdbcType=TIMESTAMP}, #{item.expireTime,jdbcType=TIMESTAMP}, 
        #{item.destoryTime,jdbcType=TIMESTAMP}, #{item.destoryBy,jdbcType=BIGINT}, #{item.destoryName,jdbcType=VARCHAR}, 
        #{item.completeBy,jdbcType=BIGINT}, #{item.complateName,jdbcType=VARCHAR}, #{item.storeBy,jdbcType=BIGINT}, 
        #{item.storeName,jdbcType=VARCHAR}, #{item.destoryApplyId,jdbcType=BIGINT}, #{item.closeBy,jdbcType=BIGINT}, 
        #{item.closeName,jdbcType=VARCHAR}, #{item.parentId,jdbcType=BIGINT})
    </foreach>
    on duplicate key update 
    id=values(id),
    sample_code=values(sample_code),
    barcode=values(barcode),
    entrust_order_id=values(entrust_order_id),
    entrust_order_no=values(entrust_order_no),
    sample_name=values(sample_name),
    is_effect=values(is_effect),
    sample_type=values(sample_type),
    remark=values(remark),
    create_by=values(create_by),
    create_name=values(create_name),
    create_time=values(create_time),
    update_by=values(update_by),
    update_time=values(update_time),
    update_name=values(update_name),
    species_name=values(species_name),
    sample_form=values(sample_form),
    position_num=values(position_num),
    plate_num=values(plate_num),
    inspect_item=values(inspect_item),
    store_location=values(store_location),
    store_time=values(store_time),
    status=values(status),
    complete_time=values(complete_time),
    expire_time=values(expire_time),
    destory_time=values(destory_time),
    destory_by=values(destory_by),
    destory_name=values(destory_name),
    complete_by=values(complete_by),
    complate_name=values(complate_name),
    store_by=values(store_by),
    store_name=values(store_name),
    destory_apply_id=values(destory_apply_id),
    close_by=values(close_by),
    close_name=values(close_name),
    parent_id=values(parent_id)
  </insert>
  <insert id="insertOnDuplicateUpdate" parameterType="com.lanhu.lims.gateway.admin.model.Sample">
    <!--@mbg.generated-->
    insert into t_sample
    (id, sample_code, barcode, entrust_order_id, entrust_order_no, sample_name, is_effect, 
      sample_type, remark, create_by, create_name, create_time, update_by, update_time, 
      update_name, species_name, sample_form, position_num, plate_num, inspect_item, 
      store_location, store_time, `status`, complete_time, expire_time, destory_time, 
      destory_by, destory_name, complete_by, complate_name, store_by, store_name, destory_apply_id, 
      close_by, close_name, parent_id)
    values
    (#{id,jdbcType=BIGINT}, #{sampleCode,jdbcType=VARCHAR}, #{barcode,jdbcType=VARCHAR}, 
      #{entrustOrderId,jdbcType=BIGINT}, #{entrustOrderNo,jdbcType=VARCHAR}, #{sampleName,jdbcType=VARCHAR}, 
      #{isEffect,jdbcType=INTEGER}, #{sampleType,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{createBy,jdbcType=BIGINT}, #{createName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{updateName,jdbcType=VARCHAR}, 
      #{speciesName,jdbcType=VARCHAR}, #{sampleForm,jdbcType=INTEGER}, #{positionNum,jdbcType=VARCHAR}, 
      #{plateNum,jdbcType=VARCHAR}, #{inspectItem,jdbcType=VARCHAR}, #{storeLocation,jdbcType=VARCHAR}, 
      #{storeTime,jdbcType=TIMESTAMP}, #{status,jdbcType=INTEGER}, #{completeTime,jdbcType=TIMESTAMP}, 
      #{expireTime,jdbcType=TIMESTAMP}, #{destoryTime,jdbcType=TIMESTAMP}, #{destoryBy,jdbcType=BIGINT}, 
      #{destoryName,jdbcType=VARCHAR}, #{completeBy,jdbcType=BIGINT}, #{complateName,jdbcType=VARCHAR}, 
      #{storeBy,jdbcType=BIGINT}, #{storeName,jdbcType=VARCHAR}, #{destoryApplyId,jdbcType=BIGINT}, 
      #{closeBy,jdbcType=BIGINT}, #{closeName,jdbcType=VARCHAR}, #{parentId,jdbcType=BIGINT}
      )
    on duplicate key update 
    id = #{id,jdbcType=BIGINT}, 
    sample_code = #{sampleCode,jdbcType=VARCHAR}, 
    barcode = #{barcode,jdbcType=VARCHAR}, 
    entrust_order_id = #{entrustOrderId,jdbcType=BIGINT}, 
    entrust_order_no = #{entrustOrderNo,jdbcType=VARCHAR}, 
    sample_name = #{sampleName,jdbcType=VARCHAR}, 
    is_effect = #{isEffect,jdbcType=INTEGER}, 
    sample_type = #{sampleType,jdbcType=VARCHAR}, 
    remark = #{remark,jdbcType=VARCHAR}, 
    create_by = #{createBy,jdbcType=BIGINT}, 
    create_name = #{createName,jdbcType=VARCHAR}, 
    create_time = #{createTime,jdbcType=TIMESTAMP}, 
    update_by = #{updateBy,jdbcType=BIGINT}, 
    update_time = #{updateTime,jdbcType=TIMESTAMP}, 
    update_name = #{updateName,jdbcType=VARCHAR}, 
    species_name = #{speciesName,jdbcType=VARCHAR}, 
    sample_form = #{sampleForm,jdbcType=INTEGER}, 
    position_num = #{positionNum,jdbcType=VARCHAR}, 
    plate_num = #{plateNum,jdbcType=VARCHAR}, 
    inspect_item = #{inspectItem,jdbcType=VARCHAR}, 
    store_location = #{storeLocation,jdbcType=VARCHAR}, 
    store_time = #{storeTime,jdbcType=TIMESTAMP}, 
    `status` = #{status,jdbcType=INTEGER}, 
    complete_time = #{completeTime,jdbcType=TIMESTAMP}, 
    expire_time = #{expireTime,jdbcType=TIMESTAMP}, 
    destory_time = #{destoryTime,jdbcType=TIMESTAMP}, 
    destory_by = #{destoryBy,jdbcType=BIGINT}, 
    destory_name = #{destoryName,jdbcType=VARCHAR}, 
    complete_by = #{completeBy,jdbcType=BIGINT}, 
    complate_name = #{complateName,jdbcType=VARCHAR}, 
    store_by = #{storeBy,jdbcType=BIGINT}, 
    store_name = #{storeName,jdbcType=VARCHAR}, 
    destory_apply_id = #{destoryApplyId,jdbcType=BIGINT}, 
    close_by = #{closeBy,jdbcType=BIGINT}, 
    close_name = #{closeName,jdbcType=VARCHAR}, 
    parent_id = #{parentId,jdbcType=BIGINT}
  </insert>
  <insert id="insertOnDuplicateUpdateSelective" parameterType="com.lanhu.lims.gateway.admin.model.Sample">
    <!--@mbg.generated-->
    insert into t_sample
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="sampleCode != null">
        sample_code,
      </if>
      <if test="barcode != null">
        barcode,
      </if>
      <if test="entrustOrderId != null">
        entrust_order_id,
      </if>
      <if test="entrustOrderNo != null">
        entrust_order_no,
      </if>
      <if test="sampleName != null">
        sample_name,
      </if>
      <if test="isEffect != null">
        is_effect,
      </if>
      <if test="sampleType != null">
        sample_type,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="speciesName != null">
        species_name,
      </if>
      <if test="sampleForm != null">
        sample_form,
      </if>
      <if test="positionNum != null">
        position_num,
      </if>
      <if test="plateNum != null">
        plate_num,
      </if>
      <if test="inspectItem != null">
        inspect_item,
      </if>
      <if test="storeLocation != null">
        store_location,
      </if>
      <if test="storeTime != null">
        store_time,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="completeTime != null">
        complete_time,
      </if>
      <if test="expireTime != null">
        expire_time,
      </if>
      <if test="destoryTime != null">
        destory_time,
      </if>
      <if test="destoryBy != null">
        destory_by,
      </if>
      <if test="destoryName != null">
        destory_name,
      </if>
      <if test="completeBy != null">
        complete_by,
      </if>
      <if test="complateName != null">
        complate_name,
      </if>
      <if test="storeBy != null">
        store_by,
      </if>
      <if test="storeName != null">
        store_name,
      </if>
      <if test="destoryApplyId != null">
        destory_apply_id,
      </if>
      <if test="closeBy != null">
        close_by,
      </if>
      <if test="closeName != null">
        close_name,
      </if>
      <if test="parentId != null">
        parent_id,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="sampleCode != null">
        #{sampleCode,jdbcType=VARCHAR},
      </if>
      <if test="barcode != null">
        #{barcode,jdbcType=VARCHAR},
      </if>
      <if test="entrustOrderId != null">
        #{entrustOrderId,jdbcType=BIGINT},
      </if>
      <if test="entrustOrderNo != null">
        #{entrustOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="sampleName != null">
        #{sampleName,jdbcType=VARCHAR},
      </if>
      <if test="isEffect != null">
        #{isEffect,jdbcType=INTEGER},
      </if>
      <if test="sampleType != null">
        #{sampleType,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="speciesName != null">
        #{speciesName,jdbcType=VARCHAR},
      </if>
      <if test="sampleForm != null">
        #{sampleForm,jdbcType=INTEGER},
      </if>
      <if test="positionNum != null">
        #{positionNum,jdbcType=VARCHAR},
      </if>
      <if test="plateNum != null">
        #{plateNum,jdbcType=VARCHAR},
      </if>
      <if test="inspectItem != null">
        #{inspectItem,jdbcType=VARCHAR},
      </if>
      <if test="storeLocation != null">
        #{storeLocation,jdbcType=VARCHAR},
      </if>
      <if test="storeTime != null">
        #{storeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="completeTime != null">
        #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expireTime != null">
        #{expireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="destoryTime != null">
        #{destoryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="destoryBy != null">
        #{destoryBy,jdbcType=BIGINT},
      </if>
      <if test="destoryName != null">
        #{destoryName,jdbcType=VARCHAR},
      </if>
      <if test="completeBy != null">
        #{completeBy,jdbcType=BIGINT},
      </if>
      <if test="complateName != null">
        #{complateName,jdbcType=VARCHAR},
      </if>
      <if test="storeBy != null">
        #{storeBy,jdbcType=BIGINT},
      </if>
      <if test="storeName != null">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="destoryApplyId != null">
        #{destoryApplyId,jdbcType=BIGINT},
      </if>
      <if test="closeBy != null">
        #{closeBy,jdbcType=BIGINT},
      </if>
      <if test="closeName != null">
        #{closeName,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=BIGINT},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="sampleCode != null">
        sample_code = #{sampleCode,jdbcType=VARCHAR},
      </if>
      <if test="barcode != null">
        barcode = #{barcode,jdbcType=VARCHAR},
      </if>
      <if test="entrustOrderId != null">
        entrust_order_id = #{entrustOrderId,jdbcType=BIGINT},
      </if>
      <if test="entrustOrderNo != null">
        entrust_order_no = #{entrustOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="sampleName != null">
        sample_name = #{sampleName,jdbcType=VARCHAR},
      </if>
      <if test="isEffect != null">
        is_effect = #{isEffect,jdbcType=INTEGER},
      </if>
      <if test="sampleType != null">
        sample_type = #{sampleType,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="speciesName != null">
        species_name = #{speciesName,jdbcType=VARCHAR},
      </if>
      <if test="sampleForm != null">
        sample_form = #{sampleForm,jdbcType=INTEGER},
      </if>
      <if test="positionNum != null">
        position_num = #{positionNum,jdbcType=VARCHAR},
      </if>
      <if test="plateNum != null">
        plate_num = #{plateNum,jdbcType=VARCHAR},
      </if>
      <if test="inspectItem != null">
        inspect_item = #{inspectItem,jdbcType=VARCHAR},
      </if>
      <if test="storeLocation != null">
        store_location = #{storeLocation,jdbcType=VARCHAR},
      </if>
      <if test="storeTime != null">
        store_time = #{storeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="completeTime != null">
        complete_time = #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expireTime != null">
        expire_time = #{expireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="destoryTime != null">
        destory_time = #{destoryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="destoryBy != null">
        destory_by = #{destoryBy,jdbcType=BIGINT},
      </if>
      <if test="destoryName != null">
        destory_name = #{destoryName,jdbcType=VARCHAR},
      </if>
      <if test="completeBy != null">
        complete_by = #{completeBy,jdbcType=BIGINT},
      </if>
      <if test="complateName != null">
        complate_name = #{complateName,jdbcType=VARCHAR},
      </if>
      <if test="storeBy != null">
        store_by = #{storeBy,jdbcType=BIGINT},
      </if>
      <if test="storeName != null">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="destoryApplyId != null">
        destory_apply_id = #{destoryApplyId,jdbcType=BIGINT},
      </if>
      <if test="closeBy != null">
        close_by = #{closeBy,jdbcType=BIGINT},
      </if>
      <if test="closeName != null">
        close_name = #{closeName,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>






</mapper>