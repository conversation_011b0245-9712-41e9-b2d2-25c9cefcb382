<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanhu.lims.gateway.admin.mapper.AuditLogMapper">
  <resultMap id="BaseResultMap" type="com.lanhu.lims.gateway.admin.model.AuditLog">
    <!--@mbg.generated-->
    <!--@Table t_audit_log-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="table_name" jdbcType="VARCHAR" property="tableName" />
    <result column="record_id" jdbcType="BIGINT" property="recordId" />
    <result column="operation" jdbcType="VARCHAR" property="operation" />
    <result column="old_value" jdbcType="LONGVARCHAR" property="oldValue" />
    <result column="new_value" jdbcType="LONGVARCHAR" property="newValue" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `table_name`, record_id, `operation`, old_value, new_value, create_time, create_by, 
    create_name
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_audit_log
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`table_name` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.tableName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="record_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.recordId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="`operation` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.operation,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="old_value = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.oldValue,jdbcType=LONGVARCHAR}
        </foreach>
      </trim>
      <trim prefix="new_value = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.newValue,jdbcType=LONGVARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.createBy,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="create_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.createName,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_audit_log
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`table_name` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.tableName != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.tableName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="record_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.recordId != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.recordId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="`operation` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.operation != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.operation,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="old_value = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.oldValue != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.oldValue,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="new_value = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.newValue != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.newValue,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createBy != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.createBy,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createName != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.createName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_audit_log
    (`table_name`, record_id, `operation`, old_value, new_value, create_time, create_by, 
      create_name)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.tableName,jdbcType=VARCHAR}, #{item.recordId,jdbcType=BIGINT}, #{item.operation,jdbcType=VARCHAR}, 
        #{item.oldValue,jdbcType=LONGVARCHAR}, #{item.newValue,jdbcType=LONGVARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.createBy,jdbcType=BIGINT}, #{item.createName,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertOrUpdate" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_audit_log
    (`table_name`, record_id, `operation`, old_value, new_value, create_time, create_by, 
      create_name)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.tableName,jdbcType=VARCHAR}, #{item.recordId,jdbcType=BIGINT}, #{item.operation,jdbcType=VARCHAR}, 
        #{item.oldValue,jdbcType=LONGVARCHAR}, #{item.newValue,jdbcType=LONGVARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.createBy,jdbcType=BIGINT}, #{item.createName,jdbcType=VARCHAR})
    </foreach>
    on duplicate key update 
    table_name=values(table_name),
    record_id=values(record_id),
    operation=values(operation),
    old_value=values(old_value),
    new_value=values(new_value),
    create_time=values(create_time),
    create_by=values(create_by),
    create_name=values(create_name)
  </insert>
  <insert id="insertOnDuplicateUpdate" keyColumn="id" keyProperty="id" parameterType="com.lanhu.lims.gateway.admin.model.AuditLog" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_audit_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      `table_name`,
      record_id,
      `operation`,
      old_value,
      new_value,
      create_time,
      create_by,
      create_name,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      #{tableName,jdbcType=VARCHAR},
      #{recordId,jdbcType=BIGINT},
      #{operation,jdbcType=VARCHAR},
      #{oldValue,jdbcType=LONGVARCHAR},
      #{newValue,jdbcType=LONGVARCHAR},
      #{createTime,jdbcType=TIMESTAMP},
      #{createBy,jdbcType=BIGINT},
      #{createName,jdbcType=VARCHAR},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=INTEGER},
      </if>
      `table_name` = #{tableName,jdbcType=VARCHAR},
      record_id = #{recordId,jdbcType=BIGINT},
      `operation` = #{operation,jdbcType=VARCHAR},
      old_value = #{oldValue,jdbcType=LONGVARCHAR},
      new_value = #{newValue,jdbcType=LONGVARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=BIGINT},
      create_name = #{createName,jdbcType=VARCHAR},
    </trim>
  </insert>
  <insert id="insertOnDuplicateUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.lanhu.lims.gateway.admin.model.AuditLog" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_audit_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="tableName != null">
        `table_name`,
      </if>
      <if test="recordId != null">
        record_id,
      </if>
      <if test="operation != null">
        `operation`,
      </if>
      <if test="oldValue != null">
        old_value,
      </if>
      <if test="newValue != null">
        new_value,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createName != null">
        create_name,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="tableName != null">
        #{tableName,jdbcType=VARCHAR},
      </if>
      <if test="recordId != null">
        #{recordId,jdbcType=BIGINT},
      </if>
      <if test="operation != null">
        #{operation,jdbcType=VARCHAR},
      </if>
      <if test="oldValue != null">
        #{oldValue,jdbcType=LONGVARCHAR},
      </if>
      <if test="newValue != null">
        #{newValue,jdbcType=LONGVARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=INTEGER},
      </if>
      <if test="tableName != null">
        `table_name` = #{tableName,jdbcType=VARCHAR},
      </if>
      <if test="recordId != null">
        record_id = #{recordId,jdbcType=BIGINT},
      </if>
      <if test="operation != null">
        `operation` = #{operation,jdbcType=VARCHAR},
      </if>
      <if test="oldValue != null">
        old_value = #{oldValue,jdbcType=LONGVARCHAR},
      </if>
      <if test="newValue != null">
        new_value = #{newValue,jdbcType=LONGVARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>