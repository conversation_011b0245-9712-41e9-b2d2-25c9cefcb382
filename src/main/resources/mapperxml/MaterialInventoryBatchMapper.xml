<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanhu.lims.gateway.admin.mapper.MaterialInventoryBatchMapper">
    <resultMap id="BaseResultMap" type="com.lanhu.lims.gateway.admin.model.MaterialInventoryBatch">
        <!--@mbg.generated-->
        <!--@Table t_material_inventory_batch-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="batch_code" jdbcType="VARCHAR" property="batchCode"/>
        <result column="material_name" jdbcType="VARCHAR" property="materialName"/>
        <result column="material_type" jdbcType="INTEGER" property="materialType"/>
        <result column="operation_type" jdbcType="INTEGER" property="operationType"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="is_effect" jdbcType="INTEGER" property="isEffect"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_name" jdbcType="VARCHAR" property="updateName"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="audit_status" jdbcType="INTEGER" property="auditStatus"/>
        <result column="audit_by" jdbcType="BIGINT" property="auditBy"/>
        <result column="audit_name" jdbcType="VARCHAR" property="auditName"/>
        <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime"/>
        <result column="audit_remark" jdbcType="LONGVARCHAR" property="auditRemark"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, batch_code, material_name, material_type, operation_type, remark, is_effect,
        create_by, create_name, create_time, update_by, update_name, update_time, audit_status,
        audit_by, audit_name, audit_time, audit_remark
    </sql>
</mapper>