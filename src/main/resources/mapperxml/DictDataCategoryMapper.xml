<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanhu.lims.gateway.admin.mapper.DictDataCategoryMapper">
    <resultMap id="BaseResultMap" type="com.lanhu.lims.gateway.admin.model.DictDataCategory">
        <!--@mbg.generated-->
        <!--@Table t_dict_data_category-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="is_effect" jdbcType="INTEGER" property="isEffect"/>
        <result column="ord" jdbcType="INTEGER" property="ord"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, `name`, is_effect, ord
    </sql>
</mapper>