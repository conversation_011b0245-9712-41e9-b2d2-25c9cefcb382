<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanhu.lims.gateway.admin.mapper.AdminUserMapper">
  <resultMap id="BaseResultMap" type="com.lanhu.lims.gateway.admin.model.AdminUser">
    <!--@mbg.generated-->
    <!--@Table t_admin_user-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="nick_name" jdbcType="VARCHAR" property="nickName" />
    <result column="note" jdbcType="VARCHAR" property="note" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="login_time" jdbcType="TIMESTAMP" property="loginTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="real_name" jdbcType="VARCHAR" property="realName" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="is_effect" jdbcType="INTEGER" property="isEffect" />
    <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
    <result column="work_status" jdbcType="INTEGER" property="workStatus" />
    <result column="service_date" jdbcType="VARCHAR" property="serviceDate" />
    <result column="termination_date" jdbcType="VARCHAR" property="terminationDate" />
    <result column="substitute_user_id" jdbcType="BIGINT" property="substituteUserId" />
    <result column="substitute_user_name" jdbcType="VARCHAR" property="substituteUserName" />
    <result column="inspect_item" jdbcType="VARCHAR" property="inspectItem" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="gender" jdbcType="INTEGER" property="gender" />
    <result column="absence_start_date" jdbcType="VARCHAR" property="absenceStartDate" />
    <result column="absence_end_date" jdbcType="VARCHAR" property="absenceEndDate" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, user_name, `password`, icon, email, nick_name, note, create_time, login_time, 
    `status`, real_name, mobile, update_time, create_by, update_by, is_effect, dept_id, 
    work_status, service_date, termination_date, substitute_user_id, substitute_user_name, 
    inspect_item, employee_id, gender, absence_start_date, absence_end_date
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_admin_user
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="user_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.userName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`password` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.password,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="icon = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.icon,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="email = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.email,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="nick_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.nickName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="note = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.note,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="login_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.loginTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.status,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="real_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.realName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="mobile = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.mobile,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createBy,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="update_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateBy,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="is_effect = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.isEffect,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="dept_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.deptId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="work_status = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.workStatus,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="service_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.serviceDate,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="termination_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.terminationDate,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="substitute_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.substituteUserId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="substitute_user_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.substituteUserName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="inspect_item = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.inspectItem,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="employee_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.employeeId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="gender = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.gender,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="absence_start_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.absenceStartDate,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="absence_end_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.absenceEndDate,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_admin_user
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="user_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.userName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.userName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`password` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.password != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.password,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="icon = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.icon != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.icon,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="email = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.email != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.email,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="nick_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.nickName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.nickName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="note = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.note != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.note,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="login_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.loginTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.loginTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.status != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.status,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="real_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.realName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.realName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="mobile = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.mobile != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.mobile,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createBy,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateBy,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="is_effect = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isEffect != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.isEffect,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="dept_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deptId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.deptId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="work_status = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.workStatus != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.workStatus,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="service_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.serviceDate != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.serviceDate,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="termination_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.terminationDate != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.terminationDate,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="substitute_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.substituteUserId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.substituteUserId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="substitute_user_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.substituteUserName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.substituteUserName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="inspect_item = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.inspectItem != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.inspectItem,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="employee_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.employeeId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.employeeId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="gender = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.gender != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.gender,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="absence_start_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.absenceStartDate != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.absenceStartDate,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="absence_end_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.absenceEndDate != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.absenceEndDate,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into t_admin_user
    (id, user_name, `password`, icon, email, nick_name, note, create_time, login_time, 
      `status`, real_name, mobile, update_time, create_by, update_by, is_effect, dept_id, 
      work_status, service_date, termination_date, substitute_user_id, substitute_user_name, 
      inspect_item, employee_id, gender, absence_start_date, absence_end_date)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.userName,jdbcType=VARCHAR}, #{item.password,jdbcType=VARCHAR}, 
        #{item.icon,jdbcType=VARCHAR}, #{item.email,jdbcType=VARCHAR}, #{item.nickName,jdbcType=VARCHAR}, 
        #{item.note,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.loginTime,jdbcType=TIMESTAMP}, 
        #{item.status,jdbcType=INTEGER}, #{item.realName,jdbcType=VARCHAR}, #{item.mobile,jdbcType=VARCHAR}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.createBy,jdbcType=BIGINT}, #{item.updateBy,jdbcType=BIGINT}, 
        #{item.isEffect,jdbcType=INTEGER}, #{item.deptId,jdbcType=VARCHAR}, #{item.workStatus,jdbcType=INTEGER}, 
        #{item.serviceDate,jdbcType=VARCHAR}, #{item.terminationDate,jdbcType=VARCHAR}, 
        #{item.substituteUserId,jdbcType=BIGINT}, #{item.substituteUserName,jdbcType=VARCHAR}, 
        #{item.inspectItem,jdbcType=VARCHAR}, #{item.employeeId,jdbcType=VARCHAR}, #{item.gender,jdbcType=INTEGER}, 
        #{item.absenceStartDate,jdbcType=VARCHAR}, #{item.absenceEndDate,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="insertOrUpdate" parameterType="com.lanhu.lims.gateway.admin.model.AdminUser">
    <!--@mbg.generated-->
    insert into t_admin_user
    (id, user_name, `password`, icon, email, nick_name, note, create_time, login_time, 
      `status`, real_name, mobile, update_time, create_by, update_by, is_effect, dept_id, 
      work_status, service_date, termination_date, substitute_user_id, substitute_user_name, 
      inspect_item, employee_id, gender, absence_start_date, absence_end_date)
    values
    (#{id,jdbcType=BIGINT}, #{userName,jdbcType=VARCHAR}, #{password,jdbcType=VARCHAR}, 
      #{icon,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, #{nickName,jdbcType=VARCHAR}, 
      #{note,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{loginTime,jdbcType=TIMESTAMP}, 
      #{status,jdbcType=INTEGER}, #{realName,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT}, 
      #{isEffect,jdbcType=INTEGER}, #{deptId,jdbcType=VARCHAR}, #{workStatus,jdbcType=INTEGER}, 
      #{serviceDate,jdbcType=VARCHAR}, #{terminationDate,jdbcType=VARCHAR}, #{substituteUserId,jdbcType=BIGINT}, 
      #{substituteUserName,jdbcType=VARCHAR}, #{inspectItem,jdbcType=VARCHAR}, #{employeeId,jdbcType=VARCHAR}, 
      #{gender,jdbcType=INTEGER}, #{absenceStartDate,jdbcType=VARCHAR}, #{absenceEndDate,jdbcType=VARCHAR}
      )
    on duplicate key update 
    id = #{id,jdbcType=BIGINT}, 
    user_name = #{userName,jdbcType=VARCHAR}, 
    `password` = #{password,jdbcType=VARCHAR}, 
    icon = #{icon,jdbcType=VARCHAR}, 
    email = #{email,jdbcType=VARCHAR}, 
    nick_name = #{nickName,jdbcType=VARCHAR}, 
    note = #{note,jdbcType=VARCHAR}, 
    create_time = #{createTime,jdbcType=TIMESTAMP}, 
    login_time = #{loginTime,jdbcType=TIMESTAMP}, 
    `status` = #{status,jdbcType=INTEGER}, 
    real_name = #{realName,jdbcType=VARCHAR}, 
    mobile = #{mobile,jdbcType=VARCHAR}, 
    update_time = #{updateTime,jdbcType=TIMESTAMP}, 
    create_by = #{createBy,jdbcType=BIGINT}, 
    update_by = #{updateBy,jdbcType=BIGINT}, 
    is_effect = #{isEffect,jdbcType=INTEGER}, 
    dept_id = #{deptId,jdbcType=VARCHAR}, 
    work_status = #{workStatus,jdbcType=INTEGER}, 
    service_date = #{serviceDate,jdbcType=VARCHAR}, 
    termination_date = #{terminationDate,jdbcType=VARCHAR}, 
    substitute_user_id = #{substituteUserId,jdbcType=BIGINT}, 
    substitute_user_name = #{substituteUserName,jdbcType=VARCHAR}, 
    inspect_item = #{inspectItem,jdbcType=VARCHAR}, 
    employee_id = #{employeeId,jdbcType=VARCHAR}, 
    gender = #{gender,jdbcType=INTEGER}, 
    absence_start_date = #{absenceStartDate,jdbcType=VARCHAR}, 
    absence_end_date = #{absenceEndDate,jdbcType=VARCHAR}
  </insert>
  <insert id="insertOrUpdateSelective" parameterType="com.lanhu.lims.gateway.admin.model.AdminUser">
    <!--@mbg.generated-->
    insert into t_admin_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="password != null">
        `password`,
      </if>
      <if test="icon != null">
        icon,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="nickName != null">
        nick_name,
      </if>
      <if test="note != null">
        note,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="loginTime != null">
        login_time,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="realName != null">
        real_name,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="isEffect != null">
        is_effect,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="workStatus != null">
        work_status,
      </if>
      <if test="serviceDate != null">
        service_date,
      </if>
      <if test="terminationDate != null">
        termination_date,
      </if>
      <if test="substituteUserId != null">
        substitute_user_id,
      </if>
      <if test="substituteUserName != null">
        substitute_user_name,
      </if>
      <if test="inspectItem != null">
        inspect_item,
      </if>
      <if test="employeeId != null">
        employee_id,
      </if>
      <if test="gender != null">
        gender,
      </if>
      <if test="absenceStartDate != null">
        absence_start_date,
      </if>
      <if test="absenceEndDate != null">
        absence_end_date,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="icon != null">
        #{icon,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="nickName != null">
        #{nickName,jdbcType=VARCHAR},
      </if>
      <if test="note != null">
        #{note,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="loginTime != null">
        #{loginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="realName != null">
        #{realName,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="isEffect != null">
        #{isEffect,jdbcType=INTEGER},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=VARCHAR},
      </if>
      <if test="workStatus != null">
        #{workStatus,jdbcType=INTEGER},
      </if>
      <if test="serviceDate != null">
        #{serviceDate,jdbcType=VARCHAR},
      </if>
      <if test="terminationDate != null">
        #{terminationDate,jdbcType=VARCHAR},
      </if>
      <if test="substituteUserId != null">
        #{substituteUserId,jdbcType=BIGINT},
      </if>
      <if test="substituteUserName != null">
        #{substituteUserName,jdbcType=VARCHAR},
      </if>
      <if test="inspectItem != null">
        #{inspectItem,jdbcType=VARCHAR},
      </if>
      <if test="employeeId != null">
        #{employeeId,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=INTEGER},
      </if>
      <if test="absenceStartDate != null">
        #{absenceStartDate,jdbcType=VARCHAR},
      </if>
      <if test="absenceEndDate != null">
        #{absenceEndDate,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        `password` = #{password,jdbcType=VARCHAR},
      </if>
      <if test="icon != null">
        icon = #{icon,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="nickName != null">
        nick_name = #{nickName,jdbcType=VARCHAR},
      </if>
      <if test="note != null">
        note = #{note,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="loginTime != null">
        login_time = #{loginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="realName != null">
        real_name = #{realName,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="isEffect != null">
        is_effect = #{isEffect,jdbcType=INTEGER},
      </if>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=VARCHAR},
      </if>
      <if test="workStatus != null">
        work_status = #{workStatus,jdbcType=INTEGER},
      </if>
      <if test="serviceDate != null">
        service_date = #{serviceDate,jdbcType=VARCHAR},
      </if>
      <if test="terminationDate != null">
        termination_date = #{terminationDate,jdbcType=VARCHAR},
      </if>
      <if test="substituteUserId != null">
        substitute_user_id = #{substituteUserId,jdbcType=BIGINT},
      </if>
      <if test="substituteUserName != null">
        substitute_user_name = #{substituteUserName,jdbcType=VARCHAR},
      </if>
      <if test="inspectItem != null">
        inspect_item = #{inspectItem,jdbcType=VARCHAR},
      </if>
      <if test="employeeId != null">
        employee_id = #{employeeId,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        gender = #{gender,jdbcType=INTEGER},
      </if>
      <if test="absenceStartDate != null">
        absence_start_date = #{absenceStartDate,jdbcType=VARCHAR},
      </if>
      <if test="absenceEndDate != null">
        absence_end_date = #{absenceEndDate,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>