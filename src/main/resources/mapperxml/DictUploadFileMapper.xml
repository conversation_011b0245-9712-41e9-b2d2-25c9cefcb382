<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanhu.lims.gateway.admin.mapper.DictUploadFileMapper">
  <resultMap id="BaseResultMap" type="com.lanhu.lims.gateway.admin.model.DictUploadFile">
    <!--@mbg.generated-->
    <!--@Table t_dict_upload_file-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="file_type" jdbcType="VARCHAR" property="fileType" />
    <result column="file_size" jdbcType="INTEGER" property="fileSize" />
    <result column="file_max" jdbcType="INTEGER" property="fileMax" />
    <result column="file_suffix" jdbcType="VARCHAR" property="fileSuffix" />
    <result column="file_upload_dir" jdbcType="VARCHAR" property="fileUploadDir" />
    <result column="file_acl" jdbcType="INTEGER" property="fileAcl" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, file_type, file_size, file_max, file_suffix, file_upload_dir, file_acl, url, 
    remark
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_dict_upload_file
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="file_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.fileType,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="file_size = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.fileSize,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="file_max = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.fileMax,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="file_suffix = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.fileSuffix,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="file_upload_dir = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.fileUploadDir,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="file_acl = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.fileAcl,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="url = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.url,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.remark,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_dict_upload_file
    (file_type, file_size, file_max, file_suffix, file_upload_dir, file_acl, url, remark
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.fileType,jdbcType=VARCHAR}, #{item.fileSize,jdbcType=INTEGER}, #{item.fileMax,jdbcType=INTEGER}, 
        #{item.fileSuffix,jdbcType=VARCHAR}, #{item.fileUploadDir,jdbcType=VARCHAR}, #{item.fileAcl,jdbcType=INTEGER}, 
        #{item.url,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.lanhu.lims.gateway.admin.model.DictUploadFile" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_dict_upload_file
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      file_type,
      file_size,
      file_max,
      file_suffix,
      file_upload_dir,
      file_acl,
      url,
      remark,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{fileType,jdbcType=VARCHAR},
      #{fileSize,jdbcType=INTEGER},
      #{fileMax,jdbcType=INTEGER},
      #{fileSuffix,jdbcType=VARCHAR},
      #{fileUploadDir,jdbcType=VARCHAR},
      #{fileAcl,jdbcType=INTEGER},
      #{url,jdbcType=VARCHAR},
      #{remark,jdbcType=VARCHAR},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      file_type = #{fileType,jdbcType=VARCHAR},
      file_size = #{fileSize,jdbcType=INTEGER},
      file_max = #{fileMax,jdbcType=INTEGER},
      file_suffix = #{fileSuffix,jdbcType=VARCHAR},
      file_upload_dir = #{fileUploadDir,jdbcType=VARCHAR},
      file_acl = #{fileAcl,jdbcType=INTEGER},
      url = #{url,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.lanhu.lims.gateway.admin.model.DictUploadFile" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_dict_upload_file
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fileType != null">
        file_type,
      </if>
      <if test="fileSize != null">
        file_size,
      </if>
      <if test="fileMax != null">
        file_max,
      </if>
      <if test="fileSuffix != null">
        file_suffix,
      </if>
      <if test="fileUploadDir != null">
        file_upload_dir,
      </if>
      <if test="fileAcl != null">
        file_acl,
      </if>
      <if test="url != null">
        url,
      </if>
      <if test="remark != null">
        remark,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fileType != null">
        #{fileType,jdbcType=VARCHAR},
      </if>
      <if test="fileSize != null">
        #{fileSize,jdbcType=INTEGER},
      </if>
      <if test="fileMax != null">
        #{fileMax,jdbcType=INTEGER},
      </if>
      <if test="fileSuffix != null">
        #{fileSuffix,jdbcType=VARCHAR},
      </if>
      <if test="fileUploadDir != null">
        #{fileUploadDir,jdbcType=VARCHAR},
      </if>
      <if test="fileAcl != null">
        #{fileAcl,jdbcType=INTEGER},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="fileType != null">
        file_type = #{fileType,jdbcType=VARCHAR},
      </if>
      <if test="fileSize != null">
        file_size = #{fileSize,jdbcType=INTEGER},
      </if>
      <if test="fileMax != null">
        file_max = #{fileMax,jdbcType=INTEGER},
      </if>
      <if test="fileSuffix != null">
        file_suffix = #{fileSuffix,jdbcType=VARCHAR},
      </if>
      <if test="fileUploadDir != null">
        file_upload_dir = #{fileUploadDir,jdbcType=VARCHAR},
      </if>
      <if test="fileAcl != null">
        file_acl = #{fileAcl,jdbcType=INTEGER},
      </if>
      <if test="url != null">
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="selectByFileType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_dict_upload_file
    where file_type = #{fileType,jdbcType=VARCHAR}
  </select>

</mapper>