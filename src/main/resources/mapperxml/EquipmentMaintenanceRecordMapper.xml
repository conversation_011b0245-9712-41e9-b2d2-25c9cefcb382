<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanhu.lims.gateway.admin.mapper.EquipmentMaintenanceRecordMapper">
  <resultMap id="BaseResultMap" type="com.lanhu.lims.gateway.admin.model.EquipmentMaintenanceRecord">
    <!--@mbg.generated-->
    <!--@Table t_equipment_maintenance_record-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="equipment_id" jdbcType="BIGINT" property="equipmentId" />
    <result column="maintainer_id" jdbcType="BIGINT" property="maintainerId" />
    <result column="maintainer_name" jdbcType="VARCHAR" property="maintainerName" />
    <result column="maintenance_content" jdbcType="VARCHAR" property="maintenanceContent" />
    <result column="maintenance_time" jdbcType="TIMESTAMP" property="maintenanceTime" />
    <result column="next_maintenance_time" jdbcType="TIMESTAMP" property="nextMaintenanceTime" />
    <result column="is_effect" jdbcType="INTEGER" property="isEffect" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, equipment_id, maintainer_id, maintainer_name, maintenance_content, maintenance_time, 
    next_maintenance_time, is_effect, create_by, create_name, create_time, update_by, 
    update_name, update_time
  </sql>
</mapper>