<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanhu.lims.gateway.admin.mapper.ConsumableInventoryChangeRecordMapper">
    <resultMap id="BaseResultMap" type="com.lanhu.lims.gateway.admin.model.ConsumableInventoryChangeRecord">
        <!--@mbg.generated-->
        <!--@Table t_consumable_inventory_change_record-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="inventory_id" jdbcType="BIGINT" property="inventoryId"/>
        <result column="operation_type" jdbcType="INTEGER" property="operationType"/>
        <result column="change_inventory" jdbcType="DECIMAL" property="changeInventory"/>
        <result column="before_inventory" jdbcType="DECIMAL" property="beforeInventory"/>
        <result column="after_inventory" jdbcType="DECIMAL" property="afterInventory"/>
        <result column="remark" jdbcType="LONGVARCHAR" property="remark"/>
        <result column="is_effect" jdbcType="INTEGER" property="isEffect"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_name" jdbcType="VARCHAR" property="updateName"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="audit_status" jdbcType="INTEGER" property="auditStatus"/>
        <result column="audit_by" jdbcType="BIGINT" property="auditBy"/>
        <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime"/>
        <result column="audit_name" jdbcType="VARCHAR" property="auditName"/>
        <result column="audit_remark" jdbcType="LONGVARCHAR" property="auditRemark"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, order_id, inventory_id, operation_type, change_inventory, before_inventory, after_inventory,
        remark, is_effect, create_by, create_name, create_time, update_by, update_name, update_time,
        audit_status, audit_by, audit_time, audit_name, audit_remark
    </sql>
</mapper>