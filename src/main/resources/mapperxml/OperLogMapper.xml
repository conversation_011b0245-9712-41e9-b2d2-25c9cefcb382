<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanhu.lims.gateway.admin.mapper.OperLogMapper">
  <resultMap id="BaseResultMap" type="com.lanhu.lims.gateway.admin.model.OperLog">
    <!--@mbg.generated-->
    <!--@Table t_oper_log-->
    <id column="oper_id" jdbcType="BIGINT" property="operId" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="method" jdbcType="VARCHAR" property="method" />
    <result column="oper_name" jdbcType="VARCHAR" property="operName" />
    <result column="oper_ip" jdbcType="VARCHAR" property="operIp" />
    <result column="oper_param" jdbcType="VARCHAR" property="operParam" />
    <result column="json_result" jdbcType="VARCHAR" property="jsonResult" />
    <result column="oper_time" jdbcType="TIMESTAMP" property="operTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="error_msg" jdbcType="VARCHAR" property="errorMsg" />
    <result column="oper_url" jdbcType="VARCHAR" property="operUrl" />
    <result column="oper_type" jdbcType="VARCHAR" property="operType" />
    <result column="request_method" jdbcType="VARCHAR" property="requestMethod" />
    <result column="business_type" jdbcType="VARCHAR" property="businessType" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    oper_id, title, `method`, oper_name, oper_ip, oper_param, json_result, oper_time, 
    `status`, error_msg, oper_url, oper_type, request_method, business_type
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_oper_log
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="title = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when oper_id = #{item.operId,jdbcType=BIGINT} then #{item.title,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`method` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when oper_id = #{item.operId,jdbcType=BIGINT} then #{item.method,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="oper_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when oper_id = #{item.operId,jdbcType=BIGINT} then #{item.operName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="oper_ip = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when oper_id = #{item.operId,jdbcType=BIGINT} then #{item.operIp,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="oper_param = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when oper_id = #{item.operId,jdbcType=BIGINT} then #{item.operParam,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="json_result = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when oper_id = #{item.operId,jdbcType=BIGINT} then #{item.jsonResult,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="oper_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when oper_id = #{item.operId,jdbcType=BIGINT} then #{item.operTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when oper_id = #{item.operId,jdbcType=BIGINT} then #{item.status,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="error_msg = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when oper_id = #{item.operId,jdbcType=BIGINT} then #{item.errorMsg,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="oper_url = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when oper_id = #{item.operId,jdbcType=BIGINT} then #{item.operUrl,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="oper_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when oper_id = #{item.operId,jdbcType=BIGINT} then #{item.operType,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="request_method = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when oper_id = #{item.operId,jdbcType=BIGINT} then #{item.requestMethod,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="business_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when oper_id = #{item.operId,jdbcType=BIGINT} then #{item.businessType,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where oper_id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.operId,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_oper_log
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="title = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.title != null">
            when oper_id = #{item.operId,jdbcType=BIGINT} then #{item.title,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`method` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.method != null">
            when oper_id = #{item.operId,jdbcType=BIGINT} then #{item.method,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="oper_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.operName != null">
            when oper_id = #{item.operId,jdbcType=BIGINT} then #{item.operName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="oper_ip = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.operIp != null">
            when oper_id = #{item.operId,jdbcType=BIGINT} then #{item.operIp,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="oper_param = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.operParam != null">
            when oper_id = #{item.operId,jdbcType=BIGINT} then #{item.operParam,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="json_result = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.jsonResult != null">
            when oper_id = #{item.operId,jdbcType=BIGINT} then #{item.jsonResult,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="oper_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.operTime != null">
            when oper_id = #{item.operId,jdbcType=BIGINT} then #{item.operTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.status != null">
            when oper_id = #{item.operId,jdbcType=BIGINT} then #{item.status,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="error_msg = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.errorMsg != null">
            when oper_id = #{item.operId,jdbcType=BIGINT} then #{item.errorMsg,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="oper_url = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.operUrl != null">
            when oper_id = #{item.operId,jdbcType=BIGINT} then #{item.operUrl,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="oper_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.operType != null">
            when oper_id = #{item.operId,jdbcType=BIGINT} then #{item.operType,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="request_method = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.requestMethod != null">
            when oper_id = #{item.operId,jdbcType=BIGINT} then #{item.requestMethod,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="business_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.businessType != null">
            when oper_id = #{item.operId,jdbcType=BIGINT} then #{item.businessType,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where oper_id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.operId,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="oper_id" keyProperty="operId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_oper_log
    (title, `method`, oper_name, oper_ip, oper_param, json_result, oper_time, `status`, 
      error_msg, oper_url, oper_type, request_method, business_type)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.title,jdbcType=VARCHAR}, #{item.method,jdbcType=VARCHAR}, #{item.operName,jdbcType=VARCHAR}, 
        #{item.operIp,jdbcType=VARCHAR}, #{item.operParam,jdbcType=VARCHAR}, #{item.jsonResult,jdbcType=VARCHAR}, 
        #{item.operTime,jdbcType=TIMESTAMP}, #{item.status,jdbcType=INTEGER}, #{item.errorMsg,jdbcType=VARCHAR}, 
        #{item.operUrl,jdbcType=VARCHAR}, #{item.operType,jdbcType=VARCHAR}, #{item.requestMethod,jdbcType=VARCHAR}, 
        #{item.businessType,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertOrUpdate" keyColumn="oper_id" keyProperty="operId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_oper_log
    (title, `method`, oper_name, oper_ip, oper_param, json_result, oper_time, `status`, 
      error_msg, oper_url, oper_type, request_method, business_type)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.title,jdbcType=VARCHAR}, #{item.method,jdbcType=VARCHAR}, #{item.operName,jdbcType=VARCHAR}, 
        #{item.operIp,jdbcType=VARCHAR}, #{item.operParam,jdbcType=VARCHAR}, #{item.jsonResult,jdbcType=VARCHAR}, 
        #{item.operTime,jdbcType=TIMESTAMP}, #{item.status,jdbcType=INTEGER}, #{item.errorMsg,jdbcType=VARCHAR}, 
        #{item.operUrl,jdbcType=VARCHAR}, #{item.operType,jdbcType=VARCHAR}, #{item.requestMethod,jdbcType=VARCHAR}, 
        #{item.businessType,jdbcType=VARCHAR})
    </foreach>
    on duplicate key update 
    title=values(title),
    method=values(method),
    oper_name=values(oper_name),
    oper_ip=values(oper_ip),
    oper_param=values(oper_param),
    json_result=values(json_result),
    oper_time=values(oper_time),
    status=values(status),
    error_msg=values(error_msg),
    oper_url=values(oper_url),
    oper_type=values(oper_type),
    request_method=values(request_method),
    business_type=values(business_type)
  </insert>
  <insert id="insertOnDuplicateUpdate" keyColumn="oper_id" keyProperty="operId" parameterType="com.lanhu.lims.gateway.admin.model.OperLog" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_oper_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="operId != null">
        oper_id,
      </if>
      title,
      `method`,
      oper_name,
      oper_ip,
      oper_param,
      json_result,
      oper_time,
      `status`,
      error_msg,
      oper_url,
      oper_type,
      request_method,
      business_type,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="operId != null">
        #{operId,jdbcType=BIGINT},
      </if>
      #{title,jdbcType=VARCHAR},
      #{method,jdbcType=VARCHAR},
      #{operName,jdbcType=VARCHAR},
      #{operIp,jdbcType=VARCHAR},
      #{operParam,jdbcType=VARCHAR},
      #{jsonResult,jdbcType=VARCHAR},
      #{operTime,jdbcType=TIMESTAMP},
      #{status,jdbcType=INTEGER},
      #{errorMsg,jdbcType=VARCHAR},
      #{operUrl,jdbcType=VARCHAR},
      #{operType,jdbcType=VARCHAR},
      #{requestMethod,jdbcType=VARCHAR},
      #{businessType,jdbcType=VARCHAR},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="operId != null">
        oper_id = #{operId,jdbcType=BIGINT},
      </if>
      title = #{title,jdbcType=VARCHAR},
      `method` = #{method,jdbcType=VARCHAR},
      oper_name = #{operName,jdbcType=VARCHAR},
      oper_ip = #{operIp,jdbcType=VARCHAR},
      oper_param = #{operParam,jdbcType=VARCHAR},
      json_result = #{jsonResult,jdbcType=VARCHAR},
      oper_time = #{operTime,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=INTEGER},
      error_msg = #{errorMsg,jdbcType=VARCHAR},
      oper_url = #{operUrl,jdbcType=VARCHAR},
      oper_type = #{operType,jdbcType=VARCHAR},
      request_method = #{requestMethod,jdbcType=VARCHAR},
      business_type = #{businessType,jdbcType=VARCHAR},
    </trim>
  </insert>
  <insert id="insertOnDuplicateUpdateSelective" keyColumn="oper_id" keyProperty="operId" parameterType="com.lanhu.lims.gateway.admin.model.OperLog" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_oper_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="operId != null">
        oper_id,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="method != null">
        `method`,
      </if>
      <if test="operName != null">
        oper_name,
      </if>
      <if test="operIp != null">
        oper_ip,
      </if>
      <if test="operParam != null">
        oper_param,
      </if>
      <if test="jsonResult != null">
        json_result,
      </if>
      <if test="operTime != null">
        oper_time,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="errorMsg != null">
        error_msg,
      </if>
      <if test="operUrl != null">
        oper_url,
      </if>
      <if test="operType != null">
        oper_type,
      </if>
      <if test="requestMethod != null">
        request_method,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="operId != null">
        #{operId,jdbcType=BIGINT},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="method != null">
        #{method,jdbcType=VARCHAR},
      </if>
      <if test="operName != null">
        #{operName,jdbcType=VARCHAR},
      </if>
      <if test="operIp != null">
        #{operIp,jdbcType=VARCHAR},
      </if>
      <if test="operParam != null">
        #{operParam,jdbcType=VARCHAR},
      </if>
      <if test="jsonResult != null">
        #{jsonResult,jdbcType=VARCHAR},
      </if>
      <if test="operTime != null">
        #{operTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="errorMsg != null">
        #{errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="operUrl != null">
        #{operUrl,jdbcType=VARCHAR},
      </if>
      <if test="operType != null">
        #{operType,jdbcType=VARCHAR},
      </if>
      <if test="requestMethod != null">
        #{requestMethod,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="operId != null">
        oper_id = #{operId,jdbcType=BIGINT},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="method != null">
        `method` = #{method,jdbcType=VARCHAR},
      </if>
      <if test="operName != null">
        oper_name = #{operName,jdbcType=VARCHAR},
      </if>
      <if test="operIp != null">
        oper_ip = #{operIp,jdbcType=VARCHAR},
      </if>
      <if test="operParam != null">
        oper_param = #{operParam,jdbcType=VARCHAR},
      </if>
      <if test="jsonResult != null">
        json_result = #{jsonResult,jdbcType=VARCHAR},
      </if>
      <if test="operTime != null">
        oper_time = #{operTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="errorMsg != null">
        error_msg = #{errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="operUrl != null">
        oper_url = #{operUrl,jdbcType=VARCHAR},
      </if>
      <if test="operType != null">
        oper_type = #{operType,jdbcType=VARCHAR},
      </if>
      <if test="requestMethod != null">
        request_method = #{requestMethod,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>