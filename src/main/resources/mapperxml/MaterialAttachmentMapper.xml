<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanhu.lims.gateway.admin.mapper.MaterialAttachmentMapper">
    <resultMap id="BaseResultMap" type="com.lanhu.lims.gateway.admin.model.MaterialAttachment">
        <!--@mbg.generated-->
        <!--@Table t_material_attachment-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="material_id" jdbcType="BIGINT" property="materialId"/>
        <result column="material_type" jdbcType="INTEGER" property="materialType"/>
        <result column="file_id" jdbcType="BIGINT" property="fileId"/>
        <result column="attachment_name" jdbcType="VARCHAR" property="attachmentName"/>
        <result column="attachment_size" jdbcType="BIGINT" property="attachmentSize"/>
        <result column="attachment_url" jdbcType="VARCHAR" property="attachmentUrl"/>
        <result column="attachment_suffix" jdbcType="VARCHAR" property="attachmentSuffix"/>
        <result column="is_effect" jdbcType="INTEGER" property="isEffect"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_name" jdbcType="VARCHAR" property="updateName"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, material_id, material_type, file_id, attachment_name, attachment_size, attachment_url,
        attachment_suffix, is_effect, create_by, create_name, create_time, update_by, update_name,
        update_time
    </sql>
</mapper>