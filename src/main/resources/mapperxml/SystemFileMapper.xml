<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanhu.lims.gateway.admin.mapper.SystemFileMapper">
    <resultMap id="BaseResultMap" type="com.lanhu.lims.gateway.admin.model.SystemFile">
        <!--@mbg.generated-->
        <!--@Table t_system_file-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="file_code" jdbcType="VARCHAR" property="fileCode"/>
        <result column="file_name" jdbcType="VARCHAR" property="fileName"/>
        <result column="file_type_id" jdbcType="BIGINT" property="fileTypeId"/>
        <result column="file_url" jdbcType="VARCHAR" property="fileUrl"/>
        <result column="version" jdbcType="VARCHAR" property="version"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="is_effect" jdbcType="INTEGER" property="isEffect"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_name" jdbcType="VARCHAR" property="updateName"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="audit_status" jdbcType="INTEGER" property="auditStatus"/>
        <result column="audit_by" jdbcType="BIGINT" property="auditBy"/>
        <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime"/>
        <result column="audit_name" jdbcType="VARCHAR" property="auditName"/>
        <result column="audit_remark" jdbcType="LONGVARCHAR" property="auditRemark"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, file_code, file_name, file_type_id, file_url, version, remark, is_effect, create_by, create_name,
        create_time, update_by, update_name, update_time, audit_status, audit_by, audit_time,
        audit_name, audit_remark
    </sql>
</mapper>