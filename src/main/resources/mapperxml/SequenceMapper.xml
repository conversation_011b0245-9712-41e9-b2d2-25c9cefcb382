<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanhu.lims.gateway.admin.mapper.SequenceMapper">
    <resultMap id="BaseResultMap" type="com.lanhu.lims.gateway.admin.model.Sequence">
        <!--@mbg.generated-->
        <!--@Table t_sequence-->
        <id column="name" jdbcType="VARCHAR" property="name"/>
        <result column="current_value" jdbcType="INTEGER" property="currentValue"/>
        <result column="increment" jdbcType="INTEGER" property="increment"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        `name`, current_value, `increment`
    </sql>

    <select id="selectByName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_sequence
        where `name` = #{name}
        for update
    </select>
</mapper>