<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanhu.lims.gateway.admin.mapper.BusinessFlowApplicationMapper">
  <resultMap id="BaseResultMap" type="com.lanhu.lims.gateway.admin.model.BusinessFlowApplication">
    <!--@mbg.generated-->
    <!--@Table t_business_flow_application-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="bus_type" jdbcType="INTEGER" property="busType" />
    <result column="before_data" jdbcType="LONGVARCHAR" property="beforeData" />
    <result column="change_data" jdbcType="LONGVARCHAR" property="changeData" />
    <result column="after_data" jdbcType="LONGVARCHAR" property="afterData" />
    <result column="instance_id" jdbcType="BIGINT" property="instanceId" />
    <result column="node_code" jdbcType="VARCHAR" property="nodeCode" />
    <result column="node_name" jdbcType="VARCHAR" property="nodeName" />
    <result column="node_type" jdbcType="INTEGER" property="nodeType" />
    <result column="flow_status" jdbcType="INTEGER" property="flowStatus" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="is_effect" jdbcType="INTEGER" property="isEffect" />
    <result column="source_business_id" jdbcType="BIGINT" property="sourceBusinessId" />
    <result column="remark" jdbcType="LONGVARCHAR" property="remark" />
    <result column="reason" jdbcType="LONGVARCHAR" property="reason" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, bus_type, before_data, change_data, after_data, instance_id, node_code, node_name, 
    node_type, flow_status, create_by, create_time, create_name, update_by, update_time, 
    update_name, is_effect, source_business_id, remark, reason
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_business_flow_application
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="bus_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.busType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="before_data = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.beforeData,jdbcType=LONGVARCHAR}
        </foreach>
      </trim>
      <trim prefix="change_data = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.changeData,jdbcType=LONGVARCHAR}
        </foreach>
      </trim>
      <trim prefix="after_data = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.afterData,jdbcType=LONGVARCHAR}
        </foreach>
      </trim>
      <trim prefix="instance_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.instanceId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="node_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.nodeCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="node_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.nodeName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="node_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.nodeType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="flow_status = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.flowStatus,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createBy,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="create_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="update_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateBy,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="update_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="is_effect = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.isEffect,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="source_business_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.sourceBusinessId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.remark,jdbcType=LONGVARCHAR}
        </foreach>
      </trim>
      <trim prefix="reason = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.reason,jdbcType=LONGVARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_business_flow_application
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="bus_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.busType != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.busType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="before_data = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.beforeData != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.beforeData,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="change_data = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.changeData != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.changeData,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="after_data = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.afterData != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.afterData,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="instance_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.instanceId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.instanceId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="node_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.nodeCode != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.nodeCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="node_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.nodeName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.nodeName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="node_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.nodeType != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.nodeType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="flow_status = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.flowStatus != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.flowStatus,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createBy,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateBy,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="is_effect = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isEffect != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.isEffect,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="source_business_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.sourceBusinessId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.sourceBusinessId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.remark != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.remark,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="reason = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.reason != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.reason,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into t_business_flow_application
    (id, bus_type, before_data, change_data, after_data, instance_id, node_code, node_name, 
      node_type, flow_status, create_by, create_time, create_name, update_by, update_time, 
      update_name, is_effect, source_business_id, remark, reason)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.busType,jdbcType=INTEGER}, #{item.beforeData,jdbcType=LONGVARCHAR}, 
        #{item.changeData,jdbcType=LONGVARCHAR}, #{item.afterData,jdbcType=LONGVARCHAR}, 
        #{item.instanceId,jdbcType=BIGINT}, #{item.nodeCode,jdbcType=VARCHAR}, #{item.nodeName,jdbcType=VARCHAR}, 
        #{item.nodeType,jdbcType=INTEGER}, #{item.flowStatus,jdbcType=INTEGER}, #{item.createBy,jdbcType=BIGINT}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.createName,jdbcType=VARCHAR}, #{item.updateBy,jdbcType=BIGINT}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.updateName,jdbcType=VARCHAR}, #{item.isEffect,jdbcType=INTEGER}, 
        #{item.sourceBusinessId,jdbcType=BIGINT}, #{item.remark,jdbcType=LONGVARCHAR}, 
        #{item.reason,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertOrUpdate" parameterType="map">
    <!--@mbg.generated-->
    insert into t_business_flow_application
    (id, bus_type, before_data, change_data, after_data, instance_id, node_code, node_name, 
      node_type, flow_status, create_by, create_time, create_name, update_by, update_time, 
      update_name, is_effect, source_business_id, remark, reason)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.busType,jdbcType=INTEGER}, #{item.beforeData,jdbcType=LONGVARCHAR}, 
        #{item.changeData,jdbcType=LONGVARCHAR}, #{item.afterData,jdbcType=LONGVARCHAR}, 
        #{item.instanceId,jdbcType=BIGINT}, #{item.nodeCode,jdbcType=VARCHAR}, #{item.nodeName,jdbcType=VARCHAR}, 
        #{item.nodeType,jdbcType=INTEGER}, #{item.flowStatus,jdbcType=INTEGER}, #{item.createBy,jdbcType=BIGINT}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.createName,jdbcType=VARCHAR}, #{item.updateBy,jdbcType=BIGINT}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.updateName,jdbcType=VARCHAR}, #{item.isEffect,jdbcType=INTEGER}, 
        #{item.sourceBusinessId,jdbcType=BIGINT}, #{item.remark,jdbcType=LONGVARCHAR}, 
        #{item.reason,jdbcType=LONGVARCHAR})
    </foreach>
    on duplicate key update 
    id=values(id),
    bus_type=values(bus_type),
    before_data=values(before_data),
    change_data=values(change_data),
    after_data=values(after_data),
    instance_id=values(instance_id),
    node_code=values(node_code),
    node_name=values(node_name),
    node_type=values(node_type),
    flow_status=values(flow_status),
    create_by=values(create_by),
    create_time=values(create_time),
    create_name=values(create_name),
    update_by=values(update_by),
    update_time=values(update_time),
    update_name=values(update_name),
    is_effect=values(is_effect),
    source_business_id=values(source_business_id),
    remark=values(remark),
    reason=values(reason)
  </insert>
  <insert id="insertOnDuplicateUpdate" parameterType="com.lanhu.lims.gateway.admin.model.BusinessFlowApplication">
    <!--@mbg.generated-->
    insert into t_business_flow_application
    (id, bus_type, before_data, change_data, after_data, instance_id, node_code, node_name, 
      node_type, flow_status, create_by, create_time, create_name, update_by, update_time, 
      update_name, is_effect, source_business_id, remark, reason)
    values
    (#{id,jdbcType=BIGINT}, #{busType,jdbcType=INTEGER}, #{beforeData,jdbcType=LONGVARCHAR}, 
      #{changeData,jdbcType=LONGVARCHAR}, #{afterData,jdbcType=LONGVARCHAR}, #{instanceId,jdbcType=BIGINT}, 
      #{nodeCode,jdbcType=VARCHAR}, #{nodeName,jdbcType=VARCHAR}, #{nodeType,jdbcType=INTEGER}, 
      #{flowStatus,jdbcType=INTEGER}, #{createBy,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, 
      #{createName,jdbcType=VARCHAR}, #{updateBy,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{updateName,jdbcType=VARCHAR}, #{isEffect,jdbcType=INTEGER}, #{sourceBusinessId,jdbcType=BIGINT}, 
      #{remark,jdbcType=LONGVARCHAR}, #{reason,jdbcType=LONGVARCHAR})
    on duplicate key update 
    id = #{id,jdbcType=BIGINT}, 
    bus_type = #{busType,jdbcType=INTEGER}, 
    before_data = #{beforeData,jdbcType=LONGVARCHAR}, 
    change_data = #{changeData,jdbcType=LONGVARCHAR}, 
    after_data = #{afterData,jdbcType=LONGVARCHAR}, 
    instance_id = #{instanceId,jdbcType=BIGINT}, 
    node_code = #{nodeCode,jdbcType=VARCHAR}, 
    node_name = #{nodeName,jdbcType=VARCHAR}, 
    node_type = #{nodeType,jdbcType=INTEGER}, 
    flow_status = #{flowStatus,jdbcType=INTEGER}, 
    create_by = #{createBy,jdbcType=BIGINT}, 
    create_time = #{createTime,jdbcType=TIMESTAMP}, 
    create_name = #{createName,jdbcType=VARCHAR}, 
    update_by = #{updateBy,jdbcType=BIGINT}, 
    update_time = #{updateTime,jdbcType=TIMESTAMP}, 
    update_name = #{updateName,jdbcType=VARCHAR}, 
    is_effect = #{isEffect,jdbcType=INTEGER}, 
    source_business_id = #{sourceBusinessId,jdbcType=BIGINT}, 
    remark = #{remark,jdbcType=LONGVARCHAR}, 
    reason = #{reason,jdbcType=LONGVARCHAR}
  </insert>
  <insert id="insertOnDuplicateUpdateSelective" parameterType="com.lanhu.lims.gateway.admin.model.BusinessFlowApplication">
    <!--@mbg.generated-->
    insert into t_business_flow_application
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="busType != null">
        bus_type,
      </if>
      <if test="beforeData != null">
        before_data,
      </if>
      <if test="changeData != null">
        change_data,
      </if>
      <if test="afterData != null">
        after_data,
      </if>
      <if test="instanceId != null">
        instance_id,
      </if>
      <if test="nodeCode != null">
        node_code,
      </if>
      <if test="nodeName != null">
        node_name,
      </if>
      <if test="nodeType != null">
        node_type,
      </if>
      <if test="flowStatus != null">
        flow_status,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="isEffect != null">
        is_effect,
      </if>
      <if test="sourceBusinessId != null">
        source_business_id,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="reason != null">
        reason,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="busType != null">
        #{busType,jdbcType=INTEGER},
      </if>
      <if test="beforeData != null">
        #{beforeData,jdbcType=LONGVARCHAR},
      </if>
      <if test="changeData != null">
        #{changeData,jdbcType=LONGVARCHAR},
      </if>
      <if test="afterData != null">
        #{afterData,jdbcType=LONGVARCHAR},
      </if>
      <if test="instanceId != null">
        #{instanceId,jdbcType=BIGINT},
      </if>
      <if test="nodeCode != null">
        #{nodeCode,jdbcType=VARCHAR},
      </if>
      <if test="nodeName != null">
        #{nodeName,jdbcType=VARCHAR},
      </if>
      <if test="nodeType != null">
        #{nodeType,jdbcType=INTEGER},
      </if>
      <if test="flowStatus != null">
        #{flowStatus,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="isEffect != null">
        #{isEffect,jdbcType=INTEGER},
      </if>
      <if test="sourceBusinessId != null">
        #{sourceBusinessId,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=LONGVARCHAR},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=LONGVARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="busType != null">
        bus_type = #{busType,jdbcType=INTEGER},
      </if>
      <if test="beforeData != null">
        before_data = #{beforeData,jdbcType=LONGVARCHAR},
      </if>
      <if test="changeData != null">
        change_data = #{changeData,jdbcType=LONGVARCHAR},
      </if>
      <if test="afterData != null">
        after_data = #{afterData,jdbcType=LONGVARCHAR},
      </if>
      <if test="instanceId != null">
        instance_id = #{instanceId,jdbcType=BIGINT},
      </if>
      <if test="nodeCode != null">
        node_code = #{nodeCode,jdbcType=VARCHAR},
      </if>
      <if test="nodeName != null">
        node_name = #{nodeName,jdbcType=VARCHAR},
      </if>
      <if test="nodeType != null">
        node_type = #{nodeType,jdbcType=INTEGER},
      </if>
      <if test="flowStatus != null">
        flow_status = #{flowStatus,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="isEffect != null">
        is_effect = #{isEffect,jdbcType=INTEGER},
      </if>
      <if test="sourceBusinessId != null">
        source_business_id = #{sourceBusinessId,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=LONGVARCHAR},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
</mapper>