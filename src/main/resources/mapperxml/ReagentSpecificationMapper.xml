<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanhu.lims.gateway.admin.mapper.ReagentSpecificationMapper">
    <resultMap id="BaseResultMap" type="com.lanhu.lims.gateway.admin.model.ReagentSpecification">
        <!--@mbg.generated-->
        <!--@Table t_reagent_specification-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="reagent_id" jdbcType="BIGINT" property="reagentId"/>
        <result column="brand" jdbcType="VARCHAR" property="brand"/>
        <result column="catalog_number" jdbcType="VARCHAR" property="catalogNumber"/>
        <result column="specification" jdbcType="VARCHAR" property="specification"/>
        <result column="unit" jdbcType="VARCHAR" property="unit"/>
        <result column="storage_condition" jdbcType="VARCHAR" property="storageCondition"/>
        <result column="is_effect" jdbcType="INTEGER" property="isEffect"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_name" jdbcType="VARCHAR" property="updateName"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, reagent_id, brand, catalog_number, specification, unit, storage_condition, is_effect,
        create_by, create_name, create_time, update_by, update_name, update_time
    </sql>
</mapper>