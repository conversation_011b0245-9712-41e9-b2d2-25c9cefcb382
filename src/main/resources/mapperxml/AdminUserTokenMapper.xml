<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanhu.lims.gateway.admin.mapper.AdminUserTokenMapper">
  <resultMap id="BaseResultMap" type="com.lanhu.lims.gateway.admin.model.AdminUserToken">
    <!--@mbg.generated-->
    <!--@Table t_admin_user_token-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="token" jdbcType="VARCHAR" property="token" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_effect" jdbcType="INTEGER" property="isEffect" />
    <result column="os_version" jdbcType="VARCHAR" property="osVersion" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, token, create_time, update_time, is_effect, os_version, user_id
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_admin_user_token
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="token = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.token,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="is_effect = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.isEffect,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="os_version = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.osVersion,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.userId,jdbcType=BIGINT}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_admin_user_token
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="token = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.token != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.token,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="is_effect = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isEffect != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.isEffect,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="os_version = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.osVersion != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.osVersion,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.userId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.userId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into t_admin_user_token
    (id, token, create_time, update_time, is_effect, os_version, user_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.token,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.isEffect,jdbcType=INTEGER}, #{item.osVersion,jdbcType=VARCHAR}, 
        #{item.userId,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" parameterType="com.lanhu.lims.gateway.admin.model.AdminUserToken">
    <!--@mbg.generated-->
    insert into t_admin_user_token
    (id, token, create_time, update_time, is_effect, os_version, user_id)
    values
    (#{id,jdbcType=BIGINT}, #{token,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{isEffect,jdbcType=INTEGER}, #{osVersion,jdbcType=VARCHAR}, 
      #{userId,jdbcType=BIGINT})
    on duplicate key update 
    id = #{id,jdbcType=BIGINT}, 
    token = #{token,jdbcType=VARCHAR}, 
    create_time = #{createTime,jdbcType=TIMESTAMP}, 
    update_time = #{updateTime,jdbcType=TIMESTAMP}, 
    is_effect = #{isEffect,jdbcType=INTEGER}, 
    os_version = #{osVersion,jdbcType=VARCHAR}, 
    user_id = #{userId,jdbcType=BIGINT}
  </insert>
  <insert id="insertOrUpdateSelective" parameterType="com.lanhu.lims.gateway.admin.model.AdminUserToken">
    <!--@mbg.generated-->
    insert into t_admin_user_token
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="token != null">
        token,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isEffect != null">
        is_effect,
      </if>
      <if test="osVersion != null">
        os_version,
      </if>
      <if test="userId != null">
        user_id,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="token != null">
        #{token,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isEffect != null">
        #{isEffect,jdbcType=INTEGER},
      </if>
      <if test="osVersion != null">
        #{osVersion,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="token != null">
        token = #{token,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isEffect != null">
        is_effect = #{isEffect,jdbcType=INTEGER},
      </if>
      <if test="osVersion != null">
        os_version = #{osVersion,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>



  <select id="selectByToken" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_admin_user_token
    where token = #{token,jdbcType=VARCHAR}
    and is_effect=0
  </select>
</mapper>