<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanhu.lims.gateway.admin.mapper.DeptMapper">
  <resultMap id="BaseResultMap" type="com.lanhu.lims.gateway.admin.model.Dept">
    <!--@mbg.generated-->
    <!--@Table t_dept-->
    <id column="dept_id" jdbcType="BIGINT" property="deptId" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="ancestors" jdbcType="VARCHAR" property="ancestors" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="leader" jdbcType="VARCHAR" property="leader" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="is_effect" jdbcType="INTEGER" property="isEffect" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="type" jdbcType="INTEGER" property="type" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    dept_id, parent_id, ancestors, dept_name, order_num, leader, phone, email, `status`, 
    is_effect, create_by, create_time, update_by, update_time, `type`
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_dept
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="parent_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when dept_id = #{item.deptId,jdbcType=BIGINT} then #{item.parentId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="ancestors = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when dept_id = #{item.deptId,jdbcType=BIGINT} then #{item.ancestors,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="dept_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when dept_id = #{item.deptId,jdbcType=BIGINT} then #{item.deptName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="order_num = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when dept_id = #{item.deptId,jdbcType=BIGINT} then #{item.orderNum,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="leader = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when dept_id = #{item.deptId,jdbcType=BIGINT} then #{item.leader,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="phone = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when dept_id = #{item.deptId,jdbcType=BIGINT} then #{item.phone,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="email = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when dept_id = #{item.deptId,jdbcType=BIGINT} then #{item.email,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when dept_id = #{item.deptId,jdbcType=BIGINT} then #{item.status,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="is_effect = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when dept_id = #{item.deptId,jdbcType=BIGINT} then #{item.isEffect,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when dept_id = #{item.deptId,jdbcType=BIGINT} then #{item.createBy,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when dept_id = #{item.deptId,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="update_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when dept_id = #{item.deptId,jdbcType=BIGINT} then #{item.updateBy,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when dept_id = #{item.deptId,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="`type` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when dept_id = #{item.deptId,jdbcType=BIGINT} then #{item.type,jdbcType=INTEGER}
        </foreach>
      </trim>
    </trim>
    where dept_id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.deptId,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_dept
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="parent_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.parentId != null">
            when dept_id = #{item.deptId,jdbcType=BIGINT} then #{item.parentId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="ancestors = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.ancestors != null">
            when dept_id = #{item.deptId,jdbcType=BIGINT} then #{item.ancestors,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="dept_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deptName != null">
            when dept_id = #{item.deptId,jdbcType=BIGINT} then #{item.deptName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="order_num = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orderNum != null">
            when dept_id = #{item.deptId,jdbcType=BIGINT} then #{item.orderNum,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="leader = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.leader != null">
            when dept_id = #{item.deptId,jdbcType=BIGINT} then #{item.leader,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="phone = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.phone != null">
            when dept_id = #{item.deptId,jdbcType=BIGINT} then #{item.phone,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="email = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.email != null">
            when dept_id = #{item.deptId,jdbcType=BIGINT} then #{item.email,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.status != null">
            when dept_id = #{item.deptId,jdbcType=BIGINT} then #{item.status,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="is_effect = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isEffect != null">
            when dept_id = #{item.deptId,jdbcType=BIGINT} then #{item.isEffect,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createBy != null">
            when dept_id = #{item.deptId,jdbcType=BIGINT} then #{item.createBy,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when dept_id = #{item.deptId,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateBy != null">
            when dept_id = #{item.deptId,jdbcType=BIGINT} then #{item.updateBy,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when dept_id = #{item.deptId,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="`type` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.type != null">
            when dept_id = #{item.deptId,jdbcType=BIGINT} then #{item.type,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
    </trim>
    where dept_id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.deptId,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="dept_id" keyProperty="deptId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_dept
    (parent_id, ancestors, dept_name, order_num, leader, phone, email, `status`, is_effect, 
      create_by, create_time, update_by, update_time, `type`)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.parentId,jdbcType=BIGINT}, #{item.ancestors,jdbcType=VARCHAR}, #{item.deptName,jdbcType=VARCHAR}, 
        #{item.orderNum,jdbcType=INTEGER}, #{item.leader,jdbcType=VARCHAR}, #{item.phone,jdbcType=VARCHAR}, 
        #{item.email,jdbcType=VARCHAR}, #{item.status,jdbcType=INTEGER}, #{item.isEffect,jdbcType=INTEGER}, 
        #{item.createBy,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateBy,jdbcType=BIGINT}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.type,jdbcType=INTEGER})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="dept_id" keyProperty="deptId" parameterType="com.lanhu.lims.gateway.admin.model.Dept" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_dept
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="deptId != null">
        dept_id,
      </if>
      parent_id,
      ancestors,
      dept_name,
      order_num,
      leader,
      phone,
      email,
      `status`,
      is_effect,
      create_by,
      create_time,
      update_by,
      update_time,
      `type`,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="deptId != null">
        #{deptId,jdbcType=BIGINT},
      </if>
      #{parentId,jdbcType=BIGINT},
      #{ancestors,jdbcType=VARCHAR},
      #{deptName,jdbcType=VARCHAR},
      #{orderNum,jdbcType=INTEGER},
      #{leader,jdbcType=VARCHAR},
      #{phone,jdbcType=VARCHAR},
      #{email,jdbcType=VARCHAR},
      #{status,jdbcType=INTEGER},
      #{isEffect,jdbcType=INTEGER},
      #{createBy,jdbcType=BIGINT},
      #{createTime,jdbcType=TIMESTAMP},
      #{updateBy,jdbcType=BIGINT},
      #{updateTime,jdbcType=TIMESTAMP},
      #{type,jdbcType=INTEGER},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=BIGINT},
      </if>
      parent_id = #{parentId,jdbcType=BIGINT},
      ancestors = #{ancestors,jdbcType=VARCHAR},
      dept_name = #{deptName,jdbcType=VARCHAR},
      order_num = #{orderNum,jdbcType=INTEGER},
      leader = #{leader,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=INTEGER},
      is_effect = #{isEffect,jdbcType=INTEGER},
      create_by = #{createBy,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      `type` = #{type,jdbcType=INTEGER},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="dept_id" keyProperty="deptId" parameterType="com.lanhu.lims.gateway.admin.model.Dept" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_dept
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="ancestors != null">
        ancestors,
      </if>
      <if test="deptName != null">
        dept_name,
      </if>
      <if test="orderNum != null">
        order_num,
      </if>
      <if test="leader != null">
        leader,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="isEffect != null">
        is_effect,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="type != null">
        `type`,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="deptId != null">
        #{deptId,jdbcType=BIGINT},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=BIGINT},
      </if>
      <if test="ancestors != null">
        #{ancestors,jdbcType=VARCHAR},
      </if>
      <if test="deptName != null">
        #{deptName,jdbcType=VARCHAR},
      </if>
      <if test="orderNum != null">
        #{orderNum,jdbcType=INTEGER},
      </if>
      <if test="leader != null">
        #{leader,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="isEffect != null">
        #{isEffect,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=BIGINT},
      </if>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=BIGINT},
      </if>
      <if test="ancestors != null">
        ancestors = #{ancestors,jdbcType=VARCHAR},
      </if>
      <if test="deptName != null">
        dept_name = #{deptName,jdbcType=VARCHAR},
      </if>
      <if test="orderNum != null">
        order_num = #{orderNum,jdbcType=INTEGER},
      </if>
      <if test="leader != null">
        leader = #{leader,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="isEffect != null">
        is_effect = #{isEffect,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
</mapper>