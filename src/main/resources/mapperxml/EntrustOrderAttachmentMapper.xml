<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanhu.lims.gateway.admin.mapper.EntrustOrderAttachmentMapper">
  <resultMap id="BaseResultMap" type="com.lanhu.lims.gateway.admin.model.EntrustOrderAttachment">
    <!--@mbg.generated-->
    <!--@Table t_entrust_order_attachment-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="entrust_order_id" jdbcType="BIGINT" property="entrustOrderId" />
    <result column="attachment_name" jdbcType="VARCHAR" property="attachmentName" />
    <result column="attachment_size" jdbcType="INTEGER" property="attachmentSize" />
    <result column="attachment_suffix" jdbcType="VARCHAR" property="attachmentSuffix" />
    <result column="is_effect" jdbcType="INTEGER" property="isEffect" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="file_id" jdbcType="BIGINT" property="fileId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, entrust_order_id, attachment_name, attachment_size, attachment_suffix, is_effect, 
    url, remark, create_by, create_name, create_time, update_by, update_time, update_name, 
    file_id
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_entrust_order_attachment
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="entrust_order_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.entrustOrderId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="attachment_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.attachmentName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="attachment_size = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.attachmentSize,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="attachment_suffix = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.attachmentSuffix,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="is_effect = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.isEffect,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="url = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.url,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.remark,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createBy,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="create_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="update_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateBy,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="update_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="file_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.fileId,jdbcType=BIGINT}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_entrust_order_attachment
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="entrust_order_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.entrustOrderId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.entrustOrderId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="attachment_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.attachmentName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.attachmentName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="attachment_size = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.attachmentSize != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.attachmentSize,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="attachment_suffix = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.attachmentSuffix != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.attachmentSuffix,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="is_effect = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isEffect != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.isEffect,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="url = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.url != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.url,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.remark != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.remark,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createBy,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateBy,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="file_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fileId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.fileId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into t_entrust_order_attachment
    (id, entrust_order_id, attachment_name, attachment_size, attachment_suffix, is_effect, 
      url, remark, create_by, create_name, create_time, update_by, update_time, update_name, 
      file_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.entrustOrderId,jdbcType=BIGINT}, #{item.attachmentName,jdbcType=VARCHAR}, 
        #{item.attachmentSize,jdbcType=INTEGER}, #{item.attachmentSuffix,jdbcType=VARCHAR}, 
        #{item.isEffect,jdbcType=INTEGER}, #{item.url,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, 
        #{item.createBy,jdbcType=BIGINT}, #{item.createName,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateBy,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.updateName,jdbcType=VARCHAR}, 
        #{item.fileId,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertOrUpdate" parameterType="map">
    <!--@mbg.generated-->
    insert into t_entrust_order_attachment
    (id, entrust_order_id, attachment_name, attachment_size, attachment_suffix, is_effect, 
      url, remark, create_by, create_name, create_time, update_by, update_time, update_name, 
      file_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.entrustOrderId,jdbcType=BIGINT}, #{item.attachmentName,jdbcType=VARCHAR}, 
        #{item.attachmentSize,jdbcType=INTEGER}, #{item.attachmentSuffix,jdbcType=VARCHAR}, 
        #{item.isEffect,jdbcType=INTEGER}, #{item.url,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, 
        #{item.createBy,jdbcType=BIGINT}, #{item.createName,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateBy,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.updateName,jdbcType=VARCHAR}, 
        #{item.fileId,jdbcType=BIGINT})
    </foreach>
    on duplicate key update 
    id=values(id),
    entrust_order_id=values(entrust_order_id),
    attachment_name=values(attachment_name),
    attachment_size=values(attachment_size),
    attachment_suffix=values(attachment_suffix),
    is_effect=values(is_effect),
    url=values(url),
    remark=values(remark),
    create_by=values(create_by),
    create_name=values(create_name),
    create_time=values(create_time),
    update_by=values(update_by),
    update_time=values(update_time),
    update_name=values(update_name),
    file_id=values(file_id)
  </insert>
  <insert id="insertOnDuplicateUpdate" parameterType="com.lanhu.lims.gateway.admin.model.EntrustOrderAttachment">
    <!--@mbg.generated-->
    insert into t_entrust_order_attachment
    (id, entrust_order_id, attachment_name, attachment_size, attachment_suffix, is_effect, 
      url, remark, create_by, create_name, create_time, update_by, update_time, update_name, 
      file_id)
    values
    (#{id,jdbcType=BIGINT}, #{entrustOrderId,jdbcType=BIGINT}, #{attachmentName,jdbcType=VARCHAR}, 
      #{attachmentSize,jdbcType=INTEGER}, #{attachmentSuffix,jdbcType=VARCHAR}, #{isEffect,jdbcType=INTEGER}, 
      #{url,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{createBy,jdbcType=BIGINT}, 
      #{createName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=BIGINT}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{updateName,jdbcType=VARCHAR}, #{fileId,jdbcType=BIGINT}
      )
    on duplicate key update 
    id = #{id,jdbcType=BIGINT}, 
    entrust_order_id = #{entrustOrderId,jdbcType=BIGINT}, 
    attachment_name = #{attachmentName,jdbcType=VARCHAR}, 
    attachment_size = #{attachmentSize,jdbcType=INTEGER}, 
    attachment_suffix = #{attachmentSuffix,jdbcType=VARCHAR}, 
    is_effect = #{isEffect,jdbcType=INTEGER}, 
    url = #{url,jdbcType=VARCHAR}, 
    remark = #{remark,jdbcType=VARCHAR}, 
    create_by = #{createBy,jdbcType=BIGINT}, 
    create_name = #{createName,jdbcType=VARCHAR}, 
    create_time = #{createTime,jdbcType=TIMESTAMP}, 
    update_by = #{updateBy,jdbcType=BIGINT}, 
    update_time = #{updateTime,jdbcType=TIMESTAMP}, 
    update_name = #{updateName,jdbcType=VARCHAR}, 
    file_id = #{fileId,jdbcType=BIGINT}
  </insert>
  <insert id="insertOnDuplicateUpdateSelective" parameterType="com.lanhu.lims.gateway.admin.model.EntrustOrderAttachment">
    <!--@mbg.generated-->
    insert into t_entrust_order_attachment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="entrustOrderId != null">
        entrust_order_id,
      </if>
      <if test="attachmentName != null">
        attachment_name,
      </if>
      <if test="attachmentSize != null">
        attachment_size,
      </if>
      <if test="attachmentSuffix != null">
        attachment_suffix,
      </if>
      <if test="isEffect != null">
        is_effect,
      </if>
      <if test="url != null">
        url,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="fileId != null">
        file_id,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="entrustOrderId != null">
        #{entrustOrderId,jdbcType=BIGINT},
      </if>
      <if test="attachmentName != null">
        #{attachmentName,jdbcType=VARCHAR},
      </if>
      <if test="attachmentSize != null">
        #{attachmentSize,jdbcType=INTEGER},
      </if>
      <if test="attachmentSuffix != null">
        #{attachmentSuffix,jdbcType=VARCHAR},
      </if>
      <if test="isEffect != null">
        #{isEffect,jdbcType=INTEGER},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="fileId != null">
        #{fileId,jdbcType=BIGINT},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="entrustOrderId != null">
        entrust_order_id = #{entrustOrderId,jdbcType=BIGINT},
      </if>
      <if test="attachmentName != null">
        attachment_name = #{attachmentName,jdbcType=VARCHAR},
      </if>
      <if test="attachmentSize != null">
        attachment_size = #{attachmentSize,jdbcType=INTEGER},
      </if>
      <if test="attachmentSuffix != null">
        attachment_suffix = #{attachmentSuffix,jdbcType=VARCHAR},
      </if>
      <if test="isEffect != null">
        is_effect = #{isEffect,jdbcType=INTEGER},
      </if>
      <if test="url != null">
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="fileId != null">
        file_id = #{fileId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
</mapper>