spring:
  binlog4j:
    enable: true

    # 配置redis
    redis-config:
      host: ***************
      port: 8379
      password: lanhuredis0527QWE!


    client-configs:

      # 配置mysql服务器，从 从库 读取binlog
      slave:
        username: root
        password: root20250428QS@@
        host: ***************
        port: 5506
      # 数据库服务器id，用于区分不同的数据库服务器，如监听多数据源，serverId 必须不同，
        serverId: 1992

      # 持久化binlog读取的位置
        persistence: true
      # 是否首次启动，首次启动不会从redis读取binlog的位置，非首次启动会从redis读取binlog的位置，
      # 仅第一次启动设置为true，后续启动都设置为false
        inaugural: false
      # 单机还是集群模式，单机模式为 standalone （默认），集群模式为  cluster
        mode: cluster




    # 需要加入审计的库表
    handler:
      database: [ lims ]

#      tables: [t_admin_user, t_business_flow_application, t_business_flow_category, t_business_flow_type,
#              t_dept, t_dict_upload_file, t_file_record, t_menu, t_role, t_role_menu , test]
