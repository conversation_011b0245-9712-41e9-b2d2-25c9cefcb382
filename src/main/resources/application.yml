spring:
  profiles:
    active: dev

  messages:
    encoding: UTF-8
    basename: i18n/messages
  servlet:
    multipart:
      max-file-size: 1024MB
      max-request-size: 1024MB
  jackson:
    time-zone: Asia/Shanghai



mybatis-plus:
  mapper-locations: classpath:mapperxml/*Mapper.xml,classpath:mapperxml/flow/*Mapper.xml
  global-config:
    banner: false
  #configuration:
   # log-impl: org.apache.ibatis.logging.stdout.StdOutImpl #开启sql日志



xxlJob:
  #adminAddress: http://127.0.0.1:9000/xxl-job-admin
  adminAddress: http://*.*.*.*:9000/xxl-job-admin
  accessToken:
  appname: demo-admin-api
  address:
  ip:
  port: 6778
  logpath: logs/xxl-job/jobhandler
  logretentiondays: 30



mail:
  configs:
    #    smtp.gmail.com 587 <EMAIL> lwinlwin1992
    - username:
      password:
      default-encoding: utf-8
      host: smtp.qiye.aliyun.com
      protocol: smtp
      port: 465

flow:
  enable: false