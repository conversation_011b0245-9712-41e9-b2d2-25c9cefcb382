package com.lanhu.lims.gateway.admin.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanhu.lims.gateway.admin.core.BusinessException;
import com.lanhu.lims.gateway.admin.core.PcsResultCode;
import com.lanhu.lims.gateway.admin.enums.AuditStatusEnum;
import com.lanhu.lims.gateway.admin.enums.EnableEnum;
import com.lanhu.lims.gateway.admin.enums.IsEffectEnum;
import com.lanhu.lims.gateway.admin.enums.VisibleEnum;
import com.lanhu.lims.gateway.admin.mapper.DictDataCategoryMapper;
import com.lanhu.lims.gateway.admin.mapper.DictDataMapper;
import com.lanhu.lims.gateway.admin.model.AdminUser;
import com.lanhu.lims.gateway.admin.model.DictData;
import com.lanhu.lims.gateway.admin.model.DictDataCategory;
import com.lanhu.lims.gateway.admin.utils.ConvertUtil;
import com.lanhu.lims.gateway.admin.vo.req.*;
import com.lanhu.lims.gateway.admin.vo.resp.DictDataDetailVO;
import com.lanhu.lims.gateway.admin.vo.resp.DictDataListVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/********************************
 * @title DictDataService
 * @package com.lanhu.lims.gateway.admin.service
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/12 22:49
 * @version 0.0.1
 *********************************/
@Service
public class DictDataService {
    @Resource
    private DictDataCategoryMapper dictDataCategoryMapper;

    @Resource
    private DictDataMapper dictDataMapper;

    /**
     * 字典数据分页列表
     *
     * @return 字典数据列表
     */
    @DS("slave_1")
    public IPage<DictDataListVO> listPage(DictDataListPageForm form) {
        LambdaQueryWrapper<DictData> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(DictData::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.eq(DictData::getVisible, VisibleEnum.VISIBLE.getCode());
        wrapper.eq(ObjectUtil.isNotNull(form.getParentId()), DictData::getParentId, form.getParentId());
        wrapper.eq(ObjectUtil.isNotNull(form.getCategoryId()), DictData::getCategoryId, form.getCategoryId());
        wrapper.eq(ObjectUtil.isNotNull(form.getStatus()), DictData::getStatus, form.getStatus());
        wrapper.nested(StrUtil.isNotBlank(form.getName()), e -> e.like(DictData::getDictLabel, form.getName()).or().like(com.lanhu.lims.gateway.admin.model.DictData::getDictLabelEn, form.getName()));
        wrapper.orderByAsc(DictData::getDictSort).orderByAsc(com.lanhu.lims.gateway.admin.model.DictData::getCreateTime);
        Page<DictData> page = new Page<>(form.getPageIndex(), form.getPageSize());
        IPage<DictData> pageInfo = dictDataMapper.selectPage(page, wrapper);
        List<DictData> records = pageInfo.getRecords();
        // 获取所有分类
        List<DictDataCategory> categoryList = dictDataCategoryMapper.selectList(Wrappers.lambdaQuery());
        // 转换成map
        Map<Integer, String> categoryMap = categoryList.stream().collect(Collectors.toMap(DictDataCategory::getId, DictDataCategory::getName));
        // 使用ConvertUtil进行自定义转换
        return ConvertUtil.convertPage(pageInfo, entity -> {
            DictDataListVO vo = new DictDataListVO();
            BeanUtil.copyProperties(entity, vo);
            vo.setCategoryName(categoryMap.getOrDefault(entity.getCategoryId(), StrUtil.EMPTY));
            return vo;
        });
    }


    /**
     * 字典数据详情
     *
     * @return 字典数据详情
     */
    @DS("slave_1")
    public DictDataDetailVO detail(DictDataSingleForm form) {
        DictData dictData = dictDataMapper.selectById(form.getId());
        if (dictData == null || ObjectUtil.notEqual(IsEffectEnum.NORMAL.getCode(), dictData.getIsEffect())) {
            throw new BusinessException(PcsResultCode.DICT_DATA_NOT_EXIST);
        }
        if (ObjectUtil.notEqual(VisibleEnum.VISIBLE.getCode(), dictData.getVisible())) {
            throw new BusinessException(PcsResultCode.DICT_DATA_IS_NOT_VISIBLE);
        }
       return ConvertUtil.convert(dictData, DictDataDetailVO.class);
    }

    /**
     * 新增字典数据
     *
     * @param from      新增入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void add(DictDataAddFrom from, AdminUser adminUser) {
        // 校验分类是否存在
        DictDataCategory dictDataCategory = dictDataCategoryMapper.selectById(from.getCategoryId());
        if (dictDataCategory == null || ObjectUtil.notEqual(IsEffectEnum.NORMAL.getCode(), dictDataCategory.getIsEffect())) {
            throw new BusinessException(PcsResultCode.DICT_DATA_CATEGORY_NOT_EXIST);
        }
        // 校验父级是否存在
        if (from.getParentId() != null && from.getParentId() != 0L) {
            DictData parentDict = dictDataMapper.selectById(from.getParentId());
            if (parentDict == null || ObjectUtil.notEqual(IsEffectEnum.NORMAL.getCode(), parentDict.getIsEffect())) {
                throw new BusinessException(PcsResultCode.DICT_DATA_PARENT_NOT_EXIST);
            }
            if (ObjectUtil.notEqual(VisibleEnum.VISIBLE.getCode(), parentDict.getVisible())) {
                throw new BusinessException(PcsResultCode.DICT_DATA_IS_NOT_VISIBLE);
            }
        }
        // 校验数据是否重复
        LambdaQueryWrapper<DictData> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(DictData::getCategoryId, from.getCategoryId());
        wrapper.eq(DictData::getParentId, from.getParentId());
        wrapper.eq(DictData::getDictLabel, from.getDictLabel());
        wrapper.eq(DictData::getDictLabelEn, from.getDictLabelEn());
        wrapper.eq(DictData::getDictValue, from.getDictValue());
        wrapper.eq(DictData::getIsEffect, IsEffectEnum.NORMAL.getCode());
        DictData select = dictDataMapper.selectOne(wrapper);
        if (select != null) {
            throw new BusinessException(PcsResultCode.DICT_DATA_WAS_EXISTS);
        }
        // 新增字典数据
        DictData dictData = new DictData();
        dictData.setDictSort(ObjectUtil.defaultIfNull(from.getDictSort(), 0));
        dictData.setDictData(StrUtil.EMPTY);
        dictData.setDictLabel(from.getDictLabel());
        dictData.setDictLabelEn(from.getDictLabelEn());
        dictData.setDictValue(from.getDictValue());
        dictData.setParentId(ObjectUtil.defaultIfNull(from.getParentId(), 0L));
        dictData.setCategoryId(from.getCategoryId());
        dictData.setMappingLabel(StrUtil.EMPTY);
        dictData.setMappingValue(StrUtil.EMPTY);
        dictData.setVisible(VisibleEnum.VISIBLE.getCode());
        dictData.setStatus(EnableEnum.ENABLE.getCode());
        dictData.setIsEffect(IsEffectEnum.NORMAL.getCode());
        dictData.setCreateTime(DateUtil.date());
        dictData.setCreateBy(adminUser.getId());
        dictData.setCreateName(adminUser.getRealName());
        dictData.setUpdateTime(dictData.getCreateTime());
        dictData.setUpdateBy(dictData.getCreateBy());
        dictData.setUpdateName(dictData.getCreateName());
        dictData.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        dictData.setAuditBy(null);
        dictData.setAuditTime(null);
        dictData.setAuditName(StrUtil.EMPTY);
        dictData.setAuditRemark(StrUtil.EMPTY);
        dictDataMapper.insert(dictData);
        // todo 发起流程
    }

    /**
     * 修改字典数据
     *
     * @param from      修改入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void edit(DictDataEditFrom from, AdminUser adminUser) {
        // 校验数据
        DictData dictData = dictDataMapper.selectById(from.getId());
        if (dictData == null || ObjectUtil.notEqual(IsEffectEnum.NORMAL.getCode(), dictData.getIsEffect())) {
            throw new BusinessException(PcsResultCode.DICT_DATA_NOT_EXIST);
        }
        if (ObjectUtil.notEqual(VisibleEnum.VISIBLE.getCode(), dictData.getVisible())) {
            throw new BusinessException(PcsResultCode.DICT_DATA_IS_NOT_VISIBLE);
        }
        // 更新
        dictData.setDictSort(ObjectUtil.defaultIfNull(from.getDictSort(), 0));
        dictData.setDictLabel(from.getDictLabel());
        dictData.setDictLabelEn(from.getDictLabelEn());
        dictData.setDictValue(from.getDictValue());
        dictData.setUpdateTime(DateUtil.date());
        dictData.setUpdateBy(adminUser.getId());
        dictData.setUpdateName(adminUser.getRealName());
        dictData.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        dictDataMapper.updateById(dictData);
        // todo 发起流程
    }

    /**
     * 删除字典数据
     *
     * @param from      删除入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void del(DictDataDelFrom from, AdminUser adminUser) {
        // 校验数据
        DictData dictData = dictDataMapper.selectById(from.getId());
        if (dictData == null || ObjectUtil.notEqual(IsEffectEnum.NORMAL.getCode(), dictData.getIsEffect())) {
            throw new BusinessException(PcsResultCode.DICT_DATA_NOT_EXIST);
        }
        if (ObjectUtil.notEqual(VisibleEnum.VISIBLE.getCode(), dictData.getVisible())) {
            throw new BusinessException(PcsResultCode.DICT_DATA_IS_NOT_VISIBLE);
        }
        // 校验是否有子级
        LambdaQueryWrapper<DictData> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(DictData::getParentId, from.getId());
        wrapper.eq(DictData::getIsEffect, IsEffectEnum.NORMAL.getCode());
        Long count = dictDataMapper.selectCount(wrapper);
        if (count > 0) {
            throw new BusinessException(PcsResultCode.DICT_DATA_HAS_CHILDREN);
        }
        // 删除
        dictData.setIsEffect(IsEffectEnum.DELETE.getCode());
        dictData.setUpdateTime(DateUtil.date());
        dictData.setUpdateBy(adminUser.getId());
        dictData.setUpdateName(adminUser.getRealName());
        dictData.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        dictDataMapper.updateById(dictData);
        // todo 发起流程
    }

    /**
     * 启用字典数据
     *
     * @param form      启用入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void enable(DictDataSingleForm form, AdminUser adminUser) {
        updateStatus(form, EnableEnum.ENABLE, adminUser);
    }

    /**
     * 禁用字典数据
     *
     * @param form      禁用入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void disable(DictDataSingleForm form, AdminUser adminUser) {
        updateStatus(form, EnableEnum.DISABLE, adminUser);
    }

    // ================================================================== 内部引用 ================================================================== //

    /**
     * 字典数据列表
     *
     * @return 字典数据列表
     */
    @DS("slave_1")
    public List<DictData> list() {
        LambdaQueryWrapper<DictData> dictDataLambdaQueryWrapper = Wrappers.lambdaQuery();
        dictDataLambdaQueryWrapper.eq(DictData::getIsEffect, IsEffectEnum.NORMAL.getCode());
        dictDataLambdaQueryWrapper.eq(DictData::getStatus, EnableEnum.ENABLE.getCode());
        return dictDataMapper.selectList(dictDataLambdaQueryWrapper);
    }

    // =================================================================== 工作流回调 =================================================================== //

    // =================================================================== 私有方法区 =================================================================== //

    /**
     * 更新字典数据状态
     *
     * @param form       更新入参
     * @param enableEnum 启用状态
     * @param adminUser  当前用户
     */
    private void updateStatus(DictDataSingleForm form, EnableEnum enableEnum, AdminUser adminUser) {
        DictData dictData = dictDataMapper.selectById(form.getId());
        if (dictData == null || ObjectUtil.notEqual(IsEffectEnum.NORMAL.getCode(), dictData.getIsEffect())) {
            throw new BusinessException(PcsResultCode.DICT_DATA_NOT_EXIST);
        }
        if (ObjectUtil.notEqual(VisibleEnum.VISIBLE.getCode(), dictData.getVisible())) {
            throw new BusinessException(PcsResultCode.DICT_DATA_IS_NOT_VISIBLE);
        }
        // 更新状态
        dictData.setStatus(enableEnum.getCode());
        dictData.setUpdateTime(DateUtil.date());
        dictData.setUpdateBy(adminUser.getId());
        dictData.setUpdateName(adminUser.getRealName());
        dictDataMapper.updateById(dictData);
        // 更新子级状态
        LambdaUpdateWrapper<DictData> wrapper = Wrappers.lambdaUpdate();
        wrapper.eq(DictData::getParentId, form.getId());
        wrapper.eq(DictData::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.set(DictData::getStatus, enableEnum.getCode());
        dictDataMapper.update(null, wrapper);
    }
}
