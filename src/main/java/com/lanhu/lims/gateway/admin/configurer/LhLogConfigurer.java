package com.lanhu.lims.gateway.admin.configurer;
import com.lanhu.lims.gateway.admin.aspect.LhLogAspect;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

/********************************
 * @title LhLogConfigurer
 * @package com.lanhu.imenu.gateway.client.configurer
 * @description description
 *
 * @date 2022/11/17 2:19 下午
 * @version 0.0.1
 *********************************/
@Configuration
@EnableAspectJAutoProxy(proxyTargetClass = true)
@ConditionalOnClass(LhLogAspect.class)
@ConditionalOnMissingBean(LhLogAspect.class)
public class LhLogConfigurer {
    @Bean
    public LhLogAspect lhLogAspect() {
        return new LhLogAspect();
    }
}
