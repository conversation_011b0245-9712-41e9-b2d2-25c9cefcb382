package com.lanhu.lims.gateway.admin.controller;

import com.lanhu.lims.gateway.admin.annotation.Log;
import com.lanhu.lims.gateway.admin.auth.annotation.IgnoreSecurity;
import com.lanhu.lims.gateway.admin.core.BaseController;
import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.enums.LogBusinessType;
import com.lanhu.lims.gateway.admin.model.AdminUser;
import com.lanhu.lims.gateway.admin.service.LeaveService;
import com.lanhu.lims.gateway.admin.vo.req.LeaveAddForm;
import com.lanhu.lims.gateway.admin.vo.req.LeaveHandleForm;
import com.lanhu.lims.gateway.admin.vo.req.LeaveRejectLastForm;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindException;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @description: 请假业务相关接口
 * @author: huangzheng
 * @date: 2025/5/7 9:18
 */


@RestController
@RequestMapping()
@Api(tags = "请假业务相关接口",value = "请假业务相关接口")
@Slf4j
public class LeaveController extends BaseController {



    @Resource
    private LeaveService leaveService;


    @PostMapping("/leave/add")
    @ApiOperation("添加请假申请")
//    @Log(title = "请假业务", businessType = LogBusinessType.INSERT)
    @IgnoreSecurity
    public PcsResult addLeave(@Validated @RequestBody LeaveAddForm leaveAddForm) {

//        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER,LoginUser.class);

        AdminUser adminUser = new AdminUser();
        adminUser.setId(1L);
        adminUser.setRealName("admin");
        return leaveService.addLeave(leaveAddForm, adminUser);
    }





    /**
     *  处理请假申请(通过或驳回)
     */
    @PostMapping("/leave/handle")
    @ApiOperation("处理请假申请(通过或驳回)")
    @Log(title = "请假业务", businessType = LogBusinessType.HANDLER_BUSINESS)
    public PcsResult handleLeave(@Validated @RequestBody LeaveHandleForm LeaveHandleForm) throws BindException{



//        throw new BindException(new Object(),"ddd");

        return leaveService.handleLeave(LeaveHandleForm);

    }





    /**
     *   TODO 拿回到最近办理的任务
     */
//    @PostMapping("/taskBackByInsId")
//    public PcsResult taskBackByInsId(HttpServletRequest request, @RequestBody String id) {
//        return leaveService.taskBackByInsId(id);
//
//    }

    /**
     * 驳回到上一个节点
     */
    @PostMapping("/leave/rejectLast")
    @ApiOperation("驳回到上一个节点")
    @Log(title = "请假业务", businessType = LogBusinessType.FLOW_REJECT_LAST)
    public PcsResult rejectLast(@Validated @RequestBody LeaveRejectLastForm form) {
        return leaveService.rejectLast(form);
    }





















}
