package com.lanhu.lims.gateway.admin.utils.change;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.*;

/********************************
 * @title FieldChangeUtil
 * @package com.lanhu.lims.gateway.admin.utils.change
 * @description 字段变化对比工具类，用于对比同一个类更新前与更新后变化的值，并列出字段的注释
 *
 * <AUTHOR>
 * @date 2025/6/8 16:36
 * @version 1.0.0
 *********************************/
@Slf4j
public class FieldChangeUtil {

    /**
     * 默认忽略的字段名
     */
    private static final Set<String> DEFAULT_IGNORED_FIELDS = new HashSet<>(Arrays.asList("isEffect", "createTime", "updateTime", "createBy", "updateBy", "createName", "updateName", "serialVersionUID", "class"));

    /**
     * 对比两个对象的字段变化（只对比添加了@FieldMapping注解的字段）
     *
     * @param oldObject 更新前的对象
     * @param newObject 更新后的对象
     * @return 字段变化结果
     */
    public static FieldChangeResult compareObjects(Object oldObject, Object newObject) {
        return compareObjects(oldObject, newObject, null);
    }

    /**
     * 对比两个对象的字段变化（只对比添加了@FieldMapping注解的字段）
     *
     * @param oldObject     更新前的对象
     * @param newObject     更新后的对象
     * @param ignoredFields 忽略的字段名集合
     * @return 字段变化结果
     */
    public static FieldChangeResult compareObjects(Object oldObject, Object newObject, Set<String> ignoredFields) {
        return compareObjects(oldObject, newObject, ignoredFields, null);
    }

    /**
     * 对比两个对象的字段变化（只对比添加了@FieldMapping注解的字段）
     *
     * @param oldObject         更新前的对象
     * @param newObject         更新后的对象
     * @param ignoredFields     忽略的字段名集合
     * @param fieldDisplayNames 字段显示名映射（字段名 -> 显示名）
     * @return 字段变化结果
     */
    public static FieldChangeResult compareObjects(Object oldObject, Object newObject, Set<String> ignoredFields, Map<String, String> fieldDisplayNames) {
        if (oldObject == null && newObject == null) {
            return new FieldChangeResult();
        }
        if (oldObject == null || newObject == null) {
            throw new IllegalArgumentException("对比的对象不能为null");
        }
        if (!oldObject.getClass().equals(newObject.getClass())) {
            throw new IllegalArgumentException("对比的对象必须是同一类型");
        }
        Class<?> clazz = oldObject.getClass();
        FieldChangeResult result = new FieldChangeResult();
        result.setClassName(clazz.getSimpleName());
        // 获取所有字段（包括父类字段）
        List<Field> allFields = getAllFields(clazz);
        for (Field field : allFields) {
            String fieldName = field.getName();
            // 只处理添加了 @FieldMapping 注解的字段
            FieldMapping fieldMapping = field.getAnnotation(FieldMapping.class);
            if (fieldMapping == null) {
                continue;
            }
            // 检查 @FieldMapping 注解是否标记为忽略
            if (fieldMapping.ignore()) {
                continue;
            }
            // 跳过忽略的字段
            if (ignoredFields != null && ignoredFields.contains(fieldName)) {
                continue;
            }
            try {
                field.setAccessible(true);
                Object oldValue = field.get(oldObject);
                Object newValue = field.get(newObject);
                // 判断字段是否发生变化
                boolean changed = !ObjectUtil.equal(oldValue, newValue);
                // 获取字段显示名
                String fieldDisplayName = getFieldDisplayName(field, fieldDisplayNames);
                // 获取映射后的显示值
                Object oldDisplayValue = getMappedValue(field, oldValue);
                Object newDisplayValue = getMappedValue(field, newValue);
                // 创建字段变化项
                FieldChangeItem changeItem = new FieldChangeItem();
                changeItem.setFieldName(fieldName);
                changeItem.setFieldDisplayName(fieldDisplayName);
                changeItem.setOldValue(oldDisplayValue);
                changeItem.setNewValue(newDisplayValue);
                changeItem.setFieldType(field.getType().getSimpleName());
                changeItem.setChanged(changed);
                result.addFieldChange(changeItem);
            } catch (IllegalAccessException e) {
                log.warn("无法访问字段 {}: {}", fieldName, e.getMessage());
            }
        }
        return result;
    }

    /**
     * 对比两个对象的所有字段变化（包括没有@FieldMapping注解的字段）
     *
     * @param oldObject 更新前的对象
     * @param newObject 更新后的对象
     * @return 字段变化结果
     */
    public static FieldChangeResult compareAllFields(Object oldObject, Object newObject) {
        return compareAllFields(oldObject, newObject, DEFAULT_IGNORED_FIELDS);
    }

    /**
     * 对比两个对象的所有字段变化（包括没有@FieldMapping注解的字段）
     *
     * @param oldObject     更新前的对象
     * @param newObject     更新后的对象
     * @param ignoredFields 忽略的字段名集合
     * @return 字段变化结果
     */
    public static FieldChangeResult compareAllFields(Object oldObject, Object newObject, Set<String> ignoredFields) {
        return compareAllFields(oldObject, newObject, ignoredFields, null);
    }

    /**
     * 对比两个对象的所有字段变化（包括没有@FieldMapping注解的字段）
     *
     * @param oldObject         更新前的对象
     * @param newObject         更新后的对象
     * @param ignoredFields     忽略的字段名集合
     * @param fieldDisplayNames 字段显示名映射（字段名 -> 显示名）
     * @return 字段变化结果
     */
    public static FieldChangeResult compareAllFields(Object oldObject, Object newObject, Set<String> ignoredFields, Map<String, String> fieldDisplayNames) {
        if (oldObject == null && newObject == null) {
            return new FieldChangeResult();
        }
        if (oldObject == null || newObject == null) {
            throw new IllegalArgumentException("对比的对象不能为null");
        }
        if (!oldObject.getClass().equals(newObject.getClass())) {
            throw new IllegalArgumentException("对比的对象必须是同一类型");
        }
        Class<?> clazz = oldObject.getClass();
        FieldChangeResult result = new FieldChangeResult();
        result.setClassName(clazz.getSimpleName());
        // 获取所有字段（包括父类字段）
        List<Field> allFields = getAllFields(clazz);
        for (Field field : allFields) {
            String fieldName = field.getName();
            // 检查 @FieldMapping 注解是否标记为忽略
            FieldMapping fieldMapping = field.getAnnotation(FieldMapping.class);
            if (fieldMapping != null && fieldMapping.ignore()) {
                continue;
            }
            // 跳过忽略的字段
            if (ignoredFields != null && ignoredFields.contains(fieldName)) {
                continue;
            }
            try {
                field.setAccessible(true);
                Object oldValue = field.get(oldObject);
                Object newValue = field.get(newObject);
                // 判断字段是否发生变化
                boolean changed = !ObjectUtil.equal(oldValue, newValue);
                // 获取字段显示名
                String fieldDisplayName = getFieldDisplayName(field, fieldDisplayNames);
                // 获取映射后的显示值
                Object oldDisplayValue = getMappedValue(field, oldValue);
                Object newDisplayValue = getMappedValue(field, newValue);
                // 创建字段变化项
                FieldChangeItem changeItem = new FieldChangeItem();
                changeItem.setFieldName(fieldName);
                changeItem.setFieldDisplayName(fieldDisplayName);
                changeItem.setOldValue(oldDisplayValue);
                changeItem.setNewValue(newDisplayValue);
                changeItem.setFieldType(field.getType().getSimpleName());
                changeItem.setChanged(changed);
                result.addFieldChange(changeItem);
            } catch (IllegalAccessException e) {
                log.warn("无法访问字段 {}: {}", fieldName, e.getMessage());
            }
        }
        return result;
    }

    /**
     * 获取类的所有字段（包括父类字段）
     *
     * @param clazz 类
     * @return 字段列表
     */
    private static List<Field> getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        while (clazz != null && clazz != Object.class) {
            Field[] declaredFields = clazz.getDeclaredFields();
            fields.addAll(Arrays.asList(declaredFields));
            clazz = clazz.getSuperclass();
        }
        return fields;
    }

    /**
     * 获取字段的显示名
     *
     * @param field             字段
     * @param fieldDisplayNames 自定义字段显示名映射
     * @return 字段显示名
     */
    private static String getFieldDisplayName(Field field, Map<String, String> fieldDisplayNames) {
        String fieldName = field.getName();
        // 优先使用自定义显示名
        if (fieldDisplayNames != null && fieldDisplayNames.containsKey(fieldName)) {
            return fieldDisplayNames.get(fieldName);
        }
        // 从 @FieldMapping 注解获取
        FieldMapping fieldMapping = field.getAnnotation(FieldMapping.class);
        if (fieldMapping != null && StrUtil.isNotBlank(fieldMapping.name())) {
            return fieldMapping.name();
        }
        // 从 @ApiModelProperty 注解获取
        ApiModelProperty apiModelProperty = field.getAnnotation(ApiModelProperty.class);
        if (apiModelProperty != null && StrUtil.isNotBlank(apiModelProperty.value())) {
            return apiModelProperty.value();
        }
        // 返回字段名
        return fieldName;
    }

    /**
     * 获取字段值的映射显示值
     *
     * @param field 字段
     * @param value 原始值
     * @return 映射后的显示值，如果没有映射则返回原始值
     */
    private static Object getMappedValue(Field field, Object value) {
        if (value == null) {
            return null;
        }
        FieldMapping fieldMapping = field.getAnnotation(FieldMapping.class);
        if (fieldMapping == null || fieldMapping.mappings().length == 0) {
            return value;
        }
        String valueStr = value.toString();
        for (ValueMapping mapping : fieldMapping.mappings()) {
            if (valueStr.equals(mapping.value())) {
                return mapping.text();
            }
        }
        // 如果没有找到映射，返回原始值
        return value;
    }

    /**
     * 快速对比并获取格式化报告
     *
     * @param oldObject 更新前的对象
     * @param newObject 更新后的对象
     * @return 格式化的变化报告
     */
    public static String getFormattedReport(Object oldObject, Object newObject) {
        FieldChangeResult result = compareObjects(oldObject, newObject);
        return result.getFormattedReport();
    }

    /**
     * 快速对比并获取简化报告
     *
     * @param oldObject 更新前的对象
     * @param newObject 更新后的对象
     * @return 简化的变化报告
     */
    public static String getSimpleReport(Object oldObject, Object newObject) {
        FieldChangeResult result = compareObjects(oldObject, newObject);
        return result.getSimpleReport();
    }

    /**
     * 快速对比并获取变化字段的JSON数组
     *
     * @param oldObject 更新前的对象
     * @param newObject 更新后的对象
     * @return 变化字段的JSON数组字符串
     */
    public static String getChangedFieldsJson(Object oldObject, Object newObject) {
        FieldChangeResult result = compareObjects(oldObject, newObject);
        return result.getChangedFieldsJson();
    }

    /**
     * 快速对比并获取简化的变化字段JSON数组
     *
     * @param oldObject 更新前的对象
     * @param newObject 更新后的对象
     * @return 简化的变化字段JSON数组字符串
     */
    public static String getSimpleChangedFieldsJson(Object oldObject, Object newObject) {
        FieldChangeResult result = compareObjects(oldObject, newObject);
        return result.getSimpleChangedFieldsJson();
    }

    /**
     * 快速对比并获取所有字段的JSON数组
     *
     * @param oldObject 更新前的对象
     * @param newObject 更新后的对象
     * @return 所有字段的JSON数组字符串
     */
    public static String getAllFieldsJson(Object oldObject, Object newObject) {
        FieldChangeResult result = compareObjects(oldObject, newObject);
        return result.getAllFieldsJson();
    }
}
