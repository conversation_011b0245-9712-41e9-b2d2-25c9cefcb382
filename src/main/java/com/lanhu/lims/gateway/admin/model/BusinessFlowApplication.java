package com.lanhu.lims.gateway.admin.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description: ${DESCRIPTION}
 * @author: huangzheng
 * @date: 2025/6/17 15:28
 */

/**
 * Lims业务流申请表
 */
@ApiModel(description = "Lims业务流申请表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "t_business_flow_application")
public class BusinessFlowApplication {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 业务类型
     */
    @TableField(value = "bus_type")
    @ApiModelProperty(value = "业务类型")
    private Integer busType;

    /**
     * 变更前数据完整快照
     */
    @TableField(value = "before_data")
    @ApiModelProperty(value = "变更前数据完整快照")
    private String beforeData;

    /**
     * 变动的数据，只包含变化字段及其变动值
     */
    @TableField(value = "change_data")
    @ApiModelProperty(value = "变动的数据，只包含变化字段及其变动值")
    private String changeData;

    /**
     * 变更后数据完整快照
     */
    @TableField(value = "after_data")
    @ApiModelProperty(value = "变更后数据完整快照")
    private String afterData;

    /**
     * 流程实例的id
     */
    @TableField(value = "instance_id")
    @ApiModelProperty(value = "流程实例的id")
    private Long instanceId;

    /**
     * 节点编码
     */
    @TableField(value = "node_code")
    @ApiModelProperty(value = "节点编码")
    private String nodeCode;

    /**
     * 流程节点名称
     */
    @TableField(value = "node_name")
    @ApiModelProperty(value = "流程节点名称")
    private String nodeName;

    /**
     * 节点类型（0开始节点 1中间节点 2结束节点 3互斥网关 4并行网关）
     */
    @TableField(value = "node_type")
    @ApiModelProperty(value = "节点类型（0开始节点 1中间节点 2结束节点 3互斥网关 4并行网关）")
    private Integer nodeType;

    /**
     * 流程状态（0待提交 1审批中 2 审批通过 3自动通过 4终止 5作废 6撤销 7取回  8已完成 9已退回 10失效）
     */
    @TableField(value = "flow_status")
    @ApiModelProperty(value = "流程状态（0待提交 1审批中 2 审批通过 3自动通过 4终止 5作废 6撤销 7取回  8已完成 9已退回 10失效）")
    private Integer flowStatus;

    /**
     * 创建者
     */
    @TableField(value = "create_by")
    @ApiModelProperty(value = "创建者")
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 创建人姓名
     */
    @TableField(value = "create_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createName;

    /**
     * 更新者
     */
    @TableField(value = "update_by")
    @ApiModelProperty(value = "更新者")
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 更新人姓名
     */
    @TableField(value = "update_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updateName;

    /**
     * 是否有效，0：有效，1：无效
     */
    @TableField(value = "is_effect")
    @ApiModelProperty(value = "是否有效，0：有效，1：无效")
    private Integer isEffect;

    /**
     * 源头业务ID
     */
    @TableField(value = "source_business_id")
    @ApiModelProperty(value = "源头业务ID")
    private Long sourceBusinessId;

    /**
     * 备注
     */
    @TableField(value = "remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 原因
     */
    @TableField(value = "reason")
    @ApiModelProperty(value = "原因")
    private String reason;
}