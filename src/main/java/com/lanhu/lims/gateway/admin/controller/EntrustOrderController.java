package com.lanhu.lims.gateway.admin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanhu.lims.gateway.admin.auth.constants.SecurityConstants;
import com.lanhu.lims.gateway.admin.auth.context.SecurityContextHolder;
import com.lanhu.lims.gateway.admin.core.BaseController;
import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.model.EntrustOrder;
import com.lanhu.lims.gateway.admin.model.LoginUser;
import com.lanhu.lims.gateway.admin.service.EntrustOrderService;
import com.lanhu.lims.gateway.admin.vo.req.*;
import com.lanhu.lims.gateway.admin.vo.resp.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * @description: 委托单相关接口
 * @author: huangzheng
 * @date: 2025/6/10 9:51
 */

@RestController
@Slf4j
@Api(value = "lims-委托单相关接口", tags = "lims-委托单相关接口")
public class EntrustOrderController extends BaseController {




    @Resource
    private EntrustOrderService entrustOrderService;




    /**
     * 委托单列表分页查询
     */

    @PostMapping("/entrustOrder/v1/listPage")
    @ApiOperation(value = "委托单列表分页查询", notes = "委托单列表分页查询")
    public PcsResult<Page<EntrustOrder>> listPage(@Validated @RequestBody EntrustOrderListPageForm form) {


        return entrustOrderService.listPage(form);


    }



    /**
     * 新增委托单-客户信息录入
     */
    @PostMapping("/entrustOrder/v1/addCustomer")
    @ApiOperation(value = "新增委托单-客户信息录入", notes = "新增委托单-客户信息录入")
    public PcsResult<EntrustOrderAddCustomerVO> addCustomer(@Validated @RequestBody EntrustOrderCustomerAddForm form) {

        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        return entrustOrderService.addCustomer(form, loginUser);

    }






    /**
     * 新增委托单-样本信息录入
     */
    @PostMapping("/entrustOrder/v1/addSample")
    @ApiOperation(value = "新增委托单-样本信息录入", notes = "新增委托单-样本信息录入")
    public PcsResult addSample(@Validated @RequestBody EntrustOrderSampleAddForm form) {

        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);

        return entrustOrderService.addSample(form, loginUser);


    }




    /**
     * 新增委托单-其他信息录入
     */
    @PostMapping("/entrustOrder/v1/addOther")
    @ApiOperation(value = "新增委托单-其他信息录入", notes = "新增委托单-其他信息录入")
    public PcsResult addOther(@Validated @RequestBody EntrustOrderOtherAddForm form) {

        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);

        return entrustOrderService.addOther(form, loginUser);


    }


    /**
     * 提交委托单，发起委托单审批流程
     */
    @PostMapping("/entrustOrder/v1/commit")
    @ApiOperation(value = "新增委托单-提交审批", notes = "新增委托单-提交审批")
    public PcsResult commit(@Validated @RequestBody EntrustOrderCommitForm form) {

        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);

        return entrustOrderService.commit(form, loginUser);


    }



    /**
     * 委托单-查询客户信息内容
     */
    @PostMapping("/entrustOrder/v1/customerDetail")
    @ApiOperation(value = "委托单-查询客户信息内容", notes = "委托单-查询客户信息内容")
    public PcsResult<EntrustOrderCustomerDetailVO> customerDetail(@Validated @RequestBody EntrustOrderCustomerDetailForm form) {

        return entrustOrderService.customerDetail(form);


    }


    /**
     * 委托单-查询样本信息内容
     */
    @PostMapping("/entrustOrder/v1/otherInfoDetail")
    @ApiOperation(value = "委托单-查询样本信息内容", notes = "委托单-查询样本信息内容")
    public PcsResult<EntrustOrderSampleDetailVO> sampleDetail(@Validated @RequestBody EntrustOrderSampleDetailForm form) {

        return entrustOrderService.sampleDetail(form);


    }


    /**
     * 新增委托单-查询其他信息内容
     */
    @PostMapping("/entrustOrder/v1/otherDetail")
    @ApiOperation(value = "委托单-查询其他信息内容", notes = "委托单-查询其他信息内容")
    public PcsResult<EntrustOrderOtherDetailVO> otherDetail(@Validated @RequestBody EntrustOrderOtherDetailForm form) {

        return entrustOrderService.otherDetail(form);

    }


    /**
     * 委托单详情页面顶栏信息内容查询
     */
    @PostMapping("/entrustOrder/v1/bannerDetail")
    @ApiOperation(value = "委托单-详情页面顶栏信息内容查询", notes = "新增委托单-详情页面顶栏信息内容查询")
    public PcsResult<EntrustOrderBannerDetailVO> bannerDetail(@Validated @RequestBody EntrustOrderBannerDetailForm form) {

        return entrustOrderService.bannerDetail(form);

    }


    /**
     * 委托单内容编辑申请，委托单已经审核成功后继续编辑，需要重新发起编辑审批流程
     */
    @PostMapping("/entrustOrder/v1/editApply")
    @ApiOperation(value = "委托单-编辑申请", notes = "委托单-编辑申请，委托单已经审核成功后继续编辑，需要重新发起编辑审批流程")
    public PcsResult applyEdit(@Validated @RequestBody EntrustOrderEditApplyForm form) {


        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);


        return entrustOrderService.applyEdit(form,loginUser);

    }





    /**
     * 终止委托单
     */
    @PostMapping("/entrustOrder/v1/termination")
    @ApiOperation(value = "委托单-终止委托单", notes = "委托单-终止委托单")
    public PcsResult termination(@Validated @RequestBody EntrustOrderTerminationForm form) {


        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);


        return entrustOrderService.termination(form,loginUser);

    }


    /**
     * 完成委托单
     */
    @PostMapping("/entrustOrder/v1/complete")
    @ApiOperation(value = "委托单-完成委托单", notes = "委托单-完成委托单")
    public PcsResult complete(@Validated @RequestBody EntrustOrderCompleteForm form) {


        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);


        return entrustOrderService.complete(form,loginUser);

    }




    /**
     * 委托单删除功能
     */
    @PostMapping("/entrustOrder/v1/del")
    @ApiOperation(value = "委托单-删除委托单", notes = "委托单-删除委托单")
    public PcsResult del(@Validated @RequestBody EntrustOrderDelForm form) {


        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);


        return entrustOrderService.del(form,loginUser);

    }


    /**
     * 委托单附件预览
     */
    @PostMapping("/entrustOrder/v1/attachment/preview")
    @ApiOperation(value = "委托单-附件预览", notes = "委托单-附件预览")
    public PcsResult<String> preview(@Validated @RequestBody EntrustOrderAttachmentPreviewForm form) {


        return entrustOrderService.previewAttachment(form);



    }




    /**
     * 委托单附件下载
     */
    @PostMapping("/entrustOrder/v1/attachment/download")
    @ApiOperation(value = "委托单-附件下载", notes = "委托单-附件下载")
    public void downloadAttachment(@Validated @RequestBody  EntrustOrderAttachmentDownloadForm form, HttpServletResponse response) {


        entrustOrderService.downloadAttachment(form, response);

    }


    /**
     * 委托单附件下载
     */
    @PostMapping("/entrustOrder/v1/template/download")
    @ApiOperation(value = "委托单-附件下载", notes = "委托单-附件下载")
    public void downloadSampleImportTemplate(HttpServletResponse response) {


        entrustOrderService.downloadSampleImportTemplate(response);


    }


























}
