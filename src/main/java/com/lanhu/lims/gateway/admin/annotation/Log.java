package com.lanhu.lims.gateway.admin.annotation;


import com.lanhu.lims.gateway.admin.enums.LogBusinessType;
import com.lanhu.lims.gateway.admin.enums.LogOperatorType;

import java.lang.annotation.*;

/**
 * 自定义操作日志记录注解
 *
 * <AUTHOR>
 */
@Target({ElementType.PARAMETER, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Log {

    /**
     * 模块
     */
    String title() default "";

    /**
     * 功能
     */
    LogBusinessType businessType() default LogBusinessType.INSERT;

    /**
     * 操作人类别
     */
    LogOperatorType operatorType() default LogOperatorType.PC;

    /**
     * 是否保存请求的参数
     */
    boolean isSaveRequestData() default true;

    /**
     * 是否保存响应的参数
     */
    boolean isSaveResponseData() default false;
}
