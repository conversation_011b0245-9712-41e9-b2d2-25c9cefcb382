package com.lanhu.lims.gateway.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanhu.lims.gateway.admin.model.AdminUserToken;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface AdminUserTokenMapper extends BaseMapper<AdminUserToken> {
    int updateBatch(List<AdminUserToken> list);

    int updateBatchSelective(List<AdminUserToken> list);

    int batchInsert(@Param("list") List<AdminUserToken> list);

    int insertOrUpdate(AdminUserToken record);

    int insertOrUpdateSelective(AdminUserToken record);

    AdminUserToken selectByToken(@Param("token") String token);
}