package com.lanhu.lims.gateway.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanhu.lims.gateway.admin.model.Sequence;
import org.apache.ibatis.annotations.Mapper;

/********************************
 * @title SequenceMapper
 * @package com.lanhu.lims.gateway.admin.mapper
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/9 21:27
 * @version 0.0.1
 *********************************/

@Mapper
public interface SequenceMapper extends BaseMapper<Sequence> {
    /**
     * 根据名称查询
     *
     * @param name 名称
     * @return Sequence
     */
    Sequence selectByName(String name);
}