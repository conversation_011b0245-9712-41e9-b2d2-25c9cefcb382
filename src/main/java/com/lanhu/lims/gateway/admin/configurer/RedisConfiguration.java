package com.lanhu.lims.gateway.admin.configurer;

import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.*;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;


import java.util.Arrays;
import java.util.List;

@Configuration
public class RedisConfiguration {


    private final static Logger log = LoggerFactory.getLogger(RedisConfiguration.class);



    //spring.redis.cluster.nodes=redis://host1:port1,redis://host2:port2,redis://host3:port3
    //集群模式
    @Value("${spring.redis.cluster.nodes:}")
    private String clusterNodes;

    //单机模式
    @Value("${spring.redis.host:}")
    private String host;

    @Value("${spring.redis.port:6379}")
    private int port;

   //哨兵模式
    @Value("${spring.redis.sentinel.master:}")
    private String sentinelMaster;

    @Value("${spring.redis.sentinel.nodes:}")
    private String sentinelNodes;


    //密码
    @Value("${spring.redis.password:}")
    private String password;



    @Bean
    public RedisConnectionFactory redisConnectionFactory() {




        //如果是单机模式
        if(StringUtils.isNoneBlank(host)){
            RedisStandaloneConfiguration standaloneConfig = new RedisStandaloneConfiguration(host, port);
            if(StringUtils.isNoneBlank(password)){
                standaloneConfig.setPassword(password);
            }
            return new JedisConnectionFactory(standaloneConfig);
        }else {
            //如果是是集群模式
            if (StringUtils.isNoneBlank(clusterNodes)) {
                RedisClusterConfiguration clusterConfig = new RedisClusterConfiguration(Arrays.asList(clusterNodes.split(",")));
                if(StringUtils.isNoneBlank(password)){
                    clusterConfig.setPassword(password);
                }
                return new JedisConnectionFactory(clusterConfig);
            //如果是哨兵模式
            } else {
                RedisSentinelConfiguration sentinelConfig = new RedisSentinelConfiguration()
                        .master(sentinelMaster)
                        .sentinel(sentinelsFromNodes(sentinelNodes));
                if(StringUtils.isNoneBlank(password)){
                    sentinelConfig.setPassword(password);
                }

                return new JedisConnectionFactory(sentinelConfig);
            }
        }

//        RedisClusterConfiguration clusterConfig = new RedisClusterConfiguration(Arrays.asList(clusterNodes.split(",")));
//        return new JedisConnectionFactory(clusterConfig);
    }

    @Bean(name = "redisTemplate")
    public RedisTemplate<String, String> redisTemplate() {
        RedisTemplate<String, String> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory());
        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(new GenericJackson2JsonRedisSerializer());
        template.setHashKeySerializer(new GenericJackson2JsonRedisSerializer());
        template.setHashValueSerializer(new GenericJackson2JsonRedisSerializer());
        template.afterPropertiesSet();
        return template;
    }


    private RedisNode sentinelsFromNodes(String nodes) {
        List<RedisNode> sentinels = Lists.newArrayList();
        for (String node : nodes.split(",")) {
            String[] parts = node.split(":");
            sentinels.add(new RedisNode(parts[0], Integer.parseInt(parts[1])));
        }
        return sentinels.get(0);
    }
}
