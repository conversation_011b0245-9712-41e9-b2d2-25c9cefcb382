package com.lanhu.lims.gateway.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanhu.lims.gateway.admin.model.Role;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @description: ${DESCRIPTION}
 * @author: huangzheng
 * @date: 2025/4/29 15:16
 */
@Mapper
public interface RoleMapper extends BaseMapper<Role> {
    int updateBatch(@Param("list") List<Role> list);

    int updateBatchSelective(@Param("list") List<Role> list);

    int batchInsert(@Param("list") List<Role> list);

    int batchInsertOrUpdate(@Param("list") List<Role> list);

    int insertOnDuplicateUpdate(Role record);

    int insertOnDuplicateUpdateSelective(Role record);


    List<Role> selectRoleListByUserId(@Param("userId") Long userId);

    List<Role> selectAll();

    List<Role> selectUserRoleList(@Param("userId") Long userId);
}