package com.lanhu.lims.gateway.admin;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.scheduling.annotation.EnableScheduling;


@EnableScheduling
@SpringBootApplication(scanBasePackages = {"com.lanhu", "cn.hutool"},exclude = {DataSourceAutoConfiguration.class})
public class ZkqyLimsGatewayAdminApplication {

    public static void main(String[] args) {
        SpringApplication.run(ZkqyLimsGatewayAdminApplication.class, args);
    }

}
