package com.lanhu.lims.gateway.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanhu.lims.gateway.admin.model.Invoice;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @description: ${DESCRIPTION}
 * @author: huangzheng
 * @date: 2025/6/10 9:14
 */
@Mapper
public interface InvoiceMapper extends BaseMapper<Invoice> {
    int updateBatch(@Param("list") List<Invoice> list);

    int updateBatchSelective(@Param("list") List<Invoice> list);

    int batchInsert(@Param("list") List<Invoice> list);

    int batchInsertOrUpdate(@Param("list") List<Invoice> list);

    int insertOnDuplicateUpdate(Invoice record);

    int insertOnDuplicateUpdateSelective(Invoice record);
}