package com.lanhu.lims.gateway.admin.utils.sequence.formatter;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/********************************
 * @title CustomPatternFormatter
 * @package com.lanhu.lims.gateway.admin.utils.sequence.formatter
 * @description 自定义模式格式化器，支持日期时间和序列号组合
 *
 * <AUTHOR>
 * @date 2025/6/9 22:00
 * @version 0.0.1
 *********************************/
public class CustomPatternFormatter implements SequenceFormatter {
    private final String pattern;

    /**
     * 构造函数
     * @param pattern 格式化模式，支持以下占位符：
     *                {seq} - 序列号
     *                {seq:5} - 5位补零序列号
     *                {yyyy} - 年份
     *                {MM} - 月份
     *                {dd} - 日期
     *                {HH} - 小时
     *                {mm} - 分钟
     *                {ss} - 秒
     */
    public CustomPatternFormatter(String pattern) {
        this.pattern = pattern;
    }

    @Override
    public String format(int value) {
        String result = pattern;
        LocalDateTime now = LocalDateTime.now();
        
        // 替换日期时间占位符
        result = result.replace("{yyyy}", now.format(DateTimeFormatter.ofPattern("yyyy")));
        result = result.replace("{MM}", now.format(DateTimeFormatter.ofPattern("MM")));
        result = result.replace("{dd}", now.format(DateTimeFormatter.ofPattern("dd")));
        result = result.replace("{HH}", now.format(DateTimeFormatter.ofPattern("HH")));
        result = result.replace("{mm}", now.format(DateTimeFormatter.ofPattern("mm")));
        result = result.replace("{ss}", now.format(DateTimeFormatter.ofPattern("ss")));
        
        // 替换序列号占位符
        if (result.contains("{seq:")) {
            // 处理带长度的序列号格式 {seq:5}
            int start = result.indexOf("{seq:");
            int end = result.indexOf("}", start);
            if (start != -1 && end != -1) {
                String lengthStr = result.substring(start + 5, end);
                try {
                    int length = Integer.parseInt(lengthStr);
                    String formattedSeq = String.format("%0" + length + "d", value);
                    result = result.replace("{seq:" + lengthStr + "}", formattedSeq);
                } catch (NumberFormatException e) {
                    // 如果解析失败，使用原始值
                    result = result.replace("{seq:" + lengthStr + "}", String.valueOf(value));
                }
            }
        } else {
            // 处理普通序列号格式 {seq}
            result = result.replace("{seq}", String.valueOf(value));
        }
        
        return result;
    }
}
