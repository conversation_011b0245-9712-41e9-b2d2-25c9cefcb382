package com.lanhu.lims.gateway.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanhu.lims.gateway.admin.model.OperLog;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @description: ${DESCRIPTION}
 * @author: huangzheng
 * @date: 2025/4/29 15:16
 */
@Mapper
public interface OperLogMapper extends BaseMapper<OperLog> {
    int updateBatch(@Param("list") List<OperLog> list);

    int updateBatchSelective(@Param("list") List<OperLog> list);

    int batchInsert(@Param("list") List<OperLog> list);

    int batchInsertOrUpdate(@Param("list") List<OperLog> list);

    int insertOnDuplicateUpdate(OperLog record);

    int insertOnDuplicateUpdateSelective(OperLog record);
}