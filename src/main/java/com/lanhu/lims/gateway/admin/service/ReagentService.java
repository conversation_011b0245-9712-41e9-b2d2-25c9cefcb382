package com.lanhu.lims.gateway.admin.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanhu.lims.gateway.admin.core.BusinessException;
import com.lanhu.lims.gateway.admin.core.PcsResultCode;
import com.lanhu.lims.gateway.admin.enums.AuditStatusEnum;
import com.lanhu.lims.gateway.admin.enums.DictDataTypeEnum;
import com.lanhu.lims.gateway.admin.enums.EnableEnum;
import com.lanhu.lims.gateway.admin.enums.IsEffectEnum;
import com.lanhu.lims.gateway.admin.mapper.DictDataMapper;
import com.lanhu.lims.gateway.admin.mapper.ReagentMapper;
import com.lanhu.lims.gateway.admin.mapper.ReagentSpecificationMapper;
import com.lanhu.lims.gateway.admin.model.AdminUser;
import com.lanhu.lims.gateway.admin.model.DictData;
import com.lanhu.lims.gateway.admin.model.Reagent;
import com.lanhu.lims.gateway.admin.model.ReagentSpecification;
import com.lanhu.lims.gateway.admin.utils.ConvertUtil;
import com.lanhu.lims.gateway.admin.utils.sequence.SequenceNameEnum;
import com.lanhu.lims.gateway.admin.utils.sequence.SequenceUtil;
import com.lanhu.lims.gateway.admin.vo.req.*;
import com.lanhu.lims.gateway.admin.vo.resp.ReagentDetailVO;
import com.lanhu.lims.gateway.admin.vo.resp.ReagentListVO;
import com.lanhu.lims.gateway.admin.vo.resp.ReagentSpecificationVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/********************************
 * @title ReagentService
 * @package com.lanhu.lims.gateway.admin.service
 * @description 试剂管理服务
 *
 * <AUTHOR> Assistant
 * @date 2025/6/17
 * @version 0.0.1
 *********************************/
@Service
public class ReagentService {
    @Resource
    private ReagentMapper reagentMapper;

    @Resource
    private ReagentSpecificationMapper reagentSpecificationMapper;

    @Resource
    private DictDataMapper dictDataMapper;

    /**
     * 查询试剂列表
     *
     * @param form 查询入参
     * @return 列表
     */
    @DS("slave_1")
    public List<ReagentListVO> list(ReagentListForm form) {
        LambdaQueryWrapper<Reagent> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Reagent::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.eq(Reagent::getStatus, EnableEnum.ENABLE.getCode());
        wrapper.like(StrUtil.isNotBlank(form.getCode()), Reagent::getCode, form.getCode());
        wrapper.like(StrUtil.isNotBlank(form.getName()), Reagent::getName, form.getName());
        wrapper.like(StrUtil.isNotBlank(form.getShortName()), Reagent::getShortName, form.getShortName());
        wrapper.orderByDesc(Reagent::getCreateTime);
        List<Reagent> list = reagentMapper.selectList(wrapper);
        return ConvertUtil.convertList(list, ReagentListVO.class);
    }

    /**
     * 分页查询试剂
     *
     * @param form 查询入参
     * @return 分页列表
     */
    @DS("slave_1")
    public IPage<ReagentListVO> listPage(ReagentListPageForm form) {
        LambdaQueryWrapper<Reagent> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Reagent::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.eq(form.getStatus() != null, Reagent::getStatus, form.getStatus());
        wrapper.like(StrUtil.isNotBlank(form.getCode()), Reagent::getCode, form.getCode());
        wrapper.like(StrUtil.isNotBlank(form.getName()), Reagent::getName, form.getName());
        wrapper.like(StrUtil.isNotBlank(form.getShortName()), Reagent::getShortName, form.getShortName());
        wrapper.orderByDesc(Reagent::getCreateTime);
        Page<Reagent> page = new Page<>(form.getPageIndex(), form.getPageSize());
        IPage<Reagent> result = reagentMapper.selectPage(page, wrapper);
        return ConvertUtil.convertPage(result, ReagentListVO.class);
    }

    /**
     * 查询试剂详情
     *
     * @param form 查询入参
     * @return 详情
     */
    @DS("slave_1")
    public ReagentDetailVO detail(ReagentSingleForm form) {
        Reagent reagent = reagentMapper.selectById(form.getId());
        if (reagent == null || reagent.getIsEffect() != IsEffectEnum.NORMAL.getCode()) {
            throw new BusinessException(PcsResultCode.REAGENT_NOT_EXIST);
        }
        // 转换为详情VO
        return ConvertUtil.convert(reagent, ReagentDetailVO.class);
    }

    /**
     * 新增试剂
     *
     * @param form      新增入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void add(ReagentAddForm form, AdminUser adminUser) {
        // 生成试剂编号
        String reagentCode = SequenceUtil.getNextFormattedValue(SequenceNameEnum.REAGENT_CODE);
        // 新增试剂
        Reagent reagent = new Reagent();
        reagent.setCode(reagentCode);
        reagent.setName(form.getName());
        reagent.setShortName(form.getShortName());
        reagent.setStatus(EnableEnum.ENABLE.getCode());
        reagent.setIsEffect(IsEffectEnum.NORMAL.getCode());
        reagent.setCreateBy(adminUser.getId());
        reagent.setCreateName(adminUser.getRealName());
        reagent.setCreateTime(DateUtil.date());
        reagent.setUpdateBy(adminUser.getId());
        reagent.setUpdateName(adminUser.getRealName());
        reagent.setUpdateTime(DateUtil.date());
        reagent.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        reagentMapper.insert(reagent);
        // todo 发起流程
    }

    /**
     * 修改试剂
     *
     * @param form      修改入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void edit(ReagentEditForm form, AdminUser adminUser) {
        // 校验数据
        Reagent reagent = checkCanEditOrDelete(form.getId());
        // 修改试剂
        reagent.setName(form.getName());
        reagent.setShortName(form.getShortName());
        reagent.setUpdateBy(adminUser.getId());
        reagent.setUpdateName(adminUser.getRealName());
        reagent.setUpdateTime(DateUtil.date());
        reagent.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        reagentMapper.updateById(reagent);
        // todo 发起流程
    }

    /**
     * 删除试剂
     *
     * @param form      删除入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void del(ReagentSingleForm form, AdminUser adminUser) {
        // 校验数据
        Reagent reagent = checkCanEditOrDelete(form.getId());
        // 校验是否有规格数据
        LambdaQueryWrapper<ReagentSpecification> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ReagentSpecification::getReagentId, form.getId());
        wrapper.eq(ReagentSpecification::getIsEffect, IsEffectEnum.NORMAL.getCode());
        Long count = reagentSpecificationMapper.selectCount(wrapper);
        if (count > 0) {
            throw new BusinessException(PcsResultCode.REAGENT_HAS_SPECIFICATIONS);
        }
        // 删除试剂
        reagent.setIsEffect(IsEffectEnum.DELETE.getCode());
        reagent.setUpdateBy(adminUser.getId());
        reagent.setUpdateName(adminUser.getRealName());
        reagent.setUpdateTime(DateUtil.date());
        reagent.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        reagentMapper.updateById(reagent);
        // todo 发起流程
    }

    /**
     * 启用试剂
     *
     * @param form      启用入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void enable(ReagentSingleForm form, AdminUser adminUser) {
        // 更新状态
        updateStatus(form.getId(), EnableEnum.ENABLE, adminUser);
    }

    /**
     * 禁用试剂
     *
     * @param form      禁用入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void disable(ReagentSingleForm form, AdminUser adminUser) {
        // 更新状态
        updateStatus(form.getId(), EnableEnum.DISABLE, adminUser);
    }

    /**
     * 分页查询试剂规格列表
     *
     * @param form 查询入参
     * @return 分页列表
     */
    @DS("slave_1")
    public IPage<ReagentSpecificationVO> listPageSpecifications(ReagentSpecificationListPageForm form) {
        LambdaQueryWrapper<ReagentSpecification> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ReagentSpecification::getReagentId, form.getReagentId());
        wrapper.eq(ReagentSpecification::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.like(StrUtil.isNotBlank(form.getBrand()), ReagentSpecification::getBrand, form.getBrand());
        wrapper.like(StrUtil.isNotBlank(form.getCatalogNumber()), ReagentSpecification::getCatalogNumber, form.getCatalogNumber());
        wrapper.like(StrUtil.isNotBlank(form.getSpecification()), ReagentSpecification::getSpecification, form.getSpecification());
        wrapper.like(StrUtil.isNotBlank(form.getUnit()), ReagentSpecification::getUnit, form.getUnit());
        wrapper.like(StrUtil.isNotBlank(form.getStorageCondition()), ReagentSpecification::getStorageCondition, form.getStorageCondition());
        wrapper.orderByDesc(ReagentSpecification::getCreateTime);
        Page<ReagentSpecification> page = new Page<>(form.getPageIndex(), form.getPageSize());
        IPage<ReagentSpecification> result = reagentSpecificationMapper.selectPage(page, wrapper);
        return ConvertUtil.convertPage(result, ReagentSpecificationVO.class);
    }

    /**
     * 查询试剂规格列表
     *
     * @param form 查询入参
     * @return 规格列表
     */
    @DS("slave_1")
    public List<ReagentSpecificationVO> listSpecifications(ReagentSpecificationListForm form) {
        LambdaQueryWrapper<ReagentSpecification> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ReagentSpecification::getReagentId, form.getReagentId());
        wrapper.eq(ReagentSpecification::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.like(StrUtil.isNotBlank(form.getBrand()), ReagentSpecification::getBrand, form.getBrand());
        wrapper.like(StrUtil.isNotBlank(form.getCatalogNumber()), ReagentSpecification::getCatalogNumber, form.getCatalogNumber());
        wrapper.like(StrUtil.isNotBlank(form.getSpecification()), ReagentSpecification::getSpecification, form.getSpecification());
        wrapper.like(StrUtil.isNotBlank(form.getUnit()), ReagentSpecification::getUnit, form.getUnit());
        wrapper.like(StrUtil.isNotBlank(form.getStorageCondition()), ReagentSpecification::getStorageCondition, form.getStorageCondition());
        wrapper.orderByDesc(ReagentSpecification::getCreateTime);
        List<ReagentSpecification> list = reagentSpecificationMapper.selectList(wrapper);
        return ConvertUtil.convertList(list, ReagentSpecificationVO.class);
    }

    /**
     * 新增试剂规格
     *
     * @param form      新增入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void addSpecification(ReagentSpecificationAddForm form, AdminUser adminUser) {
        // 校验试剂是否存在
        Reagent reagent = reagentMapper.selectById(form.getReagentId());
        if (reagent == null || reagent.getIsEffect() != IsEffectEnum.NORMAL.getCode()) {
            throw new BusinessException(PcsResultCode.REAGENT_NOT_EXIST);
        }
        // 校验字典数据
        validateDictData(form.getBrand(), DictDataTypeEnum.REAGENT_BRAND, true, PcsResultCode.INVALID_REAGENT_BRAND_DICT);
        validateDictData(form.getUnit(), DictDataTypeEnum.REAGENT_UNIT, true, PcsResultCode.INVALID_REAGENT_UNIT_DICT);
        validateDictData(form.getStorageCondition(), DictDataTypeEnum.REAGENT_STORAGE_CONDITION, true, PcsResultCode.INVALID_REAGENT_STORAGE_CONDITION_DICT);
        // 新增规格
        ReagentSpecification specification = new ReagentSpecification();
        specification.setReagentId(form.getReagentId());
        specification.setBrand(form.getBrand());
        specification.setCatalogNumber(StrUtil.blankToDefault(form.getCatalogNumber(), StrUtil.EMPTY));
        specification.setSpecification(StrUtil.blankToDefault(form.getSpecification(), StrUtil.EMPTY));
        specification.setUnit(form.getUnit());
        specification.setStorageCondition(form.getStorageCondition());
        specification.setIsEffect(IsEffectEnum.NORMAL.getCode());
        specification.setCreateBy(adminUser.getId());
        specification.setCreateName(adminUser.getRealName());
        specification.setCreateTime(DateUtil.date());
        specification.setUpdateBy(adminUser.getId());
        specification.setUpdateName(adminUser.getRealName());
        specification.setUpdateTime(DateUtil.date());
        reagentSpecificationMapper.insert(specification);
    }

    /**
     * 修改试剂规格
     *
     * @param form      修改入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void editSpecification(ReagentSpecificationEditForm form, AdminUser adminUser) {
        // 校验规格是否存在
        ReagentSpecification specification = reagentSpecificationMapper.selectById(form.getId());
        if (specification == null || specification.getIsEffect() != IsEffectEnum.NORMAL.getCode()) {
            throw new BusinessException(PcsResultCode.REAGENT_SPECIFICATION_NOT_EXIST);
        }
        // 校验字典数据
        validateDictData(form.getBrand(), DictDataTypeEnum.REAGENT_BRAND, StrUtil.equals(specification.getBrand(), form.getBrand()), PcsResultCode.INVALID_REAGENT_BRAND_DICT);
        validateDictData(form.getUnit(), DictDataTypeEnum.REAGENT_UNIT, StrUtil.equals(specification.getUnit(), form.getUnit()), PcsResultCode.INVALID_REAGENT_UNIT_DICT);
        validateDictData(form.getStorageCondition(), DictDataTypeEnum.REAGENT_STORAGE_CONDITION, StrUtil.equals(specification.getStorageCondition(), form.getStorageCondition()), PcsResultCode.INVALID_REAGENT_STORAGE_CONDITION_DICT);
        // 修改规格
        specification.setBrand(form.getBrand());
        specification.setCatalogNumber(StrUtil.blankToDefault(form.getCatalogNumber(), StrUtil.EMPTY));
        specification.setSpecification(StrUtil.blankToDefault(form.getSpecification(), StrUtil.EMPTY));
        specification.setUnit(form.getUnit());
        specification.setStorageCondition(form.getStorageCondition());
        specification.setUpdateBy(adminUser.getId());
        specification.setUpdateName(adminUser.getRealName());
        specification.setUpdateTime(DateUtil.date());
        reagentSpecificationMapper.updateById(specification);
    }

    /**
     * 删除试剂规格
     *
     * @param form      删除入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void delSpecification(ReagentSpecificationSingleForm form, AdminUser adminUser) {
        // 校验规格是否存在
        ReagentSpecification specification = reagentSpecificationMapper.selectById(form.getId());
        if (specification == null || specification.getIsEffect() != IsEffectEnum.NORMAL.getCode()) {
            throw new BusinessException(PcsResultCode.REAGENT_SPECIFICATION_NOT_EXIST);
        }
        // 删除规格
        specification.setIsEffect(IsEffectEnum.DELETE.getCode());
        specification.setUpdateBy(adminUser.getId());
        specification.setUpdateName(adminUser.getRealName());
        specification.setUpdateTime(DateUtil.date());
        reagentSpecificationMapper.updateById(specification);
    }

    // ================================================================== 私有方法 ================================================================== //

    /**
     * 校验试剂是否可以编辑或删除
     *
     * @param id 试剂ID
     * @return 试剂对象
     */
    private Reagent checkCanEditOrDelete(Long id) {
        Reagent reagent = reagentMapper.selectById(id);
        if (reagent == null || reagent.getIsEffect() != IsEffectEnum.NORMAL.getCode()) {
            throw new BusinessException(PcsResultCode.REAGENT_NOT_EXIST);
        }
        if (ObjectUtil.notEqual(AuditStatusEnum.PASS.getCode(), reagent.getAuditStatus())) {
            throw new BusinessException(PcsResultCode.DATA_WAS_AUDITING_CANT_EDIT_OR_DELETE);
        }
        return reagent;
    }

    /**
     * 更新试剂状态
     *
     * @param id        试剂ID
     * @param status    状态
     * @param adminUser 当前用户
     */
    private void updateStatus(Long id, EnableEnum status, AdminUser adminUser) {
        Reagent reagent = reagentMapper.selectById(id);
        if (reagent == null || reagent.getIsEffect() != IsEffectEnum.NORMAL.getCode()) {
            throw new BusinessException(PcsResultCode.REAGENT_NOT_EXIST);
        }
        reagent.setStatus(status.getCode());
        reagent.setUpdateBy(adminUser.getId());
        reagent.setUpdateName(adminUser.getRealName());
        reagent.setUpdateTime(DateUtil.date());
        reagentMapper.updateById(reagent);
    }

    /**
     * 校验字典数据是否有效
     *
     * @param dictValue 字典值
     * @param dictType  字典类型
     * @param errorCode 错误码
     */
    private void validateDictData(String dictValue, DictDataTypeEnum dictType, boolean isMustEnable, PcsResultCode errorCode) {
        if (StrUtil.isBlank(dictValue)) {
            return;
        }
        LambdaQueryWrapper<DictData> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(DictData::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.eq(isMustEnable, DictData::getStatus, EnableEnum.ENABLE.getCode());
        wrapper.eq(DictData::getDictValue, dictValue);
        wrapper.apply(isMustEnable, "parent_id IN (SELECT id FROM t_dict_data WHERE dict_value = '" + dictType.getCode() + "' AND is_effect = 0 AND status = 1)", "parent_id IN (SELECT id FROM t_dict_data WHERE dict_value = '" + dictType.getCode() + "' AND is_effect = 0)");
        Long count = dictDataMapper.selectCount(wrapper);
        if (count == 0) {
            throw new BusinessException(errorCode);
        }
    }
}
