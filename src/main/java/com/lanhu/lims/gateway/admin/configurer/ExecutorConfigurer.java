package com.lanhu.lims.gateway.admin.configurer;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;

/********************************
 * @title ExecutorConfigurer
 * @package com.lanhu.imenu.gateway.client.configurer
 * @description description

 * <AUTHOR>
 * @date 2022/11/17 2:19 下午
 * @version 0.0.1
 *********************************/

@Configuration
@EnableAsync(proxyTargetClass = true)
public class ExecutorConfigurer {
    /**
     * 日志记录器
     */
    private final static Logger logger = LoggerFactory.getLogger(ExecutorConfigurer.class);

    /**
     * 操作日志异步保存线程池
     */
    @Bean("operLog")
    public ThreadPoolTaskExecutor operLogAsyncExecutor() {
        logger.info("start operLogAsyncExecutor");
        return initializeExecutor(50, 100, 99999, "oper-log-", 60, 60, true, true, new ThreadPoolExecutor.CallerRunsPolicy(), "操作日志异步保存异常: {}");
    }

    /**
     * 异步发送邮件线程池
     */
    @Bean("asyncSendMail")
    public ThreadPoolTaskExecutor asyncSendMail() {
        logger.info("start asyncSendMail");
        return initializeExecutor(50, 100, 99999, "async-send-mail-", 60, 60, true, true, new ThreadPoolExecutor.CallerRunsPolicy(), "异步发送邮件异常: {}");
    }

    /**
     * 图片下载线程池
     * 针对IO密集型任务优化配置
     */
    @Bean("imageDownloadExecutor")
    public ThreadPoolTaskExecutor imageDownloadExecutor() {
        logger.info("start imageDownloadExecutor");
        return initializeExecutor(10, 20, 100, "image-download-", 60, 60, true, true, new ThreadPoolExecutor.CallerRunsPolicy(), "图片下载线程池异常: {}");
    }

    /**
     * 通用线程池
     */
    @Bean("commonExecutor")
    public ThreadPoolTaskExecutor commonExecutor() {
        logger.info("start commonExecutor");
        return initializeExecutor(Runtime.getRuntime().availableProcessors(), Runtime.getRuntime().availableProcessors() * 2, 100, "common-", 60, 60, true, true, new ThreadPoolExecutor.CallerRunsPolicy(), "通用线程池异常: {}");
    }

    /**
     * 初始化线程池
     *
     * @param corePoolSize             核心线程数
     * @param maxPoolSize              最大线程数
     * @param queueCapacity            队列大小
     * @param threadNamePrefix         线程名前缀
     * @param keepAliveSeconds         线程空闲时间
     * @param allowCoreThreadTimeOut   是否允许核心线程超时
     * @param rejectedExecutionHandler 拒绝策略
     * @param exceptionMessageFormat   异常日志格式
     * @return ThreadPoolTaskExecutor
     */
    private ThreadPoolTaskExecutor initializeExecutor(int corePoolSize, int maxPoolSize, int queueCapacity, String threadNamePrefix, int keepAliveSeconds, int awaitTerminationSeconds, boolean waitForTasksToCompleteOnShutdown, boolean allowCoreThreadTimeOut, RejectedExecutionHandler rejectedExecutionHandler, String exceptionMessageFormat) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(queueCapacity);
        executor.setThreadNamePrefix(threadNamePrefix);
        executor.setKeepAliveSeconds(keepAliveSeconds);
        executor.setAwaitTerminationSeconds(awaitTerminationSeconds);
        executor.setAllowCoreThreadTimeOut(allowCoreThreadTimeOut);
        executor.setWaitForTasksToCompleteOnShutdown(waitForTasksToCompleteOnShutdown);
        executor.setRejectedExecutionHandler(rejectedExecutionHandler);
        executor.setThreadFactory(new CustomThreadFactory(threadNamePrefix, exceptionMessageFormat, logger));
        executor.initialize();
        return executor;
    }


    /**
     * 自定义线程工厂，为每个线程设置独立的异常处理器
     */
    private static class CustomThreadFactory implements ThreadFactory {
        private final AtomicInteger threadNumber = new AtomicInteger(1);
        private final String namePrefix;
        private final String exceptionMessageFormat;
        private final Logger logger;

        public CustomThreadFactory(String namePrefix, String exceptionMessageFormat, Logger logger) {
            this.namePrefix = namePrefix;
            this.exceptionMessageFormat = exceptionMessageFormat;
            this.logger = logger;
        }

        @Override
        public Thread newThread(@NotNull Runnable r) {
            Thread t = new Thread(r, namePrefix + threadNumber.getAndIncrement());
            // 根据需要设置是否为守护线程
            t.setDaemon(false);
            // 为每个新线程设置独立的异常处理器
            t.setUncaughtExceptionHandler((thread, e) -> {
                if (e != null) {
                    logger.error(String.format(exceptionMessageFormat, thread.getName()), ExceptionUtils.getStackTrace(e));
                }
            });
            return t;
        }
    }
}
