package com.lanhu.lims.gateway.admin.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lanhu.lims.gateway.admin.core.BusinessException;
import com.lanhu.lims.gateway.admin.core.PcsResultCode;
import com.lanhu.lims.gateway.admin.enums.AuditStatusEnum;
import com.lanhu.lims.gateway.admin.enums.EnableEnum;
import com.lanhu.lims.gateway.admin.enums.IsEffectEnum;
import com.lanhu.lims.gateway.admin.mapper.StorePositionMapper;
import com.lanhu.lims.gateway.admin.model.AdminUser;
import com.lanhu.lims.gateway.admin.model.StorePosition;
import com.lanhu.lims.gateway.admin.utils.ConvertUtil;
import com.lanhu.lims.gateway.admin.vo.req.*;
import com.lanhu.lims.gateway.admin.vo.resp.StorePositionDetailVO;
import com.lanhu.lims.gateway.admin.vo.resp.StorePositionListVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/********************************
 * @title StorePositionService
 * @package com.lanhu.lims.gateway.admin.service
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/12 20:15
 * @version 0.0.1
 *********************************/
@Service
public class StorePositionService {
    @Resource
    private StorePositionMapper storePositionMapper;

    /**
     * 获取存储位置树形列表
     */
    @DS("slave_1")
    public List<Tree<Long>> treeList(StorePositionListForm form) {
        LambdaQueryWrapper<StorePosition> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(StorePosition::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.eq(ObjectUtil.equal(form.getOnlyEnable(), Boolean.TRUE), StorePosition::getStatus, EnableEnum.ENABLE.getCode());
        wrapper.apply(ObjectUtil.isNotNull(form.getParentId()), "FIND_IN_SET(" + form.getParentId() + ",ancestors)");
        wrapper.orderByDesc(CollUtil.newArrayList(StorePosition::getStatus, StorePosition::getCreateTime));
        List<StorePosition> list = storePositionMapper.selectList(wrapper);
        // 转换为VO列表并构建树形结构
        List<StorePositionListVO> voList = BeanUtil.copyToList(list, StorePositionListVO.class);
        return TreeUtil.build(voList, 0L, (vo, tree) -> {
            tree.setId(vo.getId());
            tree.setParentId(vo.getParentId());
            tree.putExtra("name", vo.getName());
            tree.putExtra("shortName", vo.getShortName());
            tree.putExtra("status", vo.getStatus());
            tree.putExtra("createBy", vo.getCreateBy());
            tree.putExtra("createName", vo.getCreateName());
            tree.putExtra("createTime", vo.getCreateTime());
            tree.putExtra("updateBy", vo.getUpdateBy());
            tree.putExtra("updateName", vo.getUpdateName());
            tree.putExtra("updateTime", vo.getUpdateTime());
            tree.setWeight(vo.getCreateTime());
        });
    }

    /**
     * 获取存储位置详情
     */
    @DS("slave_1")
    public StorePositionDetailVO detail(StorePositionSingleForm form) {
        StorePosition storePosition = storePositionMapper.selectById(form.getId());
        if (storePosition == null || ObjectUtil.notEqual(IsEffectEnum.NORMAL.getCode(), storePosition.getIsEffect())) {
            throw new BusinessException(PcsResultCode.STORE_POSITION_NOT_EXIST);
        }
        return ConvertUtil.convert(storePosition, StorePositionDetailVO.class);
    }

    /**
     * 新增存储位置
     *
     * @param form      新增入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void add(StorePositionAddForm form, AdminUser adminUser) {
        // 校验参数是否合法 父级位置是否存在
        StorePosition parent = checkParentExist(form.getParentId());
        // 校验存储位置名是否已存在
        LambdaQueryWrapper<StorePosition> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(StorePosition::getName, form.getName());
        wrapper.eq(StorePosition::getParentId, form.getParentId());
        wrapper.eq(StorePosition::getIsEffect, IsEffectEnum.NORMAL.getCode());
        StorePosition select = storePositionMapper.selectOne(wrapper);
        if (select != null) {
            throw new BusinessException(PcsResultCode.STORE_POSITION_NAME_EXISTS);
        }
        // 新增存储位置
        StorePosition storePosition = new StorePosition();
        storePosition.setId(IdUtil.getSnowflakeNextId());
        storePosition.setName(form.getName());
        storePosition.setShortName(StrUtil.blankToDefault(form.getShortName(), StrUtil.EMPTY));
        storePosition.setParentId(form.getParentId());
        storePosition.setAncestors(ObjectUtil.isNull(parent) ? form.getParentId().toString() : parent.getAncestors() + "," + form.getParentId());
        storePosition.setStatus(EnableEnum.ENABLE.getCode());
        storePosition.setIsEffect(IsEffectEnum.NORMAL.getCode());
        storePosition.setCreateBy(adminUser.getId());
        storePosition.setCreateName(adminUser.getRealName());
        storePosition.setCreateTime(DateUtil.date());
        storePosition.setUpdateBy(adminUser.getId());
        storePosition.setUpdateName(adminUser.getRealName());
        storePosition.setUpdateTime(storePosition.getCreateTime());
        storePosition.setAuditStatus(AuditStatusEnum.PASS.getCode());
        storePositionMapper.insert(storePosition);
        // todo 发起流程
    }

    /**
     * 修改存储位置
     *
     * @param form      修改入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void edit(StorePositionEditForm form, AdminUser adminUser) {
        // 校验数据
        StorePosition storePosition = storePositionMapper.selectById(form.getId());
        if (storePosition == null || ObjectUtil.notEqual(IsEffectEnum.NORMAL.getCode(), storePosition.getIsEffect())) {
            throw new BusinessException(PcsResultCode.STORE_POSITION_NOT_EXIST);
        }
        // 校验当前父位置下存储位置名是否已存在
        LambdaQueryWrapper<StorePosition> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(StorePosition::getParentId, storePosition.getParentId());
        wrapper.eq(StorePosition::getName, form.getName());
        wrapper.eq(StorePosition::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.ne(StorePosition::getId, form.getId());
        StorePosition other = storePositionMapper.selectOne(wrapper);
        if (other != null) {
            throw new BusinessException(PcsResultCode.STORE_POSITION_NAME_EXISTS);
        }
        // 修改存储位置
        storePosition.setName(form.getName());
        storePosition.setShortName(StrUtil.blankToDefault(form.getShortName(), StrUtil.EMPTY));
        storePosition.setUpdateBy(adminUser.getId());
        storePosition.setUpdateName(adminUser.getRealName());
        storePosition.setUpdateTime(DateUtil.date());
        storePosition.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        storePositionMapper.updateById(storePosition);
        // todo 发起流程
    }

    /**
     * 删除存储位置
     *
     * @param form      删除入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void del(StorePositionDelForm form, AdminUser adminUser) {
        // 校验数据
        StorePosition storePosition = storePositionMapper.selectById(form.getId());
        if (storePosition == null || ObjectUtil.notEqual(IsEffectEnum.NORMAL.getCode(), storePosition.getIsEffect())) {
            throw new BusinessException(PcsResultCode.STORE_POSITION_NOT_EXIST);
        }
        // 校验存储位置是否有子位置
        checkHasChildren(form.getId());
        // 删除存储位置
        storePosition.setIsEffect(IsEffectEnum.DELETE.getCode());
        storePosition.setUpdateBy(adminUser.getId());
        storePosition.setUpdateName(adminUser.getRealName());
        storePosition.setUpdateTime(DateUtil.date());
        storePosition.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        storePositionMapper.updateById(storePosition);
        // todo 发起流程
    }

    /**
     * 启用存储位置
     *
     * @param form      启用入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void enable(StorePositionSingleForm form, AdminUser adminUser) {
        updateStatus(form, EnableEnum.ENABLE, adminUser);
    }

    /**
     * 禁用存储位置
     *
     * @param form      禁用入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void disable(StorePositionSingleForm form, AdminUser adminUser) {
        updateStatus(form, EnableEnum.DISABLE, adminUser);
    }

    // =================================================================== 工作流回调 =================================================================== //

    // =================================================================== 私有方法区 =================================================================== //

    /**
     * 校验存储位置是否存在
     *
     * @param parentId 父存储位置ID
     */
    private StorePosition checkParentExist(Long parentId) {
        if (parentId == null || parentId == 0L) {
            return null;
        }
        StorePosition storePosition = storePositionMapper.selectById(parentId);
        if (storePosition == null || ObjectUtil.notEqual(IsEffectEnum.NORMAL.getCode(), storePosition.getIsEffect())) {
            throw new BusinessException(PcsResultCode.STORE_POSITION_PARENT_NOT_EXIST);
        }
        return storePosition;
    }

    /**
     * 校验存储位置是否有子位置
     *
     * @param id 存储位置ID
     */
    private void checkHasChildren(Long id) {
        LambdaQueryWrapper<StorePosition> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(StorePosition::getParentId, id);
        wrapper.eq(StorePosition::getIsEffect, IsEffectEnum.NORMAL.getCode());
        Long count = storePositionMapper.selectCount(wrapper);
        if (count > 0) {
            throw new BusinessException(PcsResultCode.STORE_POSITION_HAS_CHILDREN);
        }
    }

    /**
     * 更新存储位置状态
     *
     * @param form       更新入参
     * @param enableEnum 启用状态
     * @param adminUser  当前用户
     */
    private void updateStatus(StorePositionSingleForm form, EnableEnum enableEnum, AdminUser adminUser) {
        // 校验数据
        StorePosition storePosition = storePositionMapper.selectById(form.getId());
        if (storePosition == null || ObjectUtil.notEqual(IsEffectEnum.NORMAL.getCode(), storePosition.getIsEffect())) {
            throw new BusinessException(PcsResultCode.STORE_POSITION_NOT_EXIST);
        }
        // 更新状态
        storePosition.setStatus(enableEnum.getCode());
        storePosition.setUpdateBy(adminUser.getId());
        storePosition.setUpdateName(adminUser.getRealName());
        storePosition.setUpdateTime(DateUtil.date());
        storePositionMapper.updateById(storePosition);
        // 更新子级状态
        LambdaUpdateWrapper<StorePosition> wrapper = Wrappers.lambdaUpdate();
        wrapper.eq(StorePosition::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.apply("FIND_IN_SET(" + form.getId() + ",ancestors)");
        wrapper.set(StorePosition::getStatus, storePosition.getStatus());
        wrapper.set(StorePosition::getUpdateBy, storePosition.getUpdateBy());
        wrapper.set(StorePosition::getUpdateName, storePosition.getUpdateName());
        wrapper.set(StorePosition::getUpdateTime, storePosition.getUpdateTime());
        storePositionMapper.update(null, wrapper);
    }
}
