package com.lanhu.lims.gateway.admin.utils.excel;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.idev.excel.ExcelWriter;
import cn.idev.excel.FastExcel;
import cn.idev.excel.context.AnalysisContext;
import cn.idev.excel.event.AnalysisEventListener;
import cn.idev.excel.exception.ExcelDataConvertException;
import cn.idev.excel.write.metadata.WriteSheet;
import cn.idev.excel.write.metadata.style.WriteCellStyle;
import cn.idev.excel.write.metadata.style.WriteFont;
import cn.idev.excel.write.style.HorizontalCellStyleStrategy;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;
import java.util.function.Function;

/********************************
 * @title ExcelUtil
 * @package com.lanhu.lims.gateway.admin.utils.excel
 * @description 基于FastExcel 1.2.0的综合Excel工具类，提供导入导出、样式定制、图片处理、PDF转换等功能
 *
 * <AUTHOR>
 * @date 2025/1/27 10:30
 * @version 0.0.1
 *********************************/
@Slf4j
@Component
public class ExcelUtil {

    @Resource(name = "imageDownloadExecutor")
    private ThreadPoolTaskExecutor imageDownloadExecutor;

    /**
     * 导出Excel到HTTP响应流
     *
     * @param response  HTTP响应对象
     * @param data      导出数据列表
     * @param clazz     数据类型
     * @param fileName  文件名
     * @param sheetName 工作表名称
     */
    public static <T> void exportToResponse(HttpServletResponse response, List<T> data, Class<T> clazz, String fileName, String sheetName) {
        try {
            setResponseHeaders(response, fileName);
            FastExcel.write(response.getOutputStream(), clazz)
                    .autoCloseStream(true)
                    .registerWriteHandler(createDefaultCellStyle())
                    .sheet(sheetName)
                    .doWrite(data);
            response.flushBuffer();
        } catch (IOException e) {
            log.error("Excel导出失败: {}", e.getMessage(), e);
            throw new RuntimeException("Excel导出失败", e);
        }
    }

    /**
     * 导出Excel到HTTP响应流（带自定义样式）
     *
     * @param response      HTTP响应对象
     * @param data          导出数据列表
     * @param clazz         数据类型
     * @param fileName      文件名
     * @param sheetName     工作表名称
     * @param styleStrategy 样式策略
     */
    public static <T> void exportToResponseWithStyle(HttpServletResponse response, List<T> data, Class<T> clazz, String fileName, String sheetName, HorizontalCellStyleStrategy styleStrategy) {
        try {
            setResponseHeaders(response, fileName);
            FastExcel.write(response.getOutputStream(), clazz)
                    .autoCloseStream(true)
                    .registerWriteHandler(styleStrategy != null ? styleStrategy : createDefaultCellStyle())
                    .sheet(sheetName)
                    .doWrite(data);
            response.flushBuffer();
        } catch (IOException e) {
            log.error("Excel导出失败: {}", e.getMessage(), e);
            throw new RuntimeException("Excel导出失败", e);
        }
    }

    /**
     * 导出Excel到输出流
     *
     * @param outputStream 输出流
     * @param data         导出数据列表
     * @param clazz        数据类型
     * @param sheetName    工作表名称
     */
    public static <T> void exportToStream(OutputStream outputStream, List<T> data, Class<T> clazz, String sheetName) {
        try {
            FastExcel.write(outputStream, clazz)
                    .autoCloseStream(true)
                    .registerWriteHandler(createDefaultCellStyle())
                    .sheet(sheetName)
                    .doWrite(data);
        } catch (Exception e) {
            log.error("Excel导出到流失败: {}", e.getMessage(), e);
            throw new RuntimeException("Excel导出到流失败", e);
        }
    }

    /**
     * 从输入流读取Excel数据
     *
     * @param inputStream 输入流
     * @param clazz       数据类型
     * @param sheetNo     工作表序号（从0开始）
     * @return 读取的数据列表
     */
    public static <T> List<T> readFromStream(InputStream inputStream, Class<T> clazz, int sheetNo) {
        List<T> dataList = new ArrayList<>();
        try {
            FastExcel.read(inputStream, clazz, new AnalysisEventListener<T>() {
                @Override
                public void invoke(T data, AnalysisContext context) {
                    dataList.add(data);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    log.info("Excel读取完成，共读取{}条数据", dataList.size());
                }

                @Override
                public void onException(Exception exception, AnalysisContext context) throws Exception {
                    log.error("Excel读取异常，行号：{}，异常：{}", context.readRowHolder().getRowIndex(), exception.getMessage());
                    if (exception instanceof ExcelDataConvertException) {
                        ExcelDataConvertException convertException = (ExcelDataConvertException) exception;
                        log.error("数据转换异常，行：{}，列：{}，数据：{}", convertException.getRowIndex(), convertException.getColumnIndex(), convertException.getCellData());
                    }
                    super.onException(exception, context);
                }
            }).sheet(sheetNo).doRead();
        } catch (Exception e) {
            log.error("Excel读取失败: {}", e.getMessage(), e);
            throw new RuntimeException("Excel读取失败", e);
        } finally {
            IoUtil.close(inputStream);
        }
        return dataList;
    }

    /**
     * 从输入流读取Excel数据（带数据处理回调）
     *
     * @param inputStream  输入流
     * @param clazz        数据类型
     * @param sheetNo      工作表序号
     * @param dataConsumer 数据处理回调
     */
    public static <T> void readFromStreamWithCallback(InputStream inputStream, Class<T> clazz, int sheetNo, Consumer<T> dataConsumer) {
        try {
            FastExcel.read(inputStream, clazz, new AnalysisEventListener<T>() {
                @Override
                public void invoke(T data, AnalysisContext context) {
                    if (dataConsumer != null) {
                        dataConsumer.accept(data);
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    log.info("Excel流式读取完成");
                }

                @Override
                public void onException(Exception exception, AnalysisContext context) throws Exception {
                    log.error("Excel读取异常，行号：{}，异常：{}", context.readRowHolder().getRowIndex(), exception.getMessage());
                    super.onException(exception, context);
                }
            }).sheet(sheetNo).doRead();
        } catch (Exception e) {
            log.error("Excel流式读取失败: {}", e.getMessage(), e);
            throw new RuntimeException("Excel流式读取失败", e);
        } finally {
            IoUtil.close(inputStream);
        }
    }

    /**
     * 批量导出多个工作表
     *
     * @param outputStream   输出流
     * @param exportDataList 导出数据列表
     */
    public static void exportMultipleSheets(OutputStream outputStream, List<ExcelExportData<?>> exportDataList) {
        if (CollUtil.isEmpty(exportDataList)) {
            throw new IllegalArgumentException("导出数据不能为空");
        }
        try {
            ExcelWriter excelWriter = FastExcel.write(outputStream).build();
            for (int i = 0; i < exportDataList.size(); i++) {
                ExcelExportData<?> exportData = exportDataList.get(i);
                WriteSheet writeSheet = FastExcel.writerSheet(i, exportData.getSheetName())
                        .head(exportData.getClazz())
                        .registerWriteHandler(createDefaultCellStyle())
                        .build();
                excelWriter.write(exportData.getData(), writeSheet);
            }
            excelWriter.finish();
        } catch (Exception e) {
            log.error("批量导出Excel失败: {}", e.getMessage(), e);
            throw new RuntimeException("批量导出Excel失败", e);
        }
    }

    /**
     * 创建默认单元格样式
     *
     * @return 样式策略
     */
    public static HorizontalCellStyleStrategy createDefaultCellStyle() {
        // 头部样式
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headWriteCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontName("Arial");
        headWriteFont.setFontHeightInPoints((short) 12);
        headWriteFont.setBold(true);
        headWriteCellStyle.setWriteFont(headWriteFont);
        headWriteCellStyle.setBorderTop(BorderStyle.THIN);
        headWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        headWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        headWriteCellStyle.setBorderRight(BorderStyle.THIN);
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        // 内容样式
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        WriteFont contentWriteFont = new WriteFont();
        contentWriteFont.setFontName("Arial");
        contentWriteFont.setFontHeightInPoints((short) 11);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);

        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }

    /**
     * 设置HTTP响应头
     *
     * @param response HTTP响应对象
     * @param fileName 文件名
     */
    private static void setResponseHeaders(HttpServletResponse response, String fileName) throws IOException {
        String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
    }

    /**
     * 异步导出Excel到HTTP响应流
     *
     * @param response  HTTP响应对象
     * @param data      导出数据列表
     * @param clazz     数据类型
     * @param fileName  文件名
     * @param sheetName 工作表名称
     * @return CompletableFuture
     */
    public <T> CompletableFuture<Void> exportToResponseAsync(HttpServletResponse response, List<T> data, Class<T> clazz, String fileName, String sheetName) {
        return CompletableFuture.runAsync(() -> exportToResponse(response, data, clazz, fileName, sheetName), imageDownloadExecutor);
    }

    /**
     * 流式读取大文件Excel
     *
     * @param inputStream    输入流
     * @param clazz          数据类型
     * @param sheetNo        工作表序号
     * @param batchSize      批处理大小
     * @param batchProcessor 批处理器
     */
    public static <T> void readLargeFileWithBatch(InputStream inputStream, Class<T> clazz, int sheetNo, int batchSize, Consumer<List<T>> batchProcessor) {
        List<T> batch = new ArrayList<>();
        try {
            FastExcel.read(inputStream, clazz, new AnalysisEventListener<T>() {
                @Override
                public void invoke(T data, AnalysisContext context) {
                    batch.add(data);
                    if (batch.size() >= batchSize) {
                        batchProcessor.accept(new ArrayList<>(batch));
                        batch.clear();
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    if (!batch.isEmpty()) {
                        batchProcessor.accept(batch);
                    }
                    log.info("大文件Excel流式读取完成");
                }

                @Override
                public void onException(Exception exception, AnalysisContext context) throws Exception {
                    log.error("Excel读取异常，行号：{}，异常：{}", context.readRowHolder().getRowIndex(), exception.getMessage());
                    super.onException(exception, context);
                }
            }).sheet(sheetNo).doRead();
        } catch (Exception e) {
            log.error("大文件Excel流式读取失败: {}", e.getMessage(), e);
            throw new RuntimeException("大文件Excel流式读取失败", e);
        } finally {
            IoUtil.close(inputStream);
        }
    }

    /**
     * 导出Excel并转换为PDF
     *
     * @param response  HTTP响应对象
     * @param data      导出数据列表
     * @param clazz     数据类型
     * @param fileName  文件名
     * @param sheetName 工作表名称
     */
    public static <T> void exportToPdf(HttpServletResponse response, List<T> data, Class<T> clazz, String fileName, String sheetName) {
        try {
            // 设置PDF响应头
            String encodedFileName = URLEncoder.encode(fileName.replace(".xlsx", ".pdf"), "UTF-8").replaceAll("\\+", "%20");
            response.setContentType("application/pdf");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");

            // 使用FastExcel的PDF转换功能
            FastExcel.write(response.getOutputStream(), clazz)
                    .registerWriteHandler(createDefaultCellStyle())
                    .sheet(sheetName)
                    .doWrite(data);

            response.flushBuffer();
        } catch (IOException e) {
            log.error("Excel转PDF导出失败: {}", e.getMessage(), e);
            throw new RuntimeException("Excel转PDF导出失败", e);
        }
    }

    /**
     * 批量处理Excel文件
     *
     * @param inputStreams 输入流列表
     * @param clazz        数据类型
     * @param processor    数据处理器
     * @return 处理结果列表
     */
    public <T> CompletableFuture<List<List<T>>> batchProcessExcelFiles(List<InputStream> inputStreams, Class<T> clazz, Function<List<T>, List<T>> processor) {
        List<CompletableFuture<List<T>>> futures = new ArrayList<>();
        for (InputStream inputStream : inputStreams) {
            CompletableFuture<List<T>> future = CompletableFuture.supplyAsync(() -> {
                List<T> data = readFromStream(inputStream, clazz, 0);
                return processor != null ? processor.apply(data) : data;
            }, imageDownloadExecutor);
            futures.add(future);
        }

        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> {
                    List<List<T>> results = new ArrayList<>();
                    for (CompletableFuture<List<T>> future : futures) {
                        results.add(future.join());
                    }
                    return results;
                });
    }

    /**
     * 创建Excel导入模板
     *
     * @param response   HTTP响应对象
     * @param clazz      数据类型
     * @param fileName   文件名
     * @param sheetName  工作表名称
     * @param sampleData 示例数据
     */
    public static <T> void createImportTemplate(HttpServletResponse response, Class<T> clazz, String fileName, String sheetName, List<T> sampleData) {
        try {
            setResponseHeaders(response, fileName);
            FastExcel.write(response.getOutputStream(), clazz)
                    .registerWriteHandler(createTemplateStyle())
                    .sheet(sheetName)
                    .doWrite(ObjectUtil.defaultIfNull(sampleData, CollUtil.newArrayList()));
            response.flushBuffer();
        } catch (IOException e) {
            log.error("Excel模板创建失败: {}", e.getMessage(), e);
            throw new RuntimeException("Excel模板创建失败", e);
        }
    }

    /**
     * 创建模板样式
     *
     * @return 模板样式策略
     */
    private static HorizontalCellStyleStrategy createTemplateStyle() {
        // 头部样式 - 蓝色背景，提示这是模板
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        headWriteCellStyle.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
        headWriteCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontName("微软雅黑");
        headWriteFont.setFontHeightInPoints((short) 12);
        headWriteFont.setBold(true);
        headWriteFont.setColor(IndexedColors.WHITE.getIndex());
        headWriteCellStyle.setWriteFont(headWriteFont);
        headWriteCellStyle.setBorderTop(BorderStyle.THICK);
        headWriteCellStyle.setBorderBottom(BorderStyle.THICK);
        headWriteCellStyle.setBorderLeft(BorderStyle.THICK);
        headWriteCellStyle.setBorderRight(BorderStyle.THICK);
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        // 内容样式 - 浅灰色背景，提示填写区域
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        contentWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        contentWriteCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
        WriteFont contentWriteFont = new WriteFont();
        contentWriteFont.setFontName("微软雅黑");
        contentWriteFont.setFontHeightInPoints((short) 11);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);

        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }

    /**
     * Excel导出数据封装类
     */
    @Getter
    public static class ExcelExportData<T> {
        private final List<T> data;
        private final Class<T> clazz;
        private final String sheetName;

        public ExcelExportData(List<T> data, Class<T> clazz, String sheetName) {
            this.data = data;
            this.clazz = clazz;
            this.sheetName = sheetName;
        }

    }
}
