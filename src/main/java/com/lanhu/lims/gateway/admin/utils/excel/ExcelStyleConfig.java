package com.lanhu.lims.gateway.admin.utils.excel;

import cn.idev.excel.metadata.data.DataFormatData;
import cn.idev.excel.write.metadata.style.WriteCellStyle;
import cn.idev.excel.write.metadata.style.WriteFont;
import cn.idev.excel.write.style.HorizontalCellStyleStrategy;
import lombok.Getter;
import org.apache.poi.ss.usermodel.*;

/********************************
 * @title ExcelStyleConfig
 * @package com.lanhu.lims.gateway.admin.utils.excel
 * @description Excel样式配置工具类，提供各种预定义样式和自定义样式创建方法
 *
 * <AUTHOR>
 * @date 2025/1/27 10:35
 * @version 0.0.1
 *********************************/
public class ExcelStyleConfig {

    /**
     * 创建蓝色主题样式
     *
     * @return 蓝色主题样式策略
     */
    public static HorizontalCellStyleStrategy createBlueThemeStyle() {
        // 头部样式 - 深蓝色背景
        WriteCellStyle headStyle = new WriteCellStyle();
        headStyle.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
        headStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
        WriteFont headFont = new WriteFont();
        headFont.setFontName("微软雅黑");
        headFont.setFontHeightInPoints((short) 12);
        headFont.setBold(true);
        headFont.setColor(IndexedColors.WHITE.getIndex());
        headStyle.setWriteFont(headFont);
        setBorders(headStyle);
        headStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        headStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 内容样式 - 浅蓝色交替
        WriteCellStyle contentStyle = new WriteCellStyle();
        WriteFont contentFont = new WriteFont();
        contentFont.setFontName("微软雅黑");
        contentFont.setFontHeightInPoints((short) 10);
        contentStyle.setWriteFont(contentFont);
        setBorders(contentStyle);
        contentStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
        contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        return new HorizontalCellStyleStrategy(headStyle, contentStyle);
    }

    /**
     * 创建绿色主题样式
     *
     * @return 绿色主题样式策略
     */
    public static HorizontalCellStyleStrategy createGreenThemeStyle() {
        // 头部样式 - 深绿色背景
        WriteCellStyle headStyle = new WriteCellStyle();
        headStyle.setFillForegroundColor(IndexedColors.DARK_GREEN.getIndex());
        headStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
        WriteFont headFont = new WriteFont();
        headFont.setFontName("微软雅黑");
        headFont.setFontHeightInPoints((short) 12);
        headFont.setBold(true);
        headFont.setColor(IndexedColors.WHITE.getIndex());
        headStyle.setWriteFont(headFont);
        setBorders(headStyle);
        headStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        headStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 内容样式
        WriteCellStyle contentStyle = new WriteCellStyle();
        WriteFont contentFont = new WriteFont();
        contentFont.setFontName("微软雅黑");
        contentFont.setFontHeightInPoints((short) 10);
        contentStyle.setWriteFont(contentFont);
        setBorders(contentStyle);
        contentStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
        contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        return new HorizontalCellStyleStrategy(headStyle, contentStyle);
    }

    /**
     * 创建简洁样式
     *
     * @return 简洁样式策略
     */
    public static HorizontalCellStyleStrategy createSimpleStyle() {
        // 头部样式 - 灰色背景
        WriteCellStyle headStyle = new WriteCellStyle();
        headStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
        WriteFont headFont = new WriteFont();
        headFont.setFontName("Arial");
        headFont.setFontHeightInPoints((short) 11);
        headFont.setBold(true);
        headStyle.setWriteFont(headFont);
        headStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        headStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 内容样式 - 无边框
        WriteCellStyle contentStyle = new WriteCellStyle();
        WriteFont contentFont = new WriteFont();
        contentFont.setFontName("Arial");
        contentFont.setFontHeightInPoints((short) 10);
        contentStyle.setWriteFont(contentFont);
        contentStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
        contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        return new HorizontalCellStyleStrategy(headStyle, contentStyle);
    }

    /**
     * 创建报表样式
     *
     * @return 报表样式策略
     */
    public static HorizontalCellStyleStrategy createReportStyle() {
        // 头部样式 - 深灰色背景，粗边框
        WriteCellStyle headStyle = new WriteCellStyle();
        headStyle.setFillForegroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
        headStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
        WriteFont headFont = new WriteFont();
        headFont.setFontName("宋体");
        headFont.setFontHeightInPoints((short) 12);
        headFont.setBold(true);
        headFont.setColor(IndexedColors.WHITE.getIndex());
        headStyle.setWriteFont(headFont);
        setThickBorders(headStyle);
        headStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        headStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 内容样式 - 细边框
        WriteCellStyle contentStyle = new WriteCellStyle();
        WriteFont contentFont = new WriteFont();
        contentFont.setFontName("宋体");
        contentFont.setFontHeightInPoints((short) 10);
        contentStyle.setWriteFont(contentFont);
        setBorders(contentStyle);
        contentStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        return new HorizontalCellStyleStrategy(headStyle, contentStyle);
    }

    /**
     * 创建自定义样式
     *
     * @param headBgColor     头部背景色
     * @param headFontColor   头部字体颜色
     * @param headFontSize    头部字体大小
     * @param contentFontSize 内容字体大小
     * @param fontName        字体名称
     * @return 自定义样式策略
     */
    public static HorizontalCellStyleStrategy createCustomStyle(IndexedColors headBgColor, IndexedColors headFontColor, short headFontSize, short contentFontSize, String fontName) {
        // 头部样式
        WriteCellStyle headStyle = new WriteCellStyle();
        headStyle.setFillForegroundColor(headBgColor.getIndex());
        headStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
        WriteFont headFont = new WriteFont();
        headFont.setFontName(fontName);
        headFont.setFontHeightInPoints(headFontSize);
        headFont.setBold(true);
        headFont.setColor(headFontColor.getIndex());
        headStyle.setWriteFont(headFont);
        setBorders(headStyle);
        headStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        headStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 内容样式
        WriteCellStyle contentStyle = new WriteCellStyle();
        WriteFont contentFont = new WriteFont();
        contentFont.setFontName(fontName);
        contentFont.setFontHeightInPoints(contentFontSize);
        contentStyle.setWriteFont(contentFont);
        setBorders(contentStyle);
        contentStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
        contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        return new HorizontalCellStyleStrategy(headStyle, contentStyle);
    }

    /**
     * 创建数字格式样式
     *
     * @param dataFormat 数据格式
     * @return 数字格式样式策略
     */
    public static HorizontalCellStyleStrategy createNumberFormatStyle(String dataFormat) {
        // 头部样式
        WriteCellStyle headStyle = new WriteCellStyle();
        headStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
        WriteFont headFont = new WriteFont();
        headFont.setFontName("Arial");
        headFont.setFontHeightInPoints((short) 11);
        headFont.setBold(true);
        headStyle.setWriteFont(headFont);
        setBorders(headStyle);
        headStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        // 内容样式 - 设置数字格式
        WriteCellStyle contentStyle = new WriteCellStyle();
        WriteFont contentFont = new WriteFont();
        contentFont.setFontName("Arial");
        contentFont.setFontHeightInPoints((short) 10);
        contentStyle.setWriteFont(contentFont);
        setBorders(contentStyle);
        contentStyle.setHorizontalAlignment(HorizontalAlignment.RIGHT);
        // FastExcel中的数字格式设置
        if (dataFormat != null) {
            DataFormatData dataFormatData = new DataFormatData();
            dataFormatData.setIndex((short) BuiltinFormats.getBuiltinFormat(dataFormat));
            dataFormatData.setFormat(dataFormat);
            contentStyle.setDataFormatData(dataFormatData);
        }

        return new HorizontalCellStyleStrategy(headStyle, contentStyle);
    }

    /**
     * 设置细边框
     *
     * @param cellStyle 单元格样式
     */
    private static void setBorders(WriteCellStyle cellStyle) {
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
    }

    /**
     * 设置粗边框
     *
     * @param cellStyle 单元格样式
     */
    private static void setThickBorders(WriteCellStyle cellStyle) {
        cellStyle.setBorderTop(BorderStyle.THICK);
        cellStyle.setBorderBottom(BorderStyle.THICK);
        cellStyle.setBorderLeft(BorderStyle.THICK);
        cellStyle.setBorderRight(BorderStyle.THICK);
    }

    /**
     * 样式主题枚举
     */
    @Getter
    public enum StyleTheme {
        DEFAULT("默认样式"), BLUE("蓝色主题"), GREEN("绿色主题"), SIMPLE("简洁样式"), REPORT("报表样式");

        private final String description;

        StyleTheme(String description) {
            this.description = description;
        }

        /**
         * 根据主题获取样式策略
         *
         * @return 样式策略
         */
        public HorizontalCellStyleStrategy getStyleStrategy() {
            switch (this) {
                case BLUE:
                    return createBlueThemeStyle();
                case GREEN:
                    return createGreenThemeStyle();
                case SIMPLE:
                    return createSimpleStyle();
                case REPORT:
                    return createReportStyle();
                default:
                    return ExcelUtil.createDefaultCellStyle();
            }
        }
    }
}
