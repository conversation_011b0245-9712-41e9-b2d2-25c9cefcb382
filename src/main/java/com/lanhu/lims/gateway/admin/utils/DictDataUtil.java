package com.lanhu.lims.gateway.admin.utils;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lanhu.lims.gateway.admin.core.ProjectConstant;
import com.lanhu.lims.gateway.admin.diff.vo.PropertyValueTranslation;
import com.lanhu.lims.gateway.admin.enums.DictDataTypeEnum;
import com.lanhu.lims.gateway.admin.model.DictData;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2025/6/18 11:07
 */

public class DictDataUtil {

    /**
     * @description: 根据字典数据值进行翻译
     * @param: [value, propertyValueTranslation]
     * @return: java.lang.String
     * @author: liuyi
     * @date: 09:00 2025/6/13
     */
    public static  String valueTranslation(String value, DictDataTypeEnum dictDataTypeEnum,String split, List<DictData> dictDataList){




        //翻译后值
        String valueTranslation = ProjectConstant.EMPTY;

        if(StringUtil.isBlank(value)){
            return valueTranslation;
        }

        //加载字典数据
        Map<String,DictData> dictDataMap = Maps.newHashMap();

        //原始数据列表
        List<String> valueList  = Lists.newArrayList();

        //翻译后列表
        List<String> valueTranslationList = Lists.newArrayList();


        DictData parentDictData = null;

        //需要翻译
        if(dictDataTypeEnum != null && StringUtil.isNotBlank(split)){

            valueList =  CollectionUtil.newArrayList(value.split(split));

            List<DictData> parentList = dictDataList.stream().filter(x->dictDataTypeEnum.getCode().equalsIgnoreCase(x.getDictValue())).collect(Collectors.toList());



            if(CollectionUtil.isNotEmpty(parentList)){
                parentDictData = parentList.get(0);
            }

            if(parentDictData == null){
                return  value;
            }

            for (DictData dictData : dictDataList) {
                if(parentDictData.getId().equals(dictData.getParentId())){
                    dictDataMap.put(dictData.getDictValue(),dictData);
                }
            }


            if(CollectionUtil.isNotEmpty(valueList)){

                for (String v : valueList) {
                    if(dictDataMap.get(v) != null && StringUtil.isNotBlank(dictDataMap.get(v).getDictLabel())){
                        valueTranslationList.add(dictDataMap.get(v).getDictLabel());
                    }
                }
            }

        }else {

            valueTranslationList.add(value);

        }


        if(CollectionUtil.isNotEmpty(valueTranslationList)){

            if(StringUtil.isNotBlank(split) && valueTranslationList.size() > 1){
                valueTranslation = String.join(split,valueTranslationList);
            }else {
                valueTranslation = valueTranslationList.get(0);
            }

        }



        return valueTranslation;


    }


    /**
     * @description: 核对字典value值是否存在,以便业务端乱传value
     * @param: [value, propertyValueTranslation]
     * @return: java.lang.String
     * @author: liuyi
     * @date: 09:00 2025/6/13
     */
    public static  boolean checkExist(String value, DictDataTypeEnum dictDataTypeEnum,String split, List<DictData> dictDataList){


        if(StringUtil.isBlank(value)){
            return true;
        }

        //原始数据列表
        List<String> valueList  = Lists.newArrayList();

        //翻译后列表
        List<String> valueTranslationList = Lists.newArrayList();


        if(StringUtil.isNotBlank(split)){
            if(StringUtil.isNotBlank(value)){
                valueList =   CollectionUtil.newArrayList(value.split(split));
            }
        }else {
            if(StringUtil.isNotBlank(value)){
                valueList.add(value);
            }
        }


        List<DictData> dataList = Lists.newArrayList();

        DictData parentDictData = null;

        //需要翻译
        if(dictDataTypeEnum != null){


            List<DictData> parentList = dictDataList.stream().filter(x->dictDataTypeEnum.getCode().equalsIgnoreCase(x.getDictValue())).collect(Collectors.toList());



            if(CollectionUtil.isNotEmpty(parentList)){
                parentDictData = parentList.get(0);
            }

            if(parentDictData != null){
                for (DictData dictData : dictDataList) {
                    if(parentDictData.getId().equals(dictData.getParentId())){
                        dataList.add(dictData);
                    }
                }

            }


            if(CollectionUtil.isNotEmpty(dictDataList)){
                valueTranslationList = dictDataList.stream().map(DictData::getDictValue).collect(Collectors.toList());
            }

        }



        //如果两个同时为空
        if(CollectionUtil.isEmpty(valueTranslationList) && CollectionUtil.isEmpty(valueList)){
            return true;
        }

        return CollectionUtil.containsAll(valueTranslationList,valueList);




    }
}
