package com.lanhu.lims.gateway.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanhu.lims.gateway.admin.model.Menu;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface MenuMapper extends BaseMapper<Menu> {
    int updateBatch(List<Menu> list);

    int updateBatchSelective(List<Menu> list);

    int batchInsert(@Param("list") List<Menu> list);

    int insertOrUpdate(Menu record);

    int insertOrUpdateSelective(Menu record);

    List<Menu> selectMenuListByUserId(@Param("userId") Long userId);

    List<Menu> selectMenuList();
}