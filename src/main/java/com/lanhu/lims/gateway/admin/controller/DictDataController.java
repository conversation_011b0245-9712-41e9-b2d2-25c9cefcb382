package com.lanhu.lims.gateway.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanhu.lims.gateway.admin.annotation.Log;
import com.lanhu.lims.gateway.admin.auth.constants.SecurityConstants;
import com.lanhu.lims.gateway.admin.auth.context.SecurityContextHolder;
import com.lanhu.lims.gateway.admin.core.BaseController;
import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.core.Result;
import com.lanhu.lims.gateway.admin.enums.LogBusinessType;
import com.lanhu.lims.gateway.admin.enums.LogOperatorType;
import com.lanhu.lims.gateway.admin.model.DictData;
import com.lanhu.lims.gateway.admin.model.LoginUser;
import com.lanhu.lims.gateway.admin.service.DictDataCategoryService;
import com.lanhu.lims.gateway.admin.service.DictDataService;
import com.lanhu.lims.gateway.admin.vo.req.*;
import com.lanhu.lims.gateway.admin.vo.resp.DictDataCategoryVO;
import com.lanhu.lims.gateway.admin.vo.resp.DictDataDetailVO;
import com.lanhu.lims.gateway.admin.vo.resp.DictDataListVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/********************************
 * @title DictDataController
 * @package com.lanhu.lims.gateway.admin.controller
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/13 15:28
 * @version 0.0.1
 *********************************/
@RestController
@Api(tags = "数据字典相关接口", value = "数据字典相关接口")
public class DictDataController extends BaseController {
    @Resource
    private DictDataCategoryService dictDataCategoryService;

    @Resource
    private DictDataService dictDataService;

    /**
     * 查询字典分类列表
     */
    @ApiOperation(value = "查询字典分类列表")
    @PostMapping("/dictData/category/v1/list")
    public PcsResult<List<DictDataCategoryVO>> categoryList() {
        return Result.ok(dictDataCategoryService.list());
    }

    /**
     * 查询字典数据分页列表
     */
    @ApiOperation(value = "查询字典数据分页列表")
    @PostMapping("/dictData/v1/listPage")
    public PcsResult<IPage<DictDataListVO>> listPage(DictDataListPageForm form) {
        return Result.ok(dictDataService.listPage(form));
    }

    /**
     * 根据字典类型查询字典数据列表
     */
    @ApiOperation(value = "获取所有字典数据列表")
    @PostMapping("/dictData/v1/list")
    public PcsResult<List<DictData>> list() {
        return  Result.ok(dictDataService.list());
    }

    /**
     * 查询字典数据详情
     */
    @ApiOperation(value = "查询字典数据详情")
    @PostMapping("/dictData/v1/detail")
    public PcsResult<DictDataDetailVO> detail(DictDataSingleForm form) {
        return Result.ok(dictDataService.detail(form));
    }

    /**
     * 新增字典数据
     */
    @ApiOperation(value = "新增字典数据")
    @PostMapping("/dictData/v1/add")
    @Log(title = "新增字典数据", businessType = LogBusinessType.INSERT, operatorType = LogOperatorType.PC)
    public PcsResult<Void> add(DictDataAddFrom from) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        dictDataService.add(from, loginUser.getSysUser());
        return Result.ok();
    }

    /**
     * 修改字典数据
     */
    @ApiOperation(value = "修改字典数据")
    @PostMapping("/dictData/v1/edit")
    @Log(title = "修改字典数据", businessType = LogBusinessType.UPDATE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> edit(DictDataEditFrom from) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        dictDataService.edit(from, loginUser.getSysUser());
        return Result.ok();
    }

    /**
     * 删除字典数据
     */
    @ApiOperation(value = "删除字典数据")
    @PostMapping("/dictData/v1/del")
    @Log(title = "删除字典数据", businessType = LogBusinessType.LOGICDELETE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> del(DictDataDelFrom from) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        dictDataService.del(from, loginUser.getSysUser());
        return Result.ok();
    }

    /**
     * 启用字典数据
     */
    @ApiOperation(value = "启用字典数据")
    @PostMapping("/dictData/v1/enable")
    @Log(title = "启用字典数据", businessType = LogBusinessType.ENABLE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> enable(DictDataSingleForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        dictDataService.enable(form, loginUser.getSysUser());
        return Result.ok();
    }

    /**
     * 禁用字典数据
     */
    @ApiOperation(value = "禁用字典数据")
    @PostMapping("/dictData/v1/disable")
    @Log(title = "禁用字典数据", businessType = LogBusinessType.DISABLE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> disable(DictDataSingleForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        dictDataService.disable(form, loginUser.getSysUser());
        return Result.ok();
    }
}
