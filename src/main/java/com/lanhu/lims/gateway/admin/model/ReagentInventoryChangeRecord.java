package com.lanhu.lims.gateway.admin.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/********************************
 * @title ReagentInventoryChangeRecord
 * @package com.lanhu.lims.gateway.admin.model
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/17 12:50
 * @version 0.0.1
 *********************************/

@ApiModel(description = "试剂出入库记录表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "t_reagent_inventory_change_record")
public class ReagentInventoryChangeRecord {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 出入库订单ID
     */
    @TableField(value = "order_id")
    @ApiModelProperty(value = "出入库订单ID")
    private Long orderId;

    /**
     * 试剂库存ID
     */
    @TableField(value = "inventory_id")
    @ApiModelProperty(value = "试剂库存ID")
    private Long inventoryId;

    /**
     * 操作类型(0：入库，1：出库)
     */
    @TableField(value = "operation_type")
    @ApiModelProperty(value = "操作类型(0：入库，1：出库)")
    private Integer operationType;

    /**
     * 变动库存
     */
    @TableField(value = "change_inventory")
    @ApiModelProperty(value = "变动库存")
    private BigDecimal changeInventory;

    /**
     * 变动后库存
     */
    @TableField(value = "after_inventory")
    @ApiModelProperty(value = "变动后库存")
    private BigDecimal afterInventory;

    /**
     * 变动前库存
     */
    @TableField(value = "before_inventory")
    @ApiModelProperty(value = "变动前库存")
    private BigDecimal beforeInventory;

    /**
     * 备注
     */
    @TableField(value = "remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 是否有效，0：正常，1：删除
     */
    @TableField(value = "is_effect")
    @ApiModelProperty(value = "是否有效，0：正常，1：删除")
    private Integer isEffect;

    /**
     * 创建人ID
     */
    @TableField(value = "create_by")
    @ApiModelProperty(value = "创建人ID")
    private Long createBy;

    /**
     * 创建人姓名
     */
    @TableField(value = "create_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createName;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 修改人ID
     */
    @TableField(value = "update_by")
    @ApiModelProperty(value = "修改人ID")
    private Long updateBy;

    /**
     * 修改人姓名
     */
    @TableField(value = "updater_name")
    @ApiModelProperty(value = "修改人姓名")
    private String updaterName;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 审核状态(-2: 审核不通过 0:待审核 1: 审核中 2:审核通过/正常)
     */
    @TableField(value = "audit_status")
    @ApiModelProperty(value = "审核状态(-2: 审核不通过 0:待审核 1: 审核中 2:审核通过/正常)")
    private Integer auditStatus;

    /**
     * 最近一次审核人ID
     */
    @TableField(value = "audit_by")
    @ApiModelProperty(value = "最近一次审核人ID")
    private Long auditBy;

    /**
     * 最近一次审核时间
     */
    @TableField(value = "audit_time")
    @ApiModelProperty(value = "最近一次审核时间")
    private Date auditTime;

    /**
     * 最近一次审核人名称
     */
    @TableField(value = "audit_name")
    @ApiModelProperty(value = "最近一次审核人名称")
    private String auditName;

    /**
     * 审核备注/审核不通过原因
     */
    @TableField(value = "audit_remark")
    @ApiModelProperty(value = "审核备注/审核不通过原因")
    private String auditRemark;
}