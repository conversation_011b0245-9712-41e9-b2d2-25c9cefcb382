package com.lanhu.lims.gateway.admin.utils;

import cn.hutool.core.collection.CollectionUtil;

import java.time.DateTimeException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

/**
 * 判断多个时间段是否有重叠（交集）
 * @auth: huhouchun
 * @version: 1.0.0
 * @date: 2022/10/31 17:10
 */
public class IsOverlapTimeUtil {

    /**
     * 判断多个时间段是否有重叠（交集）
     * @param timePairs 时间段数组
     * @param isStrict 是否严格重叠，true 严格，没有任何相交或相等；false 不严格，可以首尾相等，比如2021-05-29到2021-05-31和2021-05-31到2021-06-01，不重叠。
     * @return 返回是否重叠
     */
    public static boolean isOverlap(TimePairUtil[] timePairs, boolean isStrict){
        if(timePairs==null || timePairs.length==0){
            throw new DateTimeException("时间段不能为空");
        }
        Arrays.sort(timePairs, Comparator.comparingLong(TimePairUtil::getStart));

        for(int i = 1; i < timePairs.length; i++){
            if(isStrict){
                if(! (timePairs[i-1].getEnd()<timePairs[i].getStart())){
                    return true;
                }
            }else{
                if(! (timePairs[i-1].getEnd()<=timePairs[i].getStart())){
                    return true;
                }
            }
        }
        return false;
    }


    /**
     * 判断多个时间段是否有重叠（交集）
     * @param timePairList 时间段列表
     * @param isStrict 是否严格重叠，true 严格，没有任何相交或相等；false 不严格，可以首尾相等，比如2021-05-29到2021-05-31和2021-05-31到2021-06-01，不重叠。
     * @return 返回是否重叠
     */
    public static boolean isOverlap(List<TimePairUtil> timePairList, boolean isStrict){
        if(CollectionUtil.isEmpty(timePairList)){
            throw new DateTimeException("时间段不能为空");
        }
        TimePairUtil[] timePairs = new TimePairUtil[timePairList.size()];
        timePairList.toArray(timePairs);
        return isOverlap(timePairs, isStrict);
    }

    public static void main(String[] args) {
        TimePairUtil time1 = new TimePairUtil(TimeUtil.timeStrToLong("09:00") , TimeUtil.timeStrToLong("11:00") );
        TimePairUtil time2 = new TimePairUtil(TimeUtil.timeStrToLong("11:00") , TimeUtil.timeStrToLong("15:00") );
        TimePairUtil time3 = new TimePairUtil(TimeUtil.timeStrToLong("18:00") , TimeUtil.timeStrToLong("23:00") );
        TimePairUtil time4 = new TimePairUtil(TimeUtil.timeStrToLong("00:00") , TimeUtil.timeStrToLong("09:00") );

        List<TimePairUtil> list = new ArrayList<>();
        list.add(time1);
        list.add(time2);
        list.add(time3);
        list.add(time4);
        boolean result = isOverlap(list , false);
        System.out.println("result:" + result );
    }

}
