package com.lanhu.lims.gateway.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanhu.lims.gateway.admin.model.DictData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/********************************
 * @title DictDataMapper
 * @package com.lanhu.lims.gateway.admin.mapper
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/13 15:33
 * @version 0.0.1
 *********************************/
@Mapper
public interface DictDataMapper extends BaseMapper<DictData> {

    /**
     * 根据字典类型和值列表查询有效的字典数据
     *
     * @param dictType 字典类型
     * @param values   字典值列表
     * @return 有效的字典数据列表
     */
    List<DictData> selectValidDictDataByTypeAndValues(@Param("dictType") String dictType, @Param("values") List<String> values);
}