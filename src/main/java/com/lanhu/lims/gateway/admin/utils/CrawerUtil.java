package com.lanhu.lims.gateway.admin.utils;

import com.lanhu.lims.gateway.admin.core.ProjectConstant;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/8/19 11:19 上午
 */

public class CrawerUtil {



    /**
     * @description: link标签中href加上url，如果src前面是以http或者https开头则不需要加
     * @param: [html, url]
     * @return: java.lang.String
     * @author: liuyi
     * @date: 10:38 上午 2023/8/19
     */
    public static String linkUrlToHref(String html, String url) {
        // 使用正则表达式替换href属性的值
        String modifiedHtml = html.replaceAll("(<link[^>]+href=\")(?!https?)([^\"]+)(\")", "$1" + url + "$2$3");


        return modifiedHtml;
    }


    /**
     * @description: src标签中src加上url，如果src前面是以http或者https开头则不需要加
     * @param: [html, url]
     * @return: java.lang.String
     * @author: liuyi
     * @date: 10:38 上午 2023/8/19
     */
    public static String scriptUrlToSrc(String html, String url) {
        // 使用正则表达式替换src属性的值
        String modifiedHtml = html.replaceAll("(<script[^>]+src=\")(?!http?)([^\"]+)(\")", "$1" + url + "$2$3");

        return modifiedHtml;
    }


    /**
     * @description: import css标签中src加上url，如果src前面是以http或者https开头则不需要加
     * @param: [html, url]
     * @return: java.lang.String
     * @author: liuyi
     * @date: 10:38 上午 2023/8/19
     */
    public static String importCssUrlToImport(String html, String url) {
        // 使用正则表达式替换src属性的值
        String modifiedHtml = html.replaceAll("(@import url\\(\")(?!https?)([^\\)]+)(\"\\))", "$1" + url + "$2$3");

        return modifiedHtml;
    }


    /**
     * @description: a标签中href加上url，如果src前面是以http或者https开头则不需要加
     * @param: [html, url]
     * @return: java.lang.String
     * @author: liuyi
     * @date: 10:38 上午 2023/8/19
     */
    public static String aUrlToHref(String html, String url) {
        // 使用正则表达式替换src属性的值
        String modifiedHtml = html.replaceAll("(<a[^>]+href=\")(?!https?)([^\"]+)(\")", "$1" + url + "$2$3");

        return modifiedHtml;
    }


    /**
     * @description: image标签中href加上url，如果src前面是以http或者https开头则不需要加
     * @param: [html, url]
     * @return: java.lang.String
     * @author: liuyi
     * @date: 10:38 上午 2023/8/19
     */
    public static String imgUrlToSrc(String html, String url) {
        // 使用正则表达式替换src属性的值
        String modifiedHtml = html.replaceAll("(<img[^>]+src=\")(?!https?)([^\"]+)(\")", "$1" + url + "$2$3");

        return modifiedHtml;
    }

    /**
    * @description: url转域名
    * @param: [url]
    * @return: java.lang.String
    * @author: liuyi
    * @date: 11:54 上午 2023/8/19
    */
    public static String urlToDomain(String url){


        String pattern = "(https?://)[^/]+";

        Pattern regex = Pattern.compile(pattern);
        Matcher matcher = regex.matcher(url);

        while (matcher.find()) {
            String m = matcher.group();
            return m;
        }


        return ProjectConstant.EMPTY;


    }
}
