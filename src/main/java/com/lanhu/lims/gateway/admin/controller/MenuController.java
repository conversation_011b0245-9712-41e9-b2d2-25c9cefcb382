package com.lanhu.lims.gateway.admin.controller;

import com.lanhu.lims.gateway.admin.auth.constants.SecurityConstants;
import com.lanhu.lims.gateway.admin.auth.context.SecurityContextHolder;
import com.lanhu.lims.gateway.admin.core.BaseController;
import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.mapper.MenuMapper;
import com.lanhu.lims.gateway.admin.model.LoginUser;
import com.lanhu.lims.gateway.admin.model.Menu;
import com.lanhu.lims.gateway.admin.service.MenuService;
import com.lanhu.lims.gateway.admin.vo.req.MenuAddForm;
import com.lanhu.lims.gateway.admin.vo.req.MenuDelForm;
import com.lanhu.lims.gateway.admin.vo.req.MenuEditForm;
import com.lanhu.lims.gateway.admin.vo.req.MenuTreeListForm;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/1/11 5:21 下午
 */

@Slf4j
@Api(value = "lims后台-菜单管理", tags = "lims后台-菜单管理")
@RestController
public class MenuController extends BaseController {

    @Autowired
    private MenuService menuService;



    @ApiOperation(value = "菜单树形列表", notes = "菜单树形列表")
    @PostMapping("/menu/v1/treeList")
    public PcsResult<List<Menu>> treeList(@Validated @RequestBody MenuTreeListForm request) {

        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER,LoginUser.class);


        return menuService.treeList(loginUser.getSysUser(),request);
    }


    @ApiOperation(value = "菜单新增", notes = "菜单新增")
    @PostMapping("/menu/v1/add")
    public PcsResult add(@RequestBody MenuAddForm form) {

        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER,LoginUser.class);

        return menuService.add(form,loginUser);
    }


    @ApiOperation(value = "菜单删除", notes = "菜单删除")
    @PostMapping("/menu/v1/del")
    public PcsResult del(@RequestBody MenuDelForm form) {

        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER,LoginUser.class);

        return menuService.del(form,loginUser);

    }


    @ApiOperation(value = "菜单编辑", notes = "菜单编辑")
    @PostMapping("/menu/v1/edit")
    public PcsResult edit(@RequestBody MenuEditForm form) {


        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER,LoginUser.class);


        return menuService.edit(form,loginUser);

    }












}
