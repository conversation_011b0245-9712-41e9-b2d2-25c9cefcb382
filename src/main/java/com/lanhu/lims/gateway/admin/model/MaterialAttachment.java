package com.lanhu.lims.gateway.admin.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/********************************
 * @title MaterialAttachment
 * @package com.lanhu.lims.gateway.admin.model
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/17 12:20
 * @version 0.0.1
 *********************************/
@ApiModel(description = "物资附件表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "t_material_attachment")
public class MaterialAttachment {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 物资ID（如仪器ID、试剂ID等, 维修id）
     */
    @TableField(value = "material_id")
    @ApiModelProperty(value = "物资ID（如仪器ID、试剂ID等, 维修id）")
    private Long materialId;

    /**
     * 附件类型（0：仪器, 1：试剂, 2：耗材, 3：维护记录）
     */
    @TableField(value = "material_type")
    @ApiModelProperty(value = "附件类型（0：仪器, 1：试剂, 2：耗材, 3：维护记录）")
    private Integer materialType;

    /**
     * 文件ID, 对应t_file_record
     */
    @TableField(value = "file_id")
    @ApiModelProperty(value = "文件ID, 对应t_file_record")
    private Long fileId;

    /**
     * 附件名称
     */
    @TableField(value = "attachment_name")
    @ApiModelProperty(value = "附件名称")
    private String attachmentName;

    /**
     * 附件大小（单位：字节）
     */
    @TableField(value = "attachment_size")
    @ApiModelProperty(value = "附件大小（单位：字节）")
    private Long attachmentSize;

    /**
     * 附件URL
     */
    @TableField(value = "attachment_url")
    @ApiModelProperty(value = "附件URL")
    private String attachmentUrl;

    /**
     * 附件后缀
     */
    @TableField(value = "attachment_suffix")
    @ApiModelProperty(value = "附件后缀")
    private String attachmentSuffix;

    /**
     * 是否有效，0：正常，1：删除
     */
    @TableField(value = "is_effect")
    @ApiModelProperty(value = "是否有效，0：正常，1：删除")
    private Integer isEffect;

    /**
     * 创建人ID
     */
    @TableField(value = "create_by")
    @ApiModelProperty(value = "创建人ID")
    private Long createBy;

    /**
     * 创建人姓名
     */
    @TableField(value = "create_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createName;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 修改人ID
     */
    @TableField(value = "update_by")
    @ApiModelProperty(value = "修改人ID")
    private Long updateBy;

    /**
     * 修改人姓名
     */
    @TableField(value = "update_name")
    @ApiModelProperty(value = "修改人姓名")
    private String updateName;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
}