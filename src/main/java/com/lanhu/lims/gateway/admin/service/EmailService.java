package com.lanhu.lims.gateway.admin.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.lanhu.lims.gateway.admin.config.MailSenderConfig;
import com.lanhu.lims.gateway.admin.vo.req.MailRequest;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.util.CollectionUtils;

import javax.mail.internet.MimeMessage;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/10 14:43
 */
@Slf4j
@AllArgsConstructor
public class EmailService {


    private final MailSenderConfig senderConfig;



    public boolean sendMail(MailRequest mailRequest) {
        JavaMailSenderImpl mailSender = null;
        String day = DateUtil.format(DateUtil.date(),"yyyy-MM-dd");
        String sendFailKey = null;
        try{
            mailSender = senderConfig.getSender();
            if(mailSender == null){
                log.warn("获取邮箱服务器失败..");
                return false;
            }
            log.info("mail->username:{}",mailSender.getUsername());
            MimeMessage mimeMessage = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage,true);
            helper.setSubject(mailRequest.getSubject());//标题
            helper.setText(mailRequest.getContent(),true);//内容
            if(!CollectionUtils.isEmpty(mailRequest.getAttachList())){
                for(MailRequest.MailAttachDTO attach : mailRequest.getAttachList()){
                    helper.addAttachment(attach.getAttachName(),attach.getAttachment());//附件，filename：在邮件中展示附件的名字，filepath在当前机器中的路径
                }
            }
            helper.setFrom(Objects.requireNonNull(mailSender.getUsername()));//发送人
            helper.setTo(mailRequest.getReceivers());

            if(mailRequest.getCcReceivers() != null && mailRequest.getCcReceivers().length>=1){
                helper.setCc(mailRequest.getCcReceivers());
            }

            mailSender.send(mimeMessage);
            return true;
        }catch (Exception e){
            log.error("邮件发送失败 mail->username:"+(mailSender == null ? "" : mailSender.getUsername()),e);
            return false;
        }

    }


    /**
     * 异步发送预约邮件
     */
    @Async("asynSendMail")
    public void asynSendMail(String[] receivers,String title,String body){

        log.info("EmailService asynSendMail email:{} , title:{}, body:{}" , JSONUtil.toJsonStr(receivers), title, body);

        MailRequest mailRequest = new MailRequest();
        mailRequest.setSubject(title);
        mailRequest.setContent(body);
        mailRequest.setReceivers(receivers);
        sendMail(mailRequest);
    }



}
