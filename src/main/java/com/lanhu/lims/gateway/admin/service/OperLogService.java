package com.lanhu.lims.gateway.admin.service;

import com.lanhu.lims.gateway.admin.mapper.OperLogMapper;
import com.lanhu.lims.gateway.admin.model.OperLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/10/12 09:58
 */
@Service
@Slf4j
public class OperLogService {


    @Autowired
    private OperLogMapper operLogMapper;

    @Async("operLog")
    public void asyncSaveLog(OperLog operLog) {
        operLogMapper.insert(operLog);
    }
}
