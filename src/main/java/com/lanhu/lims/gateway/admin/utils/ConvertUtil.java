package com.lanhu.lims.gateway.admin.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/********************************
 * @title ConvertUtil
 * @package com.lanhu.lims.gateway.admin.utils
 * @description 通用转换工具类 - 支持分页转换、列表转换、单对象转换等
 *
 * <AUTHOR>
 * @date 2025/6/18 00:00
 * @version 0.0.1
 *********************************/
public class ConvertUtil {

    /**
     * 将IPage<Entity>转换为IPage<VO>
     * 使用BeanUtil.copyProperties进行属性复制
     *
     * @param sourcePage 源分页对象
     * @param targetClass 目标VO类型
     * @param <T> 源实体类型
     * @param <R> 目标VO类型
     * @return 转换后的分页对象
     */
    public static <T, R> IPage<R> convertPage(IPage<T> sourcePage, Class<R> targetClass) {
        if (sourcePage == null) {
            return new Page<>();
        }
        
        Page<R> targetPage = new Page<>(sourcePage.getCurrent(), sourcePage.getSize(), sourcePage.getTotal());
        List<R> targetRecords = sourcePage.getRecords().stream()
                .map(entity -> BeanUtil.copyProperties(entity, targetClass))
                .collect(Collectors.toList());
        targetPage.setRecords(targetRecords);
        return targetPage;
    }

    /**
     * 将IPage<Entity>转换为IPage<VO>
     * 使用自定义转换函数进行转换
     *
     * @param sourcePage 源分页对象
     * @param converter 转换函数
     * @param <T> 源实体类型
     * @param <R> 目标VO类型
     * @return 转换后的分页对象
     */
    public static <T, R> IPage<R> convertPage(IPage<T> sourcePage, Function<T, R> converter) {
        if (sourcePage == null) {
            return new Page<>();
        }
        
        Page<R> targetPage = new Page<>(sourcePage.getCurrent(), sourcePage.getSize(), sourcePage.getTotal());
        List<R> targetRecords = sourcePage.getRecords().stream()
                .map(converter)
                .collect(Collectors.toList());
        targetPage.setRecords(targetRecords);
        return targetPage;
    }

    /**
     * 使用已转换的数据列表创建分页对象
     * 适用于已经完成转换的数据列表，只需要包装成分页结果的场景
     *
     * @param convertedRecords 已转换的数据列表
     * @param sourcePage 源分页对象（用于获取分页信息）
     * @param <T> 转换后的数据类型
     * @return 包装后的分页对象
     */
    public static <T> IPage<T> createPageWithRecords(List<T> convertedRecords, IPage<?> sourcePage) {
        if (sourcePage == null) {
            return new Page<>();
        }

        Page<T> targetPage = new Page<>(sourcePage.getCurrent(), sourcePage.getSize(), sourcePage.getTotal());
        targetPage.setRecords(convertedRecords != null ? convertedRecords : new ArrayList<>());
        return targetPage;
    }

    /**
     * 使用已转换的数据列表创建分页对象
     * 适用于已经完成转换的数据列表，手动指定分页信息的场景
     *
     * @param convertedRecords 已转换的数据列表
     * @param current 当前页
     * @param size 每页大小
     * @param total 总记录数
     * @param <T> 转换后的数据类型
     * @return 包装后的分页对象
     */
    public static <T> IPage<T> createPageWithRecords(List<T> convertedRecords, long current, long size, long total) {
        Page<T> targetPage = new Page<>(current, size, total);
        targetPage.setRecords(convertedRecords != null ? convertedRecords : new ArrayList<>());
        return targetPage;
    }

    /**
     * 创建空的分页对象
     *
     * @param current 当前页
     * @param size 每页大小
     * @param <T> 数据类型
     * @return 空的分页对象
     */
    public static <T> IPage<T> emptyPage(long current, long size) {
        return new Page<>(current, size, 0);
    }

    /**
     * 创建空的分页对象（默认第1页，10条记录）
     *
     * @param <T> 数据类型
     * @return 空的分页对象
     */
    public static <T> IPage<T> emptyPage() {
        return new Page<>(1, 10, 0);
    }

    /**
     * 将List<Entity>转换为List<VO>
     * 使用BeanUtil.copyProperties进行属性复制
     *
     * @param sourceList 源列表
     * @param targetClass 目标VO类型
     * @param <T> 源实体类型
     * @param <R> 目标VO类型
     * @return 转换后的列表
     */
    public static <T, R> List<R> convertList(List<T> sourceList, Class<R> targetClass) {
        if (sourceList == null || sourceList.isEmpty()) {
            return new ArrayList<>();
        }

        return sourceList.stream()
                .map(entity -> BeanUtil.copyProperties(entity, targetClass))
                .collect(Collectors.toList());
    }

    /**
     * 将List<Entity>转换为List<VO>
     * 使用自定义转换函数进行转换
     *
     * @param sourceList 源列表
     * @param converter 转换函数
     * @param <T> 源实体类型
     * @param <R> 目标VO类型
     * @return 转换后的列表
     */
    public static <T, R> List<R> convertList(List<T> sourceList, Function<T, R> converter) {
        if (sourceList == null || sourceList.isEmpty()) {
            return new ArrayList<>();
        }

        return sourceList.stream()
                .map(converter)
                .collect(Collectors.toList());
    }

    // ==================== 单对象转换 ====================

    /**
     * 单对象转换 - 使用BeanUtil自动转换
     *
     * @param source 源对象
     * @param targetClass 目标类型
     * @param <T> 源对象类型
     * @param <R> 目标对象类型
     * @return 转换后的对象
     */
    public static <T, R> R convert(T source, Class<R> targetClass) {
        if (source == null) {
            return null;
        }
        return BeanUtil.copyProperties(source, targetClass);
    }

    /**
     * 单对象转换 - 使用自定义转换函数
     *
     * @param source 源对象
     * @param converter 转换函数
     * @param <T> 源对象类型
     * @param <R> 目标对象类型
     * @return 转换后的对象
     */
    public static <T, R> R convert(T source, Function<T, R> converter) {
        if (source == null) {
            return null;
        }
        return converter.apply(source);
    }

    // ==================== 带关联数据的转换 ====================

    /**
     * 列表转换 - 支持关联数据映射
     * 适用于需要根据某个字段值设置关联名称的场景
     *
     * @param sourceList 源列表
     * @param targetClass 目标VO类型
     * @param keyExtractor 提取关联键的函数
     * @param nameMap 关联数据映射表
     * @param nameSetter 设置关联名称的函数
     * @param <T> 源实体类型
     * @param <R> 目标VO类型
     * @param <K> 关联键类型
     * @return 转换后的列表
     */
    public static <T, R, K> List<R> convertListWithMapping(
            List<T> sourceList,
            Class<R> targetClass,
            Function<T, K> keyExtractor,
            Map<K, String> nameMap,
            java.util.function.BiConsumer<R, String> nameSetter) {

        if (sourceList == null || sourceList.isEmpty()) {
            return new ArrayList<>();
        }

        return sourceList.stream().map(entity -> {
            R vo = BeanUtil.copyProperties(entity, targetClass);
            K key = keyExtractor.apply(entity);
            String name = nameMap.getOrDefault(key, StrUtil.EMPTY);
            nameSetter.accept(vo, name);
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 分页转换 - 支持关联数据映射
     * 适用于需要根据某个字段值设置关联名称的场景
     *
     * @param sourcePage 源分页对象
     * @param targetClass 目标VO类型
     * @param keyExtractor 提取关联键的函数
     * @param nameMap 关联数据映射表
     * @param nameSetter 设置关联名称的函数
     * @param <T> 源实体类型
     * @param <R> 目标VO类型
     * @param <K> 关联键类型
     * @return 转换后的分页对象
     */
    public static <T, R, K> IPage<R> convertPageWithMapping(
            IPage<T> sourcePage,
            Class<R> targetClass,
            Function<T, K> keyExtractor,
            Map<K, String> nameMap,
            java.util.function.BiConsumer<R, String> nameSetter) {

        if (sourcePage == null) {
            return new Page<>();
        }

        Page<R> targetPage = new Page<>(sourcePage.getCurrent(), sourcePage.getSize(), sourcePage.getTotal());
        List<R> targetRecords = convertListWithMapping(sourcePage.getRecords(), targetClass, keyExtractor, nameMap, nameSetter);
        targetPage.setRecords(targetRecords);
        return targetPage;
    }

    // ==================== 便捷方法 ====================

    /**
     * 转换对象并执行后处理
     * 适用于转换后需要额外设置字段的场景
     *
     * @param source 源对象
     * @param targetClass 目标VO类型
     * @param postProcessor 后处理函数
     * @param <T> 源对象类型
     * @param <R> 目标VO类型
     * @return 转换后的VO
     */
    public static <T, R> R convertWithPostProcess(
            T source,
            Class<R> targetClass,
            java.util.function.Consumer<R> postProcessor) {

        if (source == null) {
            return null;
        }

        R vo = BeanUtil.copyProperties(source, targetClass);
        if (postProcessor != null) {
            postProcessor.accept(vo);
        }
        return vo;
    }

    /**
     * 转换对象并执行后处理（可访问源对象）
     * 适用于转换后需要根据源对象设置额外字段的场景
     *
     * @param source 源对象
     * @param targetClass 目标VO类型
     * @param postProcessor 后处理函数（可访问源对象和目标VO）
     * @param <T> 源对象类型
     * @param <R> 目标VO类型
     * @return 转换后的VO
     */
    public static <T, R> R convertWithPostProcess(
            T source,
            Class<R> targetClass,
            java.util.function.BiConsumer<T, R> postProcessor) {

        if (source == null) {
            return null;
        }

        R vo = BeanUtil.copyProperties(source, targetClass);
        if (postProcessor != null) {
            postProcessor.accept(source, vo);
        }
        return vo;
    }

    /**
     * 快速转换详情对象并设置子列表
     * 适用于详情VO包含子对象列表的场景
     *
     * @param source 源对象
     * @param targetClass 目标VO类型
     * @param childList 子对象列表
     * @param childTargetClass 子对象VO类型
     * @param childSetter 设置子列表的函数
     * @param <T> 源对象类型
     * @param <R> 目标VO类型
     * @param <C> 子对象类型
     * @param <CV> 子对象VO类型
     * @return 转换后的详情VO
     */
    public static <T, R, C, CV> R convertDetailWithChildren(
            T source,
            Class<R> targetClass,
            List<C> childList,
            Class<CV> childTargetClass,
            java.util.function.BiConsumer<R, List<CV>> childSetter) {

        if (source == null) {
            return null;
        }

        R vo = BeanUtil.copyProperties(source, targetClass);
        if (CollUtil.isNotEmpty(childList)) {
            List<CV> childVos = convertList(childList, childTargetClass);
            childSetter.accept(vo, childVos);
        }
        return vo;
    }

    // ==================== 业务特定转换 ====================

    /**
     * 转换列表并对每个元素执行自定义处理
     * 提供列表索引和前一个元素的访问，适用于需要基于位置或前后关系进行处理的场景
     *
     * @param sourceList 源数据列表
     * @param targetClass 目标VO类型
     * @param processor 自定义处理函数 (当前VO, 索引, 前一个VO, VO列表)
     * @param <T> 源数据类型
     * @param <R> 目标VO类型
     * @return 转换后的VO列表
     */
    public static <T, R> List<R> convertListWithIndexProcessor(
            List<T> sourceList,
            Class<R> targetClass,
            IndexedProcessor<R> processor) {

        if (CollUtil.isEmpty(sourceList)) {
            return new ArrayList<>();
        }

        // 先转换为VO列表
        List<R> targetVOs = convertList(sourceList, targetClass);

        // 对每个元素执行自定义处理
        for (int i = 0; i < targetVOs.size(); i++) {
            R currentVO = targetVOs.get(i);
            R previousVO = (i > 0) ? targetVOs.get(i - 1) : null;
            processor.process(currentVO, i, previousVO, targetVOs);
        }

        return targetVOs;
    }

    /**
     * 索引处理器接口
     * 用于在列表转换过程中对每个元素进行自定义处理
     *
     * @param <R> 目标VO类型
     */
    @FunctionalInterface
    public interface IndexedProcessor<R> {
        /**
         * 处理单个VO元素
         *
         * @param currentVO 当前VO
         * @param index 当前索引
         * @param previousVO 前一个VO（如果存在）
         * @param allVOs 所有VO列表（可用于更复杂的处理）
         */
        void process(R currentVO, int index, R previousVO, List<R> allVOs);
    }

    /**
     * 转换详情对象并设置子列表（支持自定义处理）
     * 适用于详情VO包含需要特殊处理的子对象列表的场景
     *
     * @param source 源对象
     * @param targetClass 目标VO类型
     * @param childList 子对象列表
     * @param childTargetClass 子对象VO类型
     * @param childProcessor 子列表处理器
     * @param childSetter 设置子列表的函数
     * @param <T> 源对象类型
     * @param <R> 目标VO类型
     * @param <C> 子对象类型
     * @param <CV> 子对象VO类型
     * @return 转换后的详情VO
     */
    public static <T, R, C, CV> R convertDetailWithChildProcessor(
            T source,
            Class<R> targetClass,
            List<C> childList,
            Class<CV> childTargetClass,
            IndexedProcessor<CV> childProcessor,
            java.util.function.BiConsumer<R, List<CV>> childSetter) {

        if (source == null) {
            return null;
        }

        R vo = BeanUtil.copyProperties(source, targetClass);

        if (CollUtil.isNotEmpty(childList)) {
            List<CV> childVOs = convertListWithIndexProcessor(childList, childTargetClass, childProcessor);
            childSetter.accept(vo, childVOs);
        }

        return vo;
    }


}
