package com.lanhu.lims.gateway.admin.service;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.lanhu.lims.gateway.admin.annotation.LhTransaction;
import com.lanhu.lims.gateway.admin.auth.utils.SecurityUtils;
import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.core.PcsResultCode;
import com.lanhu.lims.gateway.admin.core.ProjectConstant;
import com.lanhu.lims.gateway.admin.core.Result;
import com.lanhu.lims.gateway.admin.mapper.BusinessFlowApplicationMapper;
import com.lanhu.lims.gateway.admin.model.AdminUser;
import com.lanhu.lims.gateway.admin.model.BusinessFlowApplication;
import com.lanhu.lims.gateway.admin.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.dromara.warm.flow.core.FlowEngine;
import org.dromara.warm.flow.core.dto.FlowParams;
import org.dromara.warm.flow.core.entity.Definition;
import org.dromara.warm.flow.core.entity.Instance;
import org.dromara.warm.flow.core.exception.FlowException;
import org.dromara.warm.flow.core.service.InsService;
import org.dromara.warm.flow.core.service.TaskService;
import org.dromara.warm.flow.orm.entity.FlowDefinition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @description: 业务通用服务
 * @author: huangzheng
 * @date: 2025/5/8 16:24
 */
@Service
@Slf4j
public class BusinessService {


    @Resource
    private BusinessFlowApplicationMapper businessFlowApplicationMapper;
    @Resource
    private TaskService taskService;

    @Resource
    private BusinessService businessService;
    @Autowired
    private InsService insService;


    @LhTransaction
    @DS("master_1")
    public PcsResult revoke(String businessId, AdminUser user) {

        BusinessFlowApplication businessFlowApplication = businessFlowApplicationMapper.selectById(businessId);

        if (businessFlowApplication == null) {
            return Result.error(PcsResultCode.BUSINESS_NOT_EXISTS);
        }


        // 设置流转参数
        FlowParams flowParams = FlowParams.build();
        // 设置办理人
        flowParams.handler(user.getId().toString());
        // 作为审批意见保存到历史记录表
        flowParams.message(ProjectConstant.TASK_REVOCATION);

        // 流程变量
        Map<String, Object> variable = new HashMap<>();
        flowParams.variable(variable);
        // 业务信息存入flowParams,方便查看历史审批数据  【按需传】
        flowParams.hisTaskExt(JSON.toJSONString(businessFlowApplication));
        Instance instance = taskService.revoke(businessFlowApplication.getInstanceId(), flowParams);
        // 更新业务表
        businessFlowApplication.setNodeCode(instance.getNodeCode());
        businessFlowApplication.setNodeName(instance.getNodeName());
        businessFlowApplication.setNodeType(instance.getNodeType());
        businessFlowApplication.setFlowStatus(Integer.valueOf(instance.getFlowStatus()));
        businessFlowApplicationMapper.updateById(businessFlowApplication);

        return Result.ok();
    }



    @LhTransaction
    @DS("master_1")
    public PcsResult termination(String businessId, AdminUser user) {


        BusinessFlowApplication businessFlowApplication = businessFlowApplicationMapper.selectById(businessId);
        if (businessFlowApplication == null) {
            return Result.error(PcsResultCode.BUSINESS_NOT_EXISTS);
        }
        // 设置流转参数
        FlowParams flowParams = FlowParams.build();
        // 作为办理人保存到历史记录表 【必传】
        flowParams.handler(user.getId().toString());
        flowParams.message(ProjectConstant.TASK_TERMINATION);

        // 请假信息存入flowParams,方便查看历史审批数据  【按需传】
        flowParams.hisTaskExt(JSON.toJSONString(businessFlowApplication));
        // 终止任务
        Instance instance = taskService.terminationByInsId(businessFlowApplication.getInstanceId(), flowParams);
        // 更新业务表
        businessFlowApplication.setNodeCode(instance.getNodeCode());
        businessFlowApplication.setNodeName(instance.getNodeName());
        businessFlowApplication.setNodeType(instance.getNodeType());
        businessFlowApplication.setFlowStatus(Integer.valueOf(instance.getFlowStatus()));
        businessFlowApplicationMapper.updateById(businessFlowApplication);

        return Result.ok();
    }




    /**
     * 提交新增审批任务
     * @param businessCategory 业务类型 必传，如委托单，样本等，参照枚举类枚举 FlowBusinessCategoryEnum
     * @param sourceBusinessId 源业务id 必传
     * @return Long 审批实例id
     */
    @LhTransaction
    @DS("master_1")
    public Long submitAddAudit(Integer businessCategory ,Long sourceBusinessId){
        return businessService.submitAudit(businessCategory,sourceBusinessId,
                null,null,null,
                ProjectConstant.EMPTY,ProjectConstant.EMPTY);
    }




    /**
     * 提交数据修改审批任务
     * @param businessCategory 业务类型 必传，参照枚举类 FlowBusinessCategoryEnum
     * @param sourceBusinessId 源业务id 必传
     * @param beforeData 修改前数据 必传
     * @param afterData 修改后数据 必传
     * @param changeData 变更数据 必传
     * @return Long 审批实例id
     *
     */
    @LhTransaction
    @DS("master_1")
    public Long submitEditAudit(Integer businessCategory ,Long sourceBusinessId, Object beforeData, Object afterData, Object changeData){
        return businessService.submitAudit(businessCategory, sourceBusinessId,
                beforeData, afterData, changeData,
                ProjectConstant.EMPTY,ProjectConstant.EMPTY);
    }



    /**
     * 提交数据删除审批任务
     * @param businessCategory 业务类型 必传，参照枚举类 FlowBusinessCategoryEnum
     * @param sourceBusinessId 源业务id 必传
     * @param reason 原因 必传
     * @return Long 审批实例id
     */
    @LhTransaction
    @DS("master_1")
    public Long submitDelAudit(Integer businessCategory ,Long sourceBusinessId,String reason){
        return businessService.submitAudit(businessCategory, sourceBusinessId,
                null, null, null,
                reason,ProjectConstant.EMPTY);
    }


    /**
     * 提交文件版本复核审批任务
     * @param businessCategory 业务类型 必传，参照枚举类 FlowBusinessCategoryEnum
     * @param sourceBusinessId 源业务id 必传
     * @param reason 原因 必传
     * @return Long 审批实例id
     */
    @LhTransaction
    @DS("master_1")
    public Long submitFileReviewAudit(Integer businessCategory ,Long sourceBusinessId,String reason){
        return businessService.submitAudit(businessCategory, sourceBusinessId,
                null, null, null,
                reason,ProjectConstant.EMPTY);
    }

    
    
    
    
    
    
    /**
     * 提交业务终止审批任务
     * @param businessCategory 业务类型 必传，参照枚举类枚举 FlowBusinessCategoryEnum
     * @param sourceBusinessId 源业务id 必传
     * @param reason 原因 必传
     * @return Long 审批实例id
     */
    @LhTransaction
    @DS("master_1")
    public Long submitBusinessTerminationAudit(Integer businessCategory ,Long sourceBusinessId,String reason){
        return businessService.submitAudit(businessCategory, sourceBusinessId,
                null, null, null,
                reason,ProjectConstant.EMPTY);
    }


    
    /**
     * 提交业务终止审批任务
     * @param businessCategory 业务类型 必传，参照枚举类枚举 FlowBusinessCategoryEnum
     * @param sourceBusinessId 源业务id 必传
     * @param reason 原因 必传
     * @return Long 审批实例id
     */
    @LhTransaction
    @DS("master_1")
    public Long submitBusinessDestructionAudit(Integer businessCategory ,Long sourceBusinessId,String reason){
        return businessService.submitAudit(businessCategory, sourceBusinessId,
                null, null, null,
                reason,ProjectConstant.EMPTY);
    }




    


    /**
     * 提交修改审批任务
     * @param businessCategory 业务类型 必传，参照枚举类枚举 FlowBusinessCategoryEnum
     * @param sourceBusinessId 源业务id 必传
     * @param beforeData 修改前数据
     * @param afterData 修改后数据
     * @param changeData 变更数据
     * @param reason 原因
     * @param remark 备注
     * @return Long 审批实例id
     *
     */
    @LhTransaction
    @DS("master_1")
    public Long submitAudit(Integer businessCategory, Long sourceBusinessId, Object beforeData, Object afterData,Object changeData, String reason, String remark) {


        LoginUser loginUser = SecurityUtils.getLoginUser();


        // 生成业务id  businessFlowApplication的id
        String busId  = IdUtil.getSnowflakeNextIdStr();

        // 根据businessCategory查找流程定义
        FlowDefinition flowDefinition = new FlowDefinition();
        flowDefinition.setCategory(businessCategory.toString());
        Definition definition = FlowEngine.defService().getOne(flowDefinition);
        if (definition == null) {
            throw new FlowException(PcsResultCode.FLOW_DEFINITION_NOT_EXIST.getDesc());
        }


        // 组装流程参数
        FlowParams flowParams = new FlowParams();
        flowParams.flowCode(definition.getFlowCode());
        flowParams.handler(loginUser.getUserId().toString());

        flowParams.message(reason);


        // 启动审批实例
        Instance instance = insService.start(busId, flowParams);

        BusinessFlowApplication businessFlowApplication = new BusinessFlowApplication();
        businessFlowApplication.setId(Long.valueOf(busId));

        // 记录流程信息
        businessFlowApplication.setNodeCode(instance.getNodeCode());
        businessFlowApplication.setNodeName(instance.getNodeName());
        businessFlowApplication.setNodeType(instance.getNodeType());
        businessFlowApplication.setFlowStatus(Integer.valueOf(instance.getFlowStatus()));
        businessFlowApplication.setInstanceId(instance.getId());

        businessFlowApplication.setFlowStatus(Integer.valueOf(instance.getFlowStatus()));

        // 记录业务信息
        businessFlowApplication.setSourceBusinessId(sourceBusinessId);
        businessFlowApplication.setBusType(businessCategory);
        // 序列化数据
        if (beforeData != null) {
            businessFlowApplication.setBeforeData(JSON.toJSONString(beforeData));
        }
        if (afterData != null) {
            businessFlowApplication.setAfterData(JSON.toJSONString(afterData));
        }
        if (changeData != null) {
            businessFlowApplication.setChangeData(JSON.toJSONString(changeData));
        }

        businessFlowApplication.setReason(reason);
        businessFlowApplication.setRemark(remark);


        businessFlowApplication.setCreateTime(new Date());
        businessFlowApplication.setUpdateTime(new Date());
        businessFlowApplication.setCreateBy(loginUser.getUserId());
        businessFlowApplication.setUpdateBy(loginUser.getUserId());
        businessFlowApplication.setCreateName(loginUser.getRealName());
        businessFlowApplication.setUpdateName(loginUser.getRealName());






        businessFlowApplicationMapper.insert(businessFlowApplication);



        return instance.getId();
    }













}
