package com.lanhu.lims.gateway.admin.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/********************************
 * @title ExecutionStandard
 * @package com.lanhu.lims.gateway.admin.model
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/14 16:18
 * @version 0.0.1
 *********************************/

@ApiModel(description = "执行标准表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "t_execution_standard")
public class ExecutionStandard {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 标准编号
     */
    @TableField(value = "standard_code")
    @ApiModelProperty(value = "标准编号")
    private String standardCode;

    /**
     * 标准名称
     */
    @TableField(value = "standard_name")
    @ApiModelProperty(value = "标准名称")
    private String standardName;

    /**
     * 标准类别, 由字典表决定
     */
    @TableField(value = "standard_category")
    @ApiModelProperty(value = "标准类别, 由字典表决定")
    private String standardCategory;

    /**
     * 版本
     */
    @TableField(value = "version")
    @ApiModelProperty(value = "版本")
    private String version;

    /**
     * 附件地址
     */
    @TableField(value = "attachment_url")
    @ApiModelProperty(value = "附件地址")
    private String attachmentUrl;

    /**
     * 生效日期
     */
    @TableField(value = "effective_date")
    @ApiModelProperty(value = "生效日期")
    private Date effectiveDate;

    /**
     * 备注
     */
    @TableField(value = "remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 启用状态：0->禁用；1->启用
     */
    @TableField(value = "`status`")
    @ApiModelProperty(value = "启用状态：0->禁用；1->启用")
    private Integer status;

    /**
     * 是否有效，0：正常，1：删除
     */
    @TableField(value = "is_effect")
    @ApiModelProperty(value = "是否有效，0：正常，1：删除")
    private Integer isEffect;

    /**
     * 创建人ID
     */
    @TableField(value = "create_by")
    @ApiModelProperty(value = "创建人ID")
    private Long createBy;

    /**
     * 创建人姓名
     */
    @TableField(value = "create_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createName;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 修改人ID
     */
    @TableField(value = "update_by")
    @ApiModelProperty(value = "修改人ID")
    private Long updateBy;

    /**
     * 修改人姓名
     */
    @TableField(value = "update_name")
    @ApiModelProperty(value = "修改人姓名")
    private String updateName;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 审批人id
     */
    @TableField(value = "audit_by")
    @ApiModelProperty(value = "审批人id")
    private Long auditBy;

    /**
     * 审批人姓名
     */
    @TableField(value = "audit_name")
    @ApiModelProperty(value = "审批人姓名")
    private String auditName;

    /**
     * 审批时间
     */
    @TableField(value = "audit_time")
    @ApiModelProperty(value = "审批时间")
    private Date auditTime;

    /**
     * 状态(-2: 审核不通过 0:待审核 1: 审核中 2:审核通过/正常)
     */
    @TableField(value = "audit_status")
    @ApiModelProperty(value = "状态(-2: 审核不通过 0:待审核 1: 审核中 2:审核通过/正常)")
    private Integer auditStatus;

    /**
     * 最近一次审批备注或者审批不通过原因
     */
    @TableField(value = "audit_remark")
    @ApiModelProperty(value = "最近一次审批备注或者审批不通过原因")
    private String auditRemark;
}