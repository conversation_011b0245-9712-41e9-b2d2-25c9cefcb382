package com.lanhu.lims.gateway.admin.controller;

import cloud.tianai.captcha.application.ImageCaptchaApplication;
import cloud.tianai.captcha.application.vo.CaptchaResponse;
import cloud.tianai.captcha.application.vo.ImageCaptchaVO;
import cloud.tianai.captcha.common.constant.CaptchaTypeConstant;
import cloud.tianai.captcha.common.response.ApiResponse;
import com.google.common.collect.Maps;
import com.lanhu.lims.gateway.admin.auth.annotation.IgnoreSecurity;
import com.lanhu.lims.gateway.admin.captcha.service.CaptchaService;
import com.lanhu.lims.gateway.admin.core.BaseController;
import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.core.PcsResultCode;
import com.lanhu.lims.gateway.admin.vo.req.CaptchaCheckForm;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2025/5/21 17:27
 */
@RestController
@Validated
@Slf4j
@Api(tags = "图形验证码相关接口",value = "图形验证码相关接口")
public class CaptchaController extends BaseController {

    @Autowired
    private ImageCaptchaApplication application;

    @Autowired
    private CaptchaService captchaService;

    
    /**
    * @description: 图形验证码生成
    * @param: []
    * @return: cloud.tianai.captcha.application.vo.CaptchaResponse<cloud.tianai.captcha.application.vo.ImageCaptchaVO>
    * @author: liuyi
    * @date: 14:07 2025/5/22 
    */
    @PostMapping("/captcha/gen")
    @IgnoreSecurity
    public CaptchaResponse<ImageCaptchaVO> genCaptcha() {
        // 1.生成验证码(该数据返回给前端用于展示验证码数据)
        // 参数1为具体的验证码类型， 默认支持 SLIDER、ROTATE、WORD_IMAGE_CLICK、CONCAT 等验证码类型，详见： `CaptchaTypeConstant`类
        CaptchaResponse<ImageCaptchaVO> response =   application.generateCaptcha(CaptchaTypeConstant.SLIDER);

        return response;
    }

    /**
    * @description: 图形验证码验证
    * @param: [data]
    * @return: cloud.tianai.captcha.common.response.ApiResponse<?>
    * @author: liuyi
    * @date: 14:07 2025/5/22 
    */
    @IgnoreSecurity
    @PostMapping("/captcha/check")
    public ApiResponse<?> checkCaptcha(@RequestBody CaptchaCheckForm data) {

        ApiResponse<?> response = application.matching(data.getId(), data.getData());

        if (response.isSuccess()) {

            PcsResult<String> result = captchaService.sendPicCaptcha(data.getId());

            if(result.getCode() != PcsResultCode.SUCCESS.getCode()){

                return ApiResponse.ofError(result.getMessage());

            }

            Map<String,String> map = Maps.newHashMap();
            map.put("uid",result.getData());
            map.put("captcha",data.getId());
            return ApiResponse.ofSuccess(map);



        }
        return response;
    }
}
