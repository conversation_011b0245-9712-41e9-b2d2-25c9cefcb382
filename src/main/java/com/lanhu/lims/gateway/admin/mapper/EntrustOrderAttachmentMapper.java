package com.lanhu.lims.gateway.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanhu.lims.gateway.admin.model.EntrustOrderAttachment;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @description: ${DESCRIPTION}
 * @author: huangzheng
 * @date: 2025/6/13 13:11
 */
@Mapper
public interface EntrustOrderAttachmentMapper extends BaseMapper<EntrustOrderAttachment> {
    int updateBatch(@Param("list") List<EntrustOrderAttachment> list);

    int updateBatchSelective(@Param("list") List<EntrustOrderAttachment> list);

    int batchInsert(@Param("list") List<EntrustOrderAttachment> list);

    int batchInsertOrUpdate(@Param("list") List<EntrustOrderAttachment> list);

    int insertOnDuplicateUpdate(EntrustOrderAttachment record);

    int insertOnDuplicateUpdateSelective(EntrustOrderAttachment record);
}