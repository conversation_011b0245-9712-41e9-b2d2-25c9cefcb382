package com.lanhu.lims.gateway.admin.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lanhu.lims.gateway.admin.enums.IsEffectEnum;
import com.lanhu.lims.gateway.admin.mapper.DictDataCategoryMapper;
import com.lanhu.lims.gateway.admin.model.DictDataCategory;
import com.lanhu.lims.gateway.admin.vo.resp.DictDataCategoryVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/********************************
 * @title DictDataCategoryService
 * @package com.lanhu.lims.gateway.admin.service
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/12 22:54
 * @version 0.0.1
 *********************************/
@Service
public class DictDataCategoryService {
    @Resource
    private DictDataCategoryMapper dictDataTypeMapper;

    /**
     * 字典分类列表
     */
    @DS("slave_1")
    public List<DictDataCategoryVO> list() {
        LambdaQueryWrapper<DictDataCategory> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(DictDataCategory::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.orderByAsc(DictDataCategory::getOrd);
        List<DictDataCategory> list = dictDataTypeMapper.selectList(wrapper);
        return list.stream().map(entity -> {
            DictDataCategoryVO vo = new DictDataCategoryVO();
            BeanUtil.copyProperties(entity, vo);
            return vo;
        }).collect(Collectors.toList());
    }
}
