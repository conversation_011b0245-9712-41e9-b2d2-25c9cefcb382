package com.lanhu.lims.gateway.admin.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/********************************
 * @title StaticDataAttachment
 * @package com.lanhu.lims.gateway.admin.model
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/13 21:23
 * @version 0.0.1
 *********************************/

@ApiModel(description = "静态数据附件表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "t_static_data_attachment")
public class StaticDataAttachment {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 数据id
     */
    @TableField(value = "data_id")
    @ApiModelProperty(value = "数据id")
    private Long dataId;

    /**
     * 附件类型
     */
    @TableField(value = "attachment_type")
    @ApiModelProperty(value = "附件类型")
    private String attachmentType;

    /**
     * 附件编号
     */
    @TableField(value = "attachment_code")
    @ApiModelProperty(value = "附件编号")
    private String attachmentCode;

    /**
     * 生效日期
     */
    @TableField(value = "effective_date")
    @ApiModelProperty(value = "生效日期")
    private Date effectiveDate;

    /**
     * 版本
     */
    @TableField(value = "version")
    @ApiModelProperty(value = "版本")
    private String version;

    /**
     * 文件ID, 对应t_file_record
     */
    @TableField(value = "file_id")
    @ApiModelProperty(value = "文件ID, 对应t_file_record")
    private Long fileId;

    /**
     * 附件名称
     */
    @TableField(value = "attachment_name")
    @ApiModelProperty(value = "附件名称")
    private String attachmentName;

    /**
     * 附件url
     */
    @TableField(value = "attachment_url")
    @ApiModelProperty(value = "附件url")
    private String attachmentUrl;

    /**
     * 附件后缀
     */
    @TableField(value = "attachment_suffix")
    @ApiModelProperty(value = "附件后缀")
    private String attachmentSuffix;

    /**
     * 附件大小
     */
    @TableField(value = "attachment_size")
    @ApiModelProperty(value = "附件大小")
    private Long attachmentSize;

    /**
     * 创建人id
     */
    @TableField(value = "create_by")
    @ApiModelProperty(value = "创建人id")
    private Long createBy;

    /**
     * 创建人姓名
     */
    @TableField(value = "create_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createName;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 修改人id
     */
    @TableField(value = "update_by")
    @ApiModelProperty(value = "修改人id")
    private Long updateBy;

    /**
     * 修改人姓名
     */
    @TableField(value = "update_name")
    @ApiModelProperty(value = "修改人姓名")
    private String updateName;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 状态(-2: 审核不通过 0:待审核 1: 审核中 2:审核通过/正常)
     */
    @TableField(value = "audit_status")
    @ApiModelProperty(value = "状态(-2: 审核不通过 0:待审核 1: 审核中 2:审核通过/正常)")
    private Integer auditStatus;

    /**
     * 审核人id
     */
    @TableField(value = "audit_by")
    @ApiModelProperty(value = "审核人id")
    private Long auditBy;

    /**
     * 审核人姓名
     */
    @TableField(value = "audit_name")
    @ApiModelProperty(value = "审核人姓名")
    private String auditName;

    /**
     * 审批时间
     */
    @TableField(value = "audit_time")
    @ApiModelProperty(value = "审批时间")
    private Date auditTime;

    /**
     * 最近一次审批备注或者审批不通过原因
     */
    @TableField(value = "audit_remark")
    @ApiModelProperty(value = "最近一次审批备注或者审批不通过原因")
    private String auditRemark;
}