package com.lanhu.lims.gateway.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanhu.lims.gateway.admin.model.Captcha;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface CaptchaMapper extends BaseMapper<Captcha> {
    int updateBatch(List<Captcha> list);

    int batchInsert(@Param("list") List<Captcha> list);

    int insertOrUpdate(Captcha record);

    int insertOrUpdateSelective(Captcha record);
}