package com.lanhu.lims.gateway.admin.utils.sequence.generator;

/********************************
 * @title DynamicSequenceNameGeneratorFactory
 * @package com.lanhu.lims.gateway.admin.utils.sequence.generator
 * @description 动态序列名称生成器工厂类
 *
 * <AUTHOR>
 * @date 2025/6/9 22:30
 * @version 0.0.1
 *********************************/
public class DynamicSequenceNameGeneratorFactory {

    /**
     * 创建按年月分组的动态序列名称生成器
     *
     * @param prefix 前缀
     * @return 动态序列名称生成器
     */
    public static DynamicSequenceNameGenerator yearMonth(String prefix) {
        return DateBasedSequenceNameGenerator.yearMonth(prefix);
    }

    /**
     * 创建按年月日分组的动态序列名称生成器
     *
     * @param prefix 前缀
     * @return 动态序列名称生成器
     */
    public static DynamicSequenceNameGenerator yearMonthDay(String prefix) {
        return DateBasedSequenceNameGenerator.yearMonthDay(prefix);
    }

    /**
     * 创建按年分组的动态序列名称生成器
     *
     * @param prefix 前缀
     * @return 动态序列名称生成器
     */
    public static DynamicSequenceNameGenerator year(String prefix) {
        return DateBasedSequenceNameGenerator.year(prefix);
    }

    /**
     * 创建按年周分组的动态序列名称生成器
     *
     * @param prefix 前缀
     * @return 动态序列名称生成器
     */
    public static DynamicSequenceNameGenerator yearWeek(String prefix) {
        return DateBasedSequenceNameGenerator.yearWeek(prefix);
    }

    /**
     * 创建自定义日期格式的动态序列名称生成器
     *
     * @param prefix 前缀
     * @param datePattern 日期格式模式
     * @return 动态序列名称生成器
     */
    public static DynamicSequenceNameGenerator custom(String prefix, String datePattern) {
        return new DateBasedSequenceNameGenerator(prefix, datePattern);
    }
}
