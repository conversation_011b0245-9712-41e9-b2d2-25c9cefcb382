package com.lanhu.lims.gateway.admin.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanhu.lims.gateway.admin.core.BusinessException;
import com.lanhu.lims.gateway.admin.core.PcsResultCode;
import com.lanhu.lims.gateway.admin.enums.AuditStatusEnum;
import com.lanhu.lims.gateway.admin.enums.EnableEnum;
import com.lanhu.lims.gateway.admin.enums.IsEffectEnum;
import com.lanhu.lims.gateway.admin.mapper.DetectionMethodMapper;
import com.lanhu.lims.gateway.admin.mapper.DetectionProjectMapper;
import com.lanhu.lims.gateway.admin.mapper.FileRecordMapper;
import com.lanhu.lims.gateway.admin.mapper.StaticDataAttachmentMapper;
import com.lanhu.lims.gateway.admin.model.*;
import com.lanhu.lims.gateway.admin.utils.ConvertUtil;
import com.lanhu.lims.gateway.admin.vo.req.*;
import com.lanhu.lims.gateway.admin.vo.resp.DetectionMethodDetailVO;
import com.lanhu.lims.gateway.admin.vo.resp.DetectionMethodListVO;
import com.lanhu.lims.gateway.admin.vo.resp.StaticDataAttachmentVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/********************************
 * @title DetectionMethodService
 * @package com.lanhu.lims.gateway.admin.service
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/13 21:23
 * @version 0.0.1
 *********************************/
@Service
public class DetectionMethodService {
    @Resource
    private DetectionMethodMapper detectionMethodMapper;

    @Resource
    private StaticDataAttachmentMapper staticDataAttachmentMapper;

    @Resource
    private FileRecordMapper fileRecordMapper;

    @Resource
    private DetectionProjectMapper detectionProjectMapper;

    /**
     * 查询检测方法列表
     *
     * @param form 查询入参
     * @return 列表
     */
    @DS("slave_1")
    public List<DetectionMethodListVO> list(DetectionMethodListForm form) {
        LambdaQueryWrapper<DetectionMethod> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(DetectionMethod::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.eq(DetectionMethod::getStatus, EnableEnum.ENABLE.getCode());
        wrapper.like(StrUtil.isNotBlank(form.getMethodCode()), DetectionMethod::getMethodCode, form.getMethodCode());
        wrapper.like(StrUtil.isNotBlank(form.getMethodName()), DetectionMethod::getMethodName, form.getMethodName());
        wrapper.ge(form.getEffectiveStartDate() != null, DetectionMethod::getEffectiveDate, form.getEffectiveStartDate());
        wrapper.le(form.getEffectiveEndDate() != null, DetectionMethod::getEffectiveDate, form.getEffectiveEndDate());
        wrapper.orderByDesc(DetectionMethod::getCreateTime);
        List<DetectionMethod> list = detectionMethodMapper.selectList(wrapper);
        return ConvertUtil.convertList(list, DetectionMethodListVO.class);
    }

    /**
     * 分页查询检测方法
     *
     * @param form 查询入参
     * @return 分页列表
     */
    @DS("slave_1")
    public IPage<DetectionMethodListVO> listPage(DetectionMethodListPageForm form) {
        LambdaQueryWrapper<DetectionMethod> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(DetectionMethod::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.eq(form.getStatus() != null, DetectionMethod::getStatus, form.getStatus());
        wrapper.like(StrUtil.isNotBlank(form.getMethodCode()), DetectionMethod::getMethodCode, form.getMethodCode());
        wrapper.like(StrUtil.isNotBlank(form.getMethodName()), DetectionMethod::getMethodName, form.getMethodName());
        wrapper.ge(form.getEffectiveStartDate() != null, DetectionMethod::getEffectiveDate, form.getEffectiveStartDate());
        wrapper.le(form.getEffectiveEndDate() != null, DetectionMethod::getEffectiveDate, form.getEffectiveEndDate());
        wrapper.orderByDesc(DetectionMethod::getCreateTime);
        Page<DetectionMethod> page = new Page<>(form.getPageIndex(), form.getPageSize());
        IPage<DetectionMethod> result = detectionMethodMapper.selectPage(page, wrapper);
        // 转换为VO分页结果
        return ConvertUtil.convertPage(result, DetectionMethodListVO.class);
    }

    /**
     * 查询检测方法详情
     *
     * @param form 查询入参
     * @return 详情
     */
    @DS("slave_1")
    public DetectionMethodDetailVO detail(DetectionMethodSingleForm form) {
        DetectionMethod detectionMethod = detectionMethodMapper.selectById(form.getId());
        if (detectionMethod == null || detectionMethod.getIsEffect() != IsEffectEnum.NORMAL.getCode()) {
            throw new BusinessException(PcsResultCode.DETECTION_METHOD_NOT_EXIST);
        }

        // 转换为详情VO
        DetectionMethodDetailVO vo = ConvertUtil.convertDetailWithChildren(detectionMethod,
                DetectionMethodDetailVO.class,
                staticDataAttachmentMapper.selectList(Wrappers.<StaticDataAttachment>lambdaQuery().eq(StaticDataAttachment::getDataId, detectionMethod.getId())),
                StaticDataAttachmentVO.class,
                DetectionMethodDetailVO::setAttachments);
        // 处理附件其他信息
        if (CollUtil.isNotEmpty(vo.getAttachments())) {
            // 处理附件其他信息
            int size = CollUtil.size(vo.getAttachments());
            Date now = DateUtil.date();
            for (int i = 0; i < size; i++) {
                StaticDataAttachmentVO attachmentVo = vo.getAttachments().get(i);
                attachmentVo.setExpirationDate(i == 0 ? null : vo.getAttachments().get(i - 1).getEffectiveDate());
                attachmentVo.setIsEffective(DateUtil.compare(attachmentVo.getEffectiveDate(), now) <= 0 && DateUtil.compare(now, attachmentVo.getExpirationDate()) < 0);
            }
        }
        return vo;
    }

    /**
     * 新增检测方法
     *
     * @param form      新增入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void add(DetectionMethodAddForm form, AdminUser adminUser) {
        // 查询文件记录
        FileRecord fileRecord = fileRecordMapper.selectById(form.getFileRecordId());
        if (fileRecord == null) {
            throw new BusinessException(PcsResultCode.FILE_RECORD_NOT_EXIST);
        }
        // 新增检测方法
        DetectionMethod detectionMethod = new DetectionMethod();
        detectionMethod.setMethodCode(form.getMethodCode());
        detectionMethod.setMethodName(form.getMethodName());
        detectionMethod.setVersion(form.getVersion());
        detectionMethod.setAttachmentUrl(fileRecord.getAttachmentUrl());
        detectionMethod.setEffectiveDate(form.getEffectiveDate());
        detectionMethod.setRemark(StrUtil.blankToDefault(form.getRemark(), StrUtil.EMPTY));
        detectionMethod.setStatus(EnableEnum.ENABLE.getCode());
        detectionMethod.setIsEffect(IsEffectEnum.NORMAL.getCode());
        detectionMethod.setCreateBy(adminUser.getId());
        detectionMethod.setCreateName(adminUser.getRealName());
        detectionMethod.setCreateTime(DateUtil.date());
        detectionMethod.setUpdateBy(adminUser.getId());
        detectionMethod.setUpdateName(adminUser.getRealName());
        detectionMethod.setUpdateTime(detectionMethod.getCreateTime());
        detectionMethod.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        detectionMethod.setAuditName(StrUtil.EMPTY);
        detectionMethod.setAuditBy(null);
        detectionMethod.setAuditTime(null);
        detectionMethod.setAuditRemark(StrUtil.EMPTY);
        detectionMethodMapper.insert(detectionMethod);
        // 保存附件
        StaticDataAttachment attachment = saveAttachment(detectionMethod.getId(), detectionMethod.getMethodCode(), detectionMethod.getEffectiveDate(), detectionMethod.getVersion(), fileRecord, adminUser);
        // todo 发起流程
    }

    /**
     * 修改检测方法
     *
     * @param form      修改入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void edit(DetectionMethodEditForm form, AdminUser adminUser) {
        // 校验数据
        DetectionMethod detectionMethod = checkCanEditOrDelete(form.getId());
        // 修改检测方法
        detectionMethod.setMethodName(form.getMethodName());
        detectionMethod.setRemark(StrUtil.blankToDefault(form.getRemark(), StrUtil.EMPTY));
        detectionMethod.setUpdateBy(adminUser.getId());
        detectionMethod.setUpdateName(adminUser.getRealName());
        detectionMethod.setUpdateTime(DateUtil.date());
        detectionMethod.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        detectionMethodMapper.updateById(detectionMethod);
        // todo 发起流程
    }

    /**
     * 删除检测方法
     *
     * @param form      删除入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void del(DetectionMethodDelForm form, AdminUser adminUser) {
        // 校验数据
        DetectionMethod detectionMethod = checkCanEditOrDelete(form.getId());
        // 校验是否有检验项目引用
        LambdaQueryWrapper<DetectionProject> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(DetectionProject::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.apply("find_in_set(" + form.getId() + ", detection_method)");
        Long count = detectionProjectMapper.selectCount(wrapper);
        if (count > 0) {
            throw new BusinessException(PcsResultCode.DETECTION_METHOD_BIND_PROJECT);
        }
        // 删除检测方法
        detectionMethod.setIsEffect(IsEffectEnum.DELETE.getCode());
        detectionMethod.setUpdateBy(adminUser.getId());
        detectionMethod.setUpdateName(adminUser.getRealName());
        detectionMethod.setUpdateTime(DateUtil.date());
        detectionMethod.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        detectionMethodMapper.updateById(detectionMethod);
        // todo 发起流程
    }

    /**
     * 启用检测方法
     *
     * @param form      启用入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void enable(DetectionMethodSingleForm form, AdminUser adminUser) {
        // 更新状态
        updateStatus(form.getId(), EnableEnum.ENABLE, adminUser);
    }

    /**
     * 禁用检测方法
     *
     * @param form      禁用入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void disable(DetectionMethodSingleForm form, AdminUser adminUser) {
        // 更新状态
        updateStatus(form.getId(), EnableEnum.DISABLE, adminUser);
    }

    /**
     * 新增检测方法版本
     *
     * @param form      新增入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void addVersion(DetectionMethodVersionAddForm form, AdminUser adminUser) {
        // 查询文件记录
        FileRecord fileRecord = fileRecordMapper.selectById(form.getFileRecordId());
        if (fileRecord == null) {
            throw new BusinessException(PcsResultCode.FILE_RECORD_NOT_EXIST);
        }
        // 校验数据
        DetectionMethod detectionMethod = checkCanEditOrDelete(form.getId());
        // 保存附件
        StaticDataAttachment attachment = saveAttachment(detectionMethod.getId(), form.getMethodCode(), form.getEffectiveDate(), form.getVersion(), fileRecord, adminUser);
        // todo 发起流程
    }

    // =================================================================== 工作流回调 =================================================================== //

    // =================================================================== 私有方法区 =================================================================== //

    /**
     * 校验检测方法是否允许修改或删除
     *
     * @param id 检测方法ID
     * @return 检测方法
     */
    private DetectionMethod checkCanEditOrDelete(Long id) {
        DetectionMethod detectionMethod = detectionMethodMapper.selectById(id);
        if (detectionMethod == null || ObjectUtil.notEqual(IsEffectEnum.NORMAL.getCode(), detectionMethod.getIsEffect())) {
            throw new BusinessException(PcsResultCode.SYSTEM_FILE_NOT_EXIST);
        }
        if (ObjectUtil.notEqual(AuditStatusEnum.PASS.getCode(), detectionMethod.getAuditStatus())) {
            throw new BusinessException(PcsResultCode.DATA_WAS_AUDITING_CANT_EDIT_OR_DELETE);
        }
        return detectionMethod;
    }

    /**
     * 保存附件
     *
     * @param detectionMethodId   检测方法id
     * @param detectionMethodCode 检测方法
     * @param effectiveDate       生效日期
     * @param version             版本
     * @param fileRecord          文件记录
     * @param adminUser           当前用户
     */
    private StaticDataAttachment saveAttachment(Long detectionMethodId, String detectionMethodCode, Date effectiveDate, String version, FileRecord fileRecord, AdminUser adminUser) {
        // 新增附件
        StaticDataAttachment staticDataAttachment = new StaticDataAttachment();
        staticDataAttachment.setId(IdUtil.getSnowflakeNextId());
        staticDataAttachment.setDataId(detectionMethodId);
        staticDataAttachment.setAttachmentType(StrUtil.EMPTY);
        staticDataAttachment.setAttachmentCode(detectionMethodCode);
        staticDataAttachment.setEffectiveDate(effectiveDate);
        staticDataAttachment.setVersion(version);
        staticDataAttachment.setFileId(fileRecord.getId());
        staticDataAttachment.setAttachmentUrl(fileRecord.getAttachmentUrl());
        staticDataAttachment.setAttachmentName(fileRecord.getAttachmentName());
        staticDataAttachment.setAttachmentSuffix(fileRecord.getAttachmentSuffix());
        staticDataAttachment.setAttachmentSize(fileRecord.getAttachmentSize());
        staticDataAttachment.setCreateBy(adminUser.getId());
        staticDataAttachment.setCreateName(adminUser.getRealName());
        staticDataAttachment.setCreateTime(DateUtil.date());
        staticDataAttachment.setUpdateBy(adminUser.getId());
        staticDataAttachment.setUpdateName(adminUser.getRealName());
        staticDataAttachment.setUpdateTime(staticDataAttachment.getCreateTime());
        staticDataAttachment.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        staticDataAttachment.setAuditName(StrUtil.EMPTY);
        staticDataAttachment.setAuditBy(null);
        staticDataAttachment.setAuditTime(null);
        staticDataAttachment.setAuditRemark(StrUtil.EMPTY);
        staticDataAttachmentMapper.insert(staticDataAttachment);
        return staticDataAttachment;
    }

    /**
     * 更新检测方法状态
     *
     * @param id         检测方法id
     * @param enableEnum 启用状态
     * @param adminUser  当前用户
     */
    private void updateStatus(Long id, EnableEnum enableEnum, AdminUser adminUser) {
        // 校验数据
        DetectionMethod detectionMethod = detectionMethodMapper.selectById(id);
        if (detectionMethod == null || detectionMethod.getIsEffect() != IsEffectEnum.NORMAL.getCode()) {
            throw new BusinessException(PcsResultCode.DETECTION_METHOD_NOT_EXIST);
        }
        detectionMethod.setStatus(enableEnum.getCode());
        detectionMethod.setUpdateBy(adminUser.getId());
        detectionMethod.setUpdateName(adminUser.getRealName());
        detectionMethod.setUpdateTime(DateUtil.date());
        detectionMethod.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        detectionMethodMapper.updateById(detectionMethod);
    }
}
