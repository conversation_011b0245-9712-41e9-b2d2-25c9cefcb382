package com.lanhu.lims.gateway.admin.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/********************************
 * @title MaterialInventoryOrder
 * @package com.lanhu.lims.gateway.admin.model
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/17 12:35
 * @version 0.0.1
 *********************************/

/**
 * 物料出入库订单表
 */
@ApiModel(description = "物料出入库订单表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "t_material_inventory_order")
public class MaterialInventoryOrder {
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "")
    private Long id;

    /**
     * 批次ID
     */
    @TableField(value = "batch_id")
    @ApiModelProperty(value = "批次ID")
    private Long batchId;

    /**
     * 物料类型（ 1：试剂, 2：耗材）
     */
    @TableField(value = "material_type")
    @ApiModelProperty(value = "物料类型（ 1：试剂, 2：耗材）")
    private Integer materialType;

    /**
     * 试剂/耗材库存ID
     */
    @TableField(value = "inventory_id")
    @ApiModelProperty(value = "试剂/耗材库存ID")
    private Long inventoryId;

    /**
     * 操作类型(0：入库，1：出库)
     */
    @TableField(value = "operation_type")
    @ApiModelProperty(value = "操作类型(0：入库，1：出库)")
    private Integer operationType;

    /**
     * 变动库存
     */
    @TableField(value = "change_inventory")
    @ApiModelProperty(value = "变动库存")
    private BigDecimal changeInventory;

    /**
     * 试剂/耗材名称
     */
    @TableField(value = "material_name")
    @ApiModelProperty(value = "试剂/耗材名称")
    private String materialName;

    /**
     * 试剂/耗材编码
     */
    @TableField(value = "material_code")
    @ApiModelProperty(value = "试剂/耗材编码")
    private String materialCode;

    /**
     * 试剂/耗材简称
     */
    @TableField(value = "material_short_name")
    @ApiModelProperty(value = "试剂/耗材简称")
    private String materialShortName;

    /**
     * 试剂/耗材品牌
     */
    @TableField(value = "material_brand")
    @ApiModelProperty(value = "试剂/耗材品牌")
    private String materialBrand;

    /**
     * 试剂/耗材货号
     */
    @TableField(value = "material_catalog_number")
    @ApiModelProperty(value = "试剂/耗材货号")
    private String materialCatalogNumber;

    /**
     * 试剂/耗材规格
     */
    @TableField(value = "material_specification")
    @ApiModelProperty(value = "试剂/耗材规格")
    private String materialSpecification;

    /**
     * 试剂/耗材计量单位
     */
    @TableField(value = "material_unit")
    @ApiModelProperty(value = "试剂/耗材计量单位")
    private String materialUnit;

    /**
     * 存储位置id，多级位置，例如 1-2-3
     */
    @TableField(value = "store_position")
    @ApiModelProperty(value = "存储位置id，多级位置，例如 1-2-3")
    private String storePosition;

    /**
     * 备注
     */
    @TableField(value = "remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 状态(0:待提交 1: 审核中 2:操作成功)
     */
    @TableField(value = "`status`")
    @ApiModelProperty(value = "状态(0:待提交 1: 审核中 2:操作成功)")
    private Integer status;

    /**
     * 是否有效，0：正常，1：删除
     */
    @TableField(value = "is_effect")
    @ApiModelProperty(value = "是否有效，0：正常，1：删除")
    private Integer isEffect;

    /**
     * 创建人ID
     */
    @TableField(value = "create_by")
    @ApiModelProperty(value = "创建人ID")
    private Long createBy;

    /**
     * 创建人姓名
     */
    @TableField(value = "create_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createName;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 修改人ID
     */
    @TableField(value = "update_by")
    @ApiModelProperty(value = "修改人ID")
    private Long updateBy;

    /**
     * 修改人姓名
     */
    @TableField(value = "update_name")
    @ApiModelProperty(value = "修改人姓名")
    private String updateName;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
}