package com.lanhu.lims.gateway.admin.utils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Date 2021/8/18 14:51
 * @Version 1.0
 */
public class TimeUtil {

    public static int getSecondTimestampTwo(Date date){
        if (null == date) {
            return 0;
        }
        String timestamp = String.valueOf(date.getTime()/1000);
        return Integer.valueOf(timestamp);
    }

    /**
     * 获取当前日期是星期几
     * @return 当前日期是星期几
     */
    public static String getWeekOfDate() {
        Date date = new Date();
        //String[] weekDays = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
        String[] weekDays = {"日", "月", "火", "水", "木", "金", "土"};
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        System.out.println("getWeekOfDate w:" + w);
        if (w < 0)
            w = 0;
        String week =  weekDays[w];
        System.out.println("getWeekOfDate week:" + week);
        return "(" + week + ")";
    }

    public static String getCurrentYMD() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日");
        String ymd = sdf.format(new Date());
        return ymd;
    }

    public static String getCurrentYMD2() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
        String ymd = sdf.format(new Date());
        return ymd;
    }

    public static String getCurrentHM() {
        SimpleDateFormat sdf = new SimpleDateFormat("HH时mm分");
        String hm = sdf.format(new Date());
        return hm;
    }

    public static String getCurrentHM2() {
        SimpleDateFormat sdf = new SimpleDateFormat("HH時mm分");
        String hm = sdf.format(new Date());
        return hm;
    }

    public static String getCurrentHM3() {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        String hm = sdf.format(new Date());
        return hm;
    }

    public static String getHMStr(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        String hm = sdf.format(date);
        return hm;
    }

    public static String getCurrentHMS4() {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
        String hm = sdf.format(new Date());
        System.out.println("TimeUtil getCurrentHMS4 time:" + new Date().getTime() + " ,hm:" + hm);
        return hm;
    }

    /**
     * 计算两个日期是否 超过一个月
     * @param startDate
     * @param endDate
     * @return  true 超过     false 不超过
     */
    public static boolean calaDateRange(Date startDate , Date endDate ) {
        long startTime = startDate.getTime();
        long endTime = endDate.getTime();
        long diff = endTime - startTime;
        long diffDays = diff  / (24 * 60 * 60 * 1000);
        if (diffDays > 30){
            return true;
        }
        return false;
    }

    public static Long[] startTimeToEndTime( ) {
        Long[] startEndTime = new Long[2];
        try {
            SimpleDateFormat fmt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
            Date date = new Date();
            String da = df.format(date);
            String ss1 = da + " 00:00:00";
            String ss2 = da + " 23:59:59";

            Date parse = fmt.parse(ss1);
            Date parse2 = fmt.parse(ss2);

            System.out.println("秒1:" + parse.getTime() / 1000);
            System.out.println("秒2:" + parse2.getTime() / 1000);
            startEndTime[0] = parse.getTime() / 1000 ;
            startEndTime[1] = parse2.getTime() / 1000 ;

            DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date2 = new Date(parse.getTime());
            Date date3 = new Date(parse2.getTime());
            String format = formatter.format(date2);
            String format2 = formatter.format(date3);
            System.out.println(format);
            System.out.println(format2);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return startEndTime;
    }

    public static Date getYesterday() {
        try{
            //SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
            Date date = new Date();
            Calendar now = Calendar.getInstance();
            now.setTime(date);
            now.add(Calendar.DAY_OF_MONTH, -1);
            now.set(Calendar.HOUR_OF_DAY , 23);
            now.set(Calendar.MINUTE , 00);
            now.set(Calendar.SECOND , 00);
            System.out.println("TimeUtil getYesterday:" + now.getTime().getTime());

            SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String str = sdf2.format(now.getTime());
            System.out.println("TimeUtil getYesterday str :" + str );
            Date newDate = sdf2.parse(str);
            return newDate;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 本月开始日期 第一天
     * @return
     */
    public static Date getThisMonthStartDate() {
        try{
            Date date = new Date();
            Calendar now = Calendar.getInstance();
            now.setTime(date);
            //now.set(Calendar.DAY_OF_MONTH, 01);

            now.add(Calendar.MONTH, 0);
            now.set(Calendar.DAY_OF_MONTH, 1);

            now.set(Calendar.HOUR_OF_DAY , 00);
            now.set(Calendar.MINUTE , 00);
            now.set(Calendar.SECOND , 00);
            SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String str = sdf2.format(now.getTime());
            System.out.println("TimeUtil getThisMonthStartDate str :" + str );
            Date newDate = sdf2.parse(str);
            return newDate;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 本月结束日期 本月前一天
     * @return
     */
    public static Date getThisMonthEndDate() {
        try{
            Date date = new Date();
            Calendar now = Calendar.getInstance();
            now.setTime(date);
            //now.add(Calendar.DAY_OF_MONTH, -1);
            now.add(Calendar.MONTH, 1);
            now.set(Calendar.DAY_OF_MONTH, 0);

            now.set(Calendar.HOUR_OF_DAY , 00);
            now.set(Calendar.MINUTE , 00);
            now.set(Calendar.SECOND , 00);
            SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String str = sdf2.format(now.getTime());
            System.out.println("TimeUtil getThisMonthEndDate str :" + str );
            Date newDate = sdf2.parse(str);
            return newDate;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }



    /**
     * 昨日开始日期
     * @return
     */
    public static Date getYesterdayStartDate() {
        try{
            Date date = new Date();
            Calendar now = Calendar.getInstance();
            now.setTime(date);
            now.add(Calendar.DAY_OF_MONTH, -1);
            now.set(Calendar.HOUR_OF_DAY , 00);
            now.set(Calendar.MINUTE , 00);
            now.set(Calendar.SECOND , 00);
            SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String str = sdf2.format(now.getTime());
            System.out.println("TimeUtil getYesterdayStartDate str :" + str );
            Date newDate = sdf2.parse(str);
            return newDate;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }


    /**
     * 自定义日期的昨日开始日期
     * @return
     */
    public static Date getCustomYesterdayStartDate(Date date) {
        try{
            Calendar now = Calendar.getInstance();
            now.setTime(date);
            now.add(Calendar.DAY_OF_MONTH, -1);
            now.set(Calendar.HOUR_OF_DAY , 00);
            now.set(Calendar.MINUTE , 00);
            now.set(Calendar.SECOND , 00);
            SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String str = sdf2.format(now.getTime());
            System.out.println("TimeUtil getYesterdayStartDate str :" + str );
            Date newDate = sdf2.parse(str);
            return newDate;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }




    /**
     * 昨日结束日期
     * @return
     */
    public static Date getYesterdayEndDate() {
        try{
            Date date = new Date();
            Calendar now = Calendar.getInstance();
            now.setTime(date);
            now.add(Calendar.DAY_OF_MONTH, -1);
            now.set(Calendar.HOUR_OF_DAY , 23);
            now.set(Calendar.MINUTE , 59);
            now.set(Calendar.SECOND , 59);

            SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String str = sdf2.format(now.getTime());
            System.out.println("TimeUtil getYesterdayEndDate str :" + str );
            Date newDate = sdf2.parse(str);
            return newDate;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }


    /**
     * 自定义日期的昨日结束日期
     * @return
     */
    public static Date getCustomYesterdayEndDate(Date date) {
        try{
            Calendar now = Calendar.getInstance();
            now.setTime(date);
            now.add(Calendar.DAY_OF_MONTH, -1);
            now.set(Calendar.HOUR_OF_DAY , 23);
            now.set(Calendar.MINUTE , 59);
            now.set(Calendar.SECOND , 59);

            SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String str = sdf2.format(now.getTime());
            System.out.println("TimeUtil getYesterdayEndDate str :" + str );
            Date newDate = sdf2.parse(str);
            return newDate;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }




    /**
     * 自定义日期的7日开始日期
     * @return
     */
    public static Date get7dayStartDate( Date date) {
        try{
            //Date date = new Date();
            Calendar now = Calendar.getInstance();
            now.setTime(date);
            now.add(Calendar.DAY_OF_MONTH, -7);
            now.set(Calendar.HOUR_OF_DAY , 00);
            now.set(Calendar.MINUTE , 00);
            now.set(Calendar.SECOND , 00);
            SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String str = sdf2.format(now.getTime());
            System.out.println("TimeUtil getYesterdayStartDate str :" + str );
            Date newDate = sdf2.parse(str);
            return newDate;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 自定义日期的7日结束日期
     * @return
     */
    public static Date get7dayEndDate(Date date) {
        try{
            //Date date = new Date();
            Calendar now = Calendar.getInstance();
            now.setTime(date);
            now.add(Calendar.DAY_OF_MONTH, -7);
            now.set(Calendar.HOUR_OF_DAY , 23);
            now.set(Calendar.MINUTE , 59);
            now.set(Calendar.SECOND , 59);

            SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String str = sdf2.format(now.getTime());
            System.out.println("TimeUtil getYesterdayEndDate str :" + str );
            Date newDate = sdf2.parse(str);
            return newDate;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }


    /**
     * 当前日期  day:负数 减去day天 ; 正数 加上day天
     * @param day
     * @return
     */
    public static Date getCustomDate(Integer day) {
        try{
            Date date = new Date();
            Calendar now = Calendar.getInstance();
            now.setTime(date);
            now.add(Calendar.DAY_OF_MONTH, day);
            SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String str = sdf2.format(now.getTime());
            System.out.println("TimeUtil getCustomDate str :" + str );
            Date newDate = sdf2.parse(str);
            return newDate;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }


    public static Date getStartDate(Date startDate) {
        try{
            if (null == startDate){
                return null;
            }
            Calendar now = Calendar.getInstance();
            now.setTime(startDate);

            now.set(Calendar.HOUR_OF_DAY , 00);
            now.set(Calendar.MINUTE , 00);
            now.set(Calendar.SECOND , 00);
            System.out.println("TimeUtil getStartDate:" + now.getTime().getTime());

            SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String str = sdf2.format(now.getTime());
            System.out.println("TimeUtil getStartDate str :" + str );
            Date newDate = sdf2.parse(str);
            return newDate;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    public static Date getEndDate(Date endDate) {
        try{
            if (null == endDate){
                return null;
            }

            Calendar now = Calendar.getInstance();
            now.setTime(endDate);

            now.set(Calendar.HOUR_OF_DAY , 24);
            now.set(Calendar.MINUTE , 00);
            now.set(Calendar.SECOND , 00);
            System.out.println("TimeUtil getEndDate:" + now.getTime().getTime());

            SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String str = sdf2.format(now.getTime());
            System.out.println("TimeUtil getEndDate str :" + str );
            Date newDate = sdf2.parse(str);
            return newDate;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    public static Date getWanshiDate(Date startDate , Integer hour , Integer minute) {
        try{
            Calendar now = Calendar.getInstance();
            now.setTime(startDate);

            now.set(Calendar.HOUR_OF_DAY , hour);
            now.set(Calendar.MINUTE , minute);
            now.set(Calendar.SECOND , 00);
            System.out.println("TimeUtil getStartDate:" + now.getTime().getTime());

            SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String str = sdf2.format(now.getTime());
            System.out.println("TimeUtil getStartDate str :" + str );
            Date newDate = sdf2.parse(str);
            return newDate;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    public static Date getCurrentYMDDate() {
        try{
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
            String ymd = sdf.format(new Date());
            return sdf.parse(ymd);
        }catch(Exception e){
            e.printStackTrace();
        }
        return null;
    }

    public static Date getCurrentYMDDate2() {
        try{
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String ymd = sdf.format(new Date());
            return sdf.parse(ymd);
        }catch(Exception e){
            e.printStackTrace();
        }
        return null;
    }



    /**
     * 当前日期往后增加几个月
     * @param months
     * @return
     */
    public static long getEndTime(int months) {
        try{
            Calendar now = Calendar.getInstance();
            now.add(Calendar.MONTH , months);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
            String ymd = sdf.format(now.getTime());
            Date date = sdf.parse(ymd);
            return date.getTime() / 1000 ;
        }catch(Exception e){
            e.printStackTrace();
        }
        return 0;
    }


    public static Date formatDateStr2Date(String dateStr) {
        try{
            SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = sdf2.parse(dateStr);
            return date;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }


    /**
     * 获取当前时间后十分钟的时间
     * @return
     */
    public static String afterTenMinsToNowDate() {
        try{
            Calendar now = Calendar.getInstance();
            now.setTime(new Date());
            now.add(Calendar.MINUTE , 10);
            SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String dateStr = sdf2.format(now.getTime());
            System.out.println("TimeUtil afterTenMinsToNowDate dateStr :" + dateStr );
            return dateStr;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }



    /**
     * 获取当前时间后一小时的时间
     * @return
     */
    public static long afterOneHourToNowDate(long time) {
        try{
            Calendar now = Calendar.getInstance();
            now.setTimeInMillis(time * 1000);
            now.add(Calendar.HOUR , 1);
            SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String dateStr = sdf2.format(now.getTime());
            System.out.println("TimeUtil afterOneHourToNowDate dateStr :" + dateStr + " ,nowTime:" + now.getTime().getTime() );
            return now.getTime().getTime() ;
        }catch (Exception e){
            e.printStackTrace();
            return 0;
        }
    }


    /**
     * 获取当前时间后二小时的时间
     * @return
     */
    public static String afterTwoHourToNowDate() {
        try{
            Calendar now = Calendar.getInstance();
            now.setTime(new Date());
            now.add(Calendar.HOUR , 2);
            SimpleDateFormat sdf2 = new SimpleDateFormat("HH:mm");
            String dateStr = sdf2.format(now.getTime());
            System.out.println("TimeUtil afterTwoHourToNowDate dateStr :" + dateStr );
            return dateStr;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    /**
     *  获取 动态设置月份 0:1月  11:12月
     * @param year
     * @param month
     * @return
     */
    public static Date getMonthDate(Integer year , Integer month) {
        try{
            Date date = new Date();
            Calendar now = Calendar.getInstance();
            now.setTime(date);
            now.set(Calendar.YEAR, year);
            now.set(Calendar.MONTH, month);
            SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String str = sdf2.format(now.getTime());
            System.out.println("TimeUtil getMonthDate str :" + str );
            Date newDate = sdf2.parse(str);
            return newDate;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    public static String getCustomHMS(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
        String ymd = sdf.format(date);
        return ymd;
    }

    public static String getCustomYMD(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String ymd = sdf.format(date);
        return ymd;
    }

    public static String getCustomDD(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("dd");
        String day = sdf.format(date);
        return day;
    }

    public static String getCustomHH(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("HH");
        String hour = sdf.format(date);
        return hour;
    }

    public static String getCustomMM(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("mm");
        String min = sdf.format(date);
        return min;
    }


    public static long calDays(long startTime , long endTime) {
        long days = 0;
        try {
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd");

            Calendar startCal = Calendar.getInstance();
            startCal.setTimeInMillis(startTime * 1000);

            Calendar endCal = Calendar.getInstance();
            endCal.setTimeInMillis(endTime * 1000);

            String startTimeStr = df.format(startCal.getTime());
            String endTimeStr = df.format(endCal.getTime());

            Date startDate = df.parse(startTimeStr);
            Date endDate = df.parse(endTimeStr);
            days = ( endDate.getTime() - startDate.getTime() ) / (24 * 60 * 60 * 1000) ;
            System.out.println("TimeUtil calDays startTimeStr :" + startTimeStr  + " ,endTimeStr:" + endTimeStr + " ,days:" + days );

            /*long oneDay = 24 * 60 * 60 ;
            for (int i =0 ; i < days ; i++ ){
                long time1 = startTime + ( i * oneDay ) ;
                long time2 = endTime - ( (days - 1) * oneDay ) + ( i * oneDay )  ;
                System.out.println("TimeUtil calDays startTime :" + time1  + " ,endTime:" + time2  );
            }*/

        } catch (Exception e) {
            e.printStackTrace();
        }
        return days;
    }


    public static long timeStrToLong (String timeStr){
        String strDateFormat = "HH:mm";
        SimpleDateFormat sdf = new SimpleDateFormat(strDateFormat);
        try {
            return sdf.parse(timeStr).getTime();
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return 0;
    }


    /**
     * 把时间戳转换为：时分秒
     *
     * @param millisecond ：毫秒，传入单位为毫秒
     */
    public static String getTimeString(final long millisecond) {
        /*if (millisecond < 1000) {
            return "0" + "秒";
        }
        long second = millisecond / 1000;
        long seconds = second % 60;
        long minutes = second / 60;
        long hours = 0;
        if (minutes >= 60) {
            hours = minutes / 60;
            minutes = minutes % 60;
        }
        String timeString = "";
        String secondString = "";
        String minuteString = "";
        String hourString = "";
        if (seconds < 10) {
            secondString = "0" + seconds + "秒";
        } else {
            secondString = seconds + "秒";
        }
        if (minutes < 10 && hours < 1) {
            minuteString = minutes + "分";
        } else if (minutes < 10){
            minuteString =  "0" + minutes + "分";
        } else {
            minuteString = minutes + "分";
        }
        if (hours < 10) {
            hourString = hours + "时";
        } else {
            hourString = hours + "" + "时";
        }
        if (hours != 0) {
            timeString = hourString + minuteString + secondString;
        } else {
            timeString = minuteString + secondString;
        }
        return timeString;*/

        Integer ss = 1000;
        Integer mi = ss * 60;
        Integer hh = mi * 60;
        Integer dd = hh * 24;

        Long day = millisecond / dd;
        Long hour = (millisecond - day * dd) / hh;
        Long minute = (millisecond - day * dd - hour * hh) / mi;
        Long second = (millisecond - day * dd - hour * hh - minute * mi) / ss;
        Long milliSecond = millisecond - day * dd - hour * hh - minute * mi - second * ss;

        StringBuffer sb = new StringBuffer();
        if(day > 0) {
            sb.append(day+"天");
        }
        if(hour > 0) {
            sb.append(hour+"小时");
        }
        if(minute > 0) {
            sb.append(minute+"分");
        }
        if(second > 0) {
            sb.append(second+"秒");
        }
        if(milliSecond > 0) {
            sb.append(milliSecond+"毫秒");
        }
        return sb.toString();

    }


    /**
     * 计算两个日期是否 超过一个月
     * @param startDate
     * @param endDate
     * @return  true 超过     false 不超过
     */
    public static boolean calaDateRange7(Date startDate , Date endDate ) {
        long startTime = startDate.getTime();
        long endTime = endDate.getTime();
        long diff = endTime - startTime;
        long diffDays = diff  / (24 * 60 * 60 * 1000);
        if (diffDays > 7){
            return true;
        }
        return false;
    }



}
