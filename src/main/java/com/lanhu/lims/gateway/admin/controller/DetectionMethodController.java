package com.lanhu.lims.gateway.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanhu.lims.gateway.admin.annotation.Log;
import com.lanhu.lims.gateway.admin.auth.constants.SecurityConstants;
import com.lanhu.lims.gateway.admin.auth.context.SecurityContextHolder;
import com.lanhu.lims.gateway.admin.core.BaseController;
import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.core.Result;
import com.lanhu.lims.gateway.admin.enums.LogBusinessType;
import com.lanhu.lims.gateway.admin.enums.LogOperatorType;
import com.lanhu.lims.gateway.admin.model.LoginUser;
import com.lanhu.lims.gateway.admin.service.DetectionMethodService;
import com.lanhu.lims.gateway.admin.vo.req.*;
import com.lanhu.lims.gateway.admin.vo.resp.DetectionMethodDetailVO;
import com.lanhu.lims.gateway.admin.vo.resp.DetectionMethodListVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/********************************
 * @title DetectionMethodController
 * @package com.lanhu.lims.gateway.admin.controller
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/13 22:34
 * @version 0.0.1
 *********************************/
@RestController
@Api(tags = "检测方法相关接口", value = "检测方法相关接口")
public class DetectionMethodController extends BaseController {
    @Resource
    private DetectionMethodService detectionMethodService;

    /**
     * 查询检测方法列表
     */
    @ApiOperation(value = "查询检测方法列表")
    @PostMapping("/detectionMethod/v1/list")
    public PcsResult<List<DetectionMethodListVO>> list(@Validated @RequestBody DetectionMethodListForm form) {
        return Result.ok(detectionMethodService.list(form));
    }

    /**
     * 查询检测方法分页列表
     */
    @ApiOperation(value = "查询检测方法分页列表")
    @PostMapping("/detectionMethod/v1/listPage")
    public PcsResult<IPage<DetectionMethodListVO>> listPage(@Validated @RequestBody DetectionMethodListPageForm form) {
        return Result.ok(detectionMethodService.listPage(form));
    }

    /**
     * 查询检测方法详情
     */
    @ApiOperation(value = "查询检测方法详情")
    @PostMapping("/detectionMethod/v1/detail")
    public PcsResult<DetectionMethodDetailVO> detail(@Validated @RequestBody DetectionMethodSingleForm form) {
        return Result.ok(detectionMethodService.detail(form));
    }

    /**
     * 新增检测方法
     */
    @ApiOperation(value = "新增检测方法")
    @PostMapping("/detectionMethod/v1/add")
    @Log(title = "新增检测方法", businessType = LogBusinessType.INSERT, operatorType = LogOperatorType.PC)
    public PcsResult<Void> add(@Validated @RequestBody DetectionMethodAddForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        detectionMethodService.add(form, loginUser.getSysUser());
        return Result.ok();
    }

    /**
     * 修改检测方法
     */
    @ApiOperation(value = "修改检测方法")
    @PostMapping("/detectionMethod/v1/edit")
    @Log(title = "修改检测方法", businessType = LogBusinessType.UPDATE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> edit(@Validated @RequestBody DetectionMethodEditForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        detectionMethodService.edit(form, loginUser.getSysUser());
        return Result.ok();
    }

    /**
     * 删除检测方法
     */
    @ApiOperation(value = "删除检测方法")
    @PostMapping("/detectionMethod/v1/del")
    @Log(title = "删除检测方法", businessType = LogBusinessType.LOGICDELETE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> del(@Validated @RequestBody DetectionMethodDelForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        detectionMethodService.del(form, loginUser.getSysUser());
        return Result.ok();
    }

    /**
     * 启用检测方法
     */
    @ApiOperation(value = "启用检测方法")
    @PostMapping("/detectionMethod/v1/enable")
    @Log(title = "启用检测方法", businessType = LogBusinessType.ENABLE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> enable(@Validated @RequestBody DetectionMethodSingleForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        detectionMethodService.enable(form, loginUser.getSysUser());
        return Result.ok();
    }

    /**
     * 禁用检测方法
     */
    @ApiOperation(value = "禁用检测方法")
    @PostMapping("/detectionMethod/v1/disable")
    @Log(title = "禁用检测方法", businessType = LogBusinessType.DISABLE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> disable(@Validated @RequestBody DetectionMethodSingleForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        detectionMethodService.disable(form, loginUser.getSysUser());
        return Result.ok();
    }

    /**
     * 新增检测方法版本
     */
    @ApiOperation(value = "新增检测方法版本")
    @PostMapping("/detectionMethod/version/v1/add")
    @Log(title = "新增检测方法版本", businessType = LogBusinessType.INSERT, operatorType = LogOperatorType.PC)
    public PcsResult<Void> fileAddVersion(@Validated @RequestBody DetectionMethodVersionAddForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        detectionMethodService.addVersion(form, loginUser.getSysUser());
        return Result.ok();
    }
}
