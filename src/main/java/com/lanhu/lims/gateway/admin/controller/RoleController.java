package com.lanhu.lims.gateway.admin.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanhu.lims.gateway.admin.annotation.Log;
import com.lanhu.lims.gateway.admin.auth.constants.SecurityConstants;
import com.lanhu.lims.gateway.admin.auth.context.SecurityContextHolder;
import com.lanhu.lims.gateway.admin.core.BaseController;
import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.enums.LogBusinessType;
import com.lanhu.lims.gateway.admin.enums.LogOperatorType;
import com.lanhu.lims.gateway.admin.model.LoginUser;
import com.lanhu.lims.gateway.admin.model.Role;
import com.lanhu.lims.gateway.admin.service.RoleService;
import com.lanhu.lims.gateway.admin.vo.req.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/1/11 5:21 下午
 */

@Slf4j
@Api(value = "lims-角色管理", tags = "lims-角色管理")
@RestController
public class RoleController extends BaseController {

    @Autowired
    private RoleService plaPcRoleService;

    @ApiOperation(value = "角色添加", notes = "角色添加")
    @PostMapping("/role/v1/add")
    @Log(title = "角色添加", businessType = LogBusinessType.INSERT, operatorType = LogOperatorType.PC)
    public PcsResult<Long> add(@Validated @RequestBody RoleAddForm request) {

        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER,LoginUser.class);



        return plaPcRoleService.add(loginUser.getSysUser(),request);
    }


    @ApiOperation(value = "角色详情", notes = "角色详情")
    @PostMapping("/role/v1/detail")
    public PcsResult<Role> detail(@Validated @RequestBody RoleDetailForm request) {

        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER,LoginUser.class);



        return plaPcRoleService.detail(loginUser.getSysUser(),request);
    }


    @ApiOperation(value = "角色分页查询", notes = "角色分页查询")
    @PostMapping("/role/v1/listPage")
    public PcsResult<IPage<Role>> listPage(@Validated @RequestBody RoleListPageForm request) {

        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER,LoginUser.class);



        return plaPcRoleService.listPage(loginUser.getSysUser(),request);
    }



    @ApiOperation(value = "我的角色列表查询", notes = "我的角色列表查询")
    @PostMapping("/role/v1/list")
    public PcsResult<List<Role>> list(@Validated @RequestBody RoleListForm request) {

        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER,LoginUser.class);



        return plaPcRoleService.list(loginUser.getSysUser(),request);


    }



    @ApiOperation(value = "角色修改", notes = "角色修改")
    @PostMapping("/role/v1/edit")
    @Log(title = "角色修改", businessType = LogBusinessType.UPDATE, operatorType = LogOperatorType.PC)
    public PcsResult edit(@Validated @RequestBody RoleEditForm request) {

        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER,LoginUser.class);
        
        return plaPcRoleService.edit(loginUser.getSysUser(),request);
    }


    @ApiOperation(value = "角色启用/删除", notes = "角色启用/删除")
    @PostMapping("/role/v1/enable")
    @Log(title = "角色启用/删除", businessType = LogBusinessType.LOGICDELETE, operatorType = LogOperatorType.PC)
    public PcsResult enable(@Validated @RequestBody RoleEnableForm request) {

        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER,LoginUser.class);



        return plaPcRoleService.enable(loginUser.getSysUser(),request);
    }

    @ApiOperation(value = "批量删除", notes = "批量删除")
    @PostMapping("/role/v1/batchDel")
    @Log(title = "角色批量删除", businessType = LogBusinessType.LOGICDELETE, operatorType = LogOperatorType.PC)
    public PcsResult batchDel(@Validated @RequestBody RoleBatchDelForm request) {

        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER,LoginUser.class);



        return plaPcRoleService.batchDel(loginUser.getSysUser(),request);
    }


}
