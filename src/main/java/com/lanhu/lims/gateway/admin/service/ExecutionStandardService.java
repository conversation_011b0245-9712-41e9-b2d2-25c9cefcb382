package com.lanhu.lims.gateway.admin.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanhu.lims.gateway.admin.core.BusinessException;
import com.lanhu.lims.gateway.admin.core.PcsResultCode;
import com.lanhu.lims.gateway.admin.enums.AuditStatusEnum;
import com.lanhu.lims.gateway.admin.enums.DictDataTypeEnum;
import com.lanhu.lims.gateway.admin.enums.EnableEnum;
import com.lanhu.lims.gateway.admin.enums.IsEffectEnum;
import com.lanhu.lims.gateway.admin.mapper.DictDataMapper;
import com.lanhu.lims.gateway.admin.mapper.ExecutionStandardMapper;
import com.lanhu.lims.gateway.admin.mapper.FileRecordMapper;
import com.lanhu.lims.gateway.admin.mapper.StaticDataAttachmentMapper;
import com.lanhu.lims.gateway.admin.model.*;
import com.lanhu.lims.gateway.admin.utils.ConvertUtil;
import com.lanhu.lims.gateway.admin.vo.req.*;
import com.lanhu.lims.gateway.admin.vo.resp.ExecutionStandardDetailVO;
import com.lanhu.lims.gateway.admin.vo.resp.ExecutionStandardListVO;
import com.lanhu.lims.gateway.admin.vo.resp.StaticDataAttachmentVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/********************************
 * @title ExecutionStandardService
 * @package com.lanhu.lims.gateway.admin.service
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/14 16:18
 * @version 0.0.1
 *********************************/
@Service
public class ExecutionStandardService {
    @Resource
    private ExecutionStandardMapper executionStandardMapper;

    @Resource
    private StaticDataAttachmentMapper staticDataAttachmentMapper;

    @Resource
    private FileRecordMapper fileRecordMapper;

    @Resource
    private DictDataMapper dictDataMapper;

    /**
     * 查询执行标准列表
     *
     * @param form 查询入参
     * @return 列表
     */
    @DS("slave_1")
    public List<ExecutionStandardListVO> list(ExecutionStandardListForm form) {
        LambdaQueryWrapper<ExecutionStandard> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ExecutionStandard::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.eq(ExecutionStandard::getStatus, EnableEnum.ENABLE.getCode());
        wrapper.like(StrUtil.isNotBlank(form.getStandardCode()), ExecutionStandard::getStandardCode, form.getStandardCode());
        wrapper.like(StrUtil.isNotBlank(form.getStandardName()), ExecutionStandard::getStandardName, form.getStandardName());
        wrapper.eq(StrUtil.isNotBlank(form.getStandardCategory()), ExecutionStandard::getStandardCategory, form.getStandardCategory());
        wrapper.ge(form.getEffectiveStartDate() != null, ExecutionStandard::getEffectiveDate, form.getEffectiveStartDate());
        wrapper.le(form.getEffectiveEndDate() != null, ExecutionStandard::getEffectiveDate, form.getEffectiveEndDate());
        wrapper.orderByDesc(ExecutionStandard::getCreateTime);
        List<ExecutionStandard> list = executionStandardMapper.selectList(wrapper);
        return ConvertUtil.convertList(list, entity -> {
            ExecutionStandardListVO vo = new ExecutionStandardListVO();
            BeanUtil.copyProperties(entity, vo);
            // 设置标准类别名称
            if (StrUtil.isNotBlank(entity.getStandardCategory())) {
                vo.setStandardCategoryName(getStandardCategoryName(entity.getStandardCategory()));
            }
            return vo;
        });
    }

    /**
     * 分页查询执行标准
     *
     * @param form 查询入参
     * @return 分页列表
     */
    @DS("slave_1")
    public IPage<ExecutionStandardListVO> listPage(ExecutionStandardListPageForm form) {
        LambdaQueryWrapper<ExecutionStandard> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ExecutionStandard::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.eq(form.getStatus() != null, ExecutionStandard::getStatus, form.getStatus());
        wrapper.like(StrUtil.isNotBlank(form.getStandardCode()), ExecutionStandard::getStandardCode, form.getStandardCode());
        wrapper.like(StrUtil.isNotBlank(form.getStandardName()), ExecutionStandard::getStandardName, form.getStandardName());
        wrapper.eq(StrUtil.isNotBlank(form.getStandardCategory()), ExecutionStandard::getStandardCategory, form.getStandardCategory());
        wrapper.ge(form.getEffectiveStartDate() != null, ExecutionStandard::getEffectiveDate, form.getEffectiveStartDate());
        wrapper.le(form.getEffectiveEndDate() != null, ExecutionStandard::getEffectiveDate, form.getEffectiveEndDate());
        wrapper.orderByDesc(ExecutionStandard::getCreateTime);
        Page<ExecutionStandard> page = new Page<>(form.getPageIndex(), form.getPageSize());
        IPage<ExecutionStandard> result = executionStandardMapper.selectPage(page, wrapper);
        // 使用ConvertUtil进行自定义转换
        return ConvertUtil.convertPage(result, entity -> {
            ExecutionStandardListVO vo = new ExecutionStandardListVO();
            BeanUtil.copyProperties(entity, vo);
            // 设置标准类别名称
            if (StrUtil.isNotBlank(entity.getStandardCategory())) {
                vo.setStandardCategoryName(getStandardCategoryName(entity.getStandardCategory()));
            }
            return vo;
        });
    }

    /**
     * 查询执行标准详情
     *
     * @param form 查询入参
     * @return 详情
     */
    @DS("slave_1")
    public ExecutionStandardDetailVO detail(ExecutionStandardSingleForm form) {
        ExecutionStandard executionStandard = executionStandardMapper.selectById(form.getId());
        if (executionStandard == null || executionStandard.getIsEffect() != IsEffectEnum.NORMAL.getCode()) {
            throw new BusinessException(PcsResultCode.EXECUTION_STANDARD_NOT_EXIST);
        }
        // 查询标准类别名称
        ExecutionStandardDetailVO vo = ConvertUtil.convert(executionStandard, ExecutionStandardDetailVO.class);
        // 查询附件
        List<StaticDataAttachmentVO> attachmentVos = ConvertUtil.convertList(staticDataAttachmentMapper.selectList(Wrappers.<StaticDataAttachment>lambdaQuery().eq(StaticDataAttachment::getDataId, executionStandard.getId())), StaticDataAttachmentVO.class);
        if (CollUtil.isNotEmpty(attachmentVos)) {
            // 处理附件其他信息
            int size = CollUtil.size(attachmentVos);
            Date now = DateUtil.date();
            for (int i = 0; i < size; i++) {
                StaticDataAttachmentVO attachmentVo = attachmentVos.get(i);
                attachmentVo.setExpirationDate(i == 0 ? null : attachmentVos.get(i - 1).getEffectiveDate());
                attachmentVo.setIsEffective(DateUtil.compare(attachmentVo.getEffectiveDate(), now) <= 0 && DateUtil.compare(now, attachmentVo.getExpirationDate()) < 0);
            }
            vo.setAttachments(attachmentVos);
        }
        return vo;
    }

    /**
     * 新增执行标准
     *
     * @param form      新增入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void add(ExecutionStandardAddForm form, AdminUser adminUser) {
        // 查询文件记录
        FileRecord fileRecord = fileRecordMapper.selectById(form.getFileRecordId());
        if (fileRecord == null) {
            throw new BusinessException(PcsResultCode.FILE_RECORD_NOT_EXIST);
        }
        // 新增执行标准
        ExecutionStandard executionStandard = new ExecutionStandard();
        executionStandard.setStandardCode(form.getStandardCode());
        executionStandard.setStandardName(form.getStandardName());
        executionStandard.setStandardCategory(form.getStandardCategory());
        executionStandard.setVersion(form.getVersion());
        executionStandard.setAttachmentUrl(fileRecord.getAttachmentUrl());
        executionStandard.setEffectiveDate(form.getEffectiveDate());
        executionStandard.setRemark(StrUtil.blankToDefault(form.getRemark(), StrUtil.EMPTY));
        executionStandard.setStatus(EnableEnum.ENABLE.getCode());
        executionStandard.setIsEffect(IsEffectEnum.NORMAL.getCode());
        executionStandard.setCreateBy(adminUser.getId());
        executionStandard.setCreateName(adminUser.getRealName());
        executionStandard.setCreateTime(DateUtil.date());
        executionStandard.setUpdateBy(adminUser.getId());
        executionStandard.setUpdateName(adminUser.getRealName());
        executionStandard.setUpdateTime(executionStandard.getCreateTime());
        executionStandard.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        executionStandard.setAuditName(StrUtil.EMPTY);
        executionStandard.setAuditBy(null);
        executionStandard.setAuditTime(null);
        executionStandard.setAuditRemark(StrUtil.EMPTY);
        executionStandardMapper.insert(executionStandard);
        // 保存附件
        StaticDataAttachment attachment = saveAttachment(executionStandard.getId(), executionStandard.getStandardCode(), executionStandard.getEffectiveDate(), executionStandard.getVersion(), fileRecord, adminUser);
        // todo 发起流程
    }

    /**
     * 修改执行标准
     *
     * @param form      修改入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void edit(ExecutionStandardEditForm form, AdminUser adminUser) {
        // 校验数据
        ExecutionStandard executionStandard = checkCanEditOrDelete(form.getId());
        // 修改执行标准
        executionStandard.setStandardName(form.getStandardName());
        executionStandard.setStandardCategory(form.getStandardCategory());
        executionStandard.setRemark(StrUtil.blankToDefault(form.getRemark(), StrUtil.EMPTY));
        executionStandard.setUpdateBy(adminUser.getId());
        executionStandard.setUpdateName(adminUser.getRealName());
        executionStandard.setUpdateTime(DateUtil.date());
        executionStandard.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        executionStandardMapper.updateById(executionStandard);
        // todo 发起流程
    }

    /**
     * 删除执行标准
     *
     * @param form      删除入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void del(ExecutionStandardDelForm form, AdminUser adminUser) {
        // 校验数据
        ExecutionStandard executionStandard = checkCanEditOrDelete(form.getId());
        // todo 校验是否有其他业务引用
        // 删除执行标准
        executionStandard.setIsEffect(IsEffectEnum.DELETE.getCode());
        executionStandard.setUpdateBy(adminUser.getId());
        executionStandard.setUpdateName(adminUser.getRealName());
        executionStandard.setUpdateTime(DateUtil.date());
        executionStandard.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        executionStandardMapper.updateById(executionStandard);
        // todo 发起流程
    }

    /**
     * 启用执行标准
     *
     * @param form      启用入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void enable(ExecutionStandardSingleForm form, AdminUser adminUser) {
        // 更新状态
        updateStatus(form.getId(), EnableEnum.ENABLE, adminUser);
    }

    /**
     * 禁用执行标准
     *
     * @param form      禁用入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void disable(ExecutionStandardSingleForm form, AdminUser adminUser) {
        // 更新状态
        updateStatus(form.getId(), EnableEnum.DISABLE, adminUser);
    }

    /**
     * 新增执行标准版本
     *
     * @param form      新增版本入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void addVersion(ExecutionStandardVersionAddForm form, AdminUser adminUser) {
        // 校验数据
        ExecutionStandard executionStandard = executionStandardMapper.selectById(form.getId());
        if (executionStandard == null || executionStandard.getIsEffect() != IsEffectEnum.NORMAL.getCode()) {
            throw new BusinessException(PcsResultCode.EXECUTION_STANDARD_NOT_EXIST);
        }
        // 查询文件记录
        FileRecord fileRecord = fileRecordMapper.selectById(form.getFileRecordId());
        if (fileRecord == null) {
            throw new BusinessException(PcsResultCode.FILE_RECORD_NOT_EXIST);
        }
        // 新增执行标准版本
        ExecutionStandard newVersion = new ExecutionStandard();
        newVersion.setStandardCode(form.getStandardCode());
        newVersion.setStandardName(executionStandard.getStandardName());
        newVersion.setStandardCategory(executionStandard.getStandardCategory());
        newVersion.setVersion(form.getVersion());
        newVersion.setAttachmentUrl(fileRecord.getAttachmentUrl());
        newVersion.setEffectiveDate(form.getEffectiveDate());
        newVersion.setRemark(executionStandard.getRemark());
        newVersion.setStatus(EnableEnum.ENABLE.getCode());
        newVersion.setIsEffect(IsEffectEnum.NORMAL.getCode());
        newVersion.setCreateBy(adminUser.getId());
        newVersion.setCreateName(adminUser.getRealName());
        newVersion.setCreateTime(DateUtil.date());
        newVersion.setUpdateBy(adminUser.getId());
        newVersion.setUpdateName(adminUser.getRealName());
        newVersion.setUpdateTime(newVersion.getCreateTime());
        newVersion.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        newVersion.setAuditName(StrUtil.EMPTY);
        newVersion.setAuditBy(null);
        newVersion.setAuditTime(null);
        newVersion.setAuditRemark(StrUtil.EMPTY);
        executionStandardMapper.insert(newVersion);
        // 保存附件
        StaticDataAttachment attachment = saveAttachment(newVersion.getId(), newVersion.getStandardCode(), newVersion.getEffectiveDate(), newVersion.getVersion(), fileRecord, adminUser);
        // todo 发起流程
    }

    // =================================================================== 工作流回调 =================================================================== //

    // =================================================================== 私有方法区 =================================================================== //

    /**
     * 校验执行标准是否允许修改或删除
     *
     * @param id 执行标准ID
     * @return 执行标准
     */
    private ExecutionStandard checkCanEditOrDelete(Long id) {
        ExecutionStandard executionStandard = executionStandardMapper.selectById(id);
        if (executionStandard == null || ObjectUtil.notEqual(IsEffectEnum.NORMAL.getCode(), executionStandard.getIsEffect())) {
            throw new BusinessException(PcsResultCode.EXECUTION_STANDARD_NOT_EXIST);
        }
        if (ObjectUtil.notEqual(AuditStatusEnum.PASS.getCode(), executionStandard.getAuditStatus())) {
            throw new BusinessException(PcsResultCode.DATA_WAS_AUDITING_CANT_EDIT_OR_DELETE);
        }
        return executionStandard;
    }

    /**
     * 获取标准类别名称
     *
     * @param standardCategory 标准类别值
     * @return 标准类别名称
     */
    private String getStandardCategoryName(String standardCategory) {
        if (StrUtil.isBlank(standardCategory)) {
            return StrUtil.EMPTY;
        }
        // 获取标准类别名称
        LambdaQueryWrapper<DictData> wrapper = Wrappers.lambdaQuery();
        wrapper.select(DictData::getDictLabel)
                .eq(DictData::getDictValue, standardCategory)
                .eq(DictData::getIsEffect, IsEffectEnum.NORMAL.getCode())
                .eq(DictData::getStatus, EnableEnum.ENABLE.getCode())
                .exists("SELECT 1 FROM dict_data parent WHERE parent.dict_value = '" +
                        DictDataTypeEnum.EXECUTION_STANDARD_CATEGORY.getCode() +
                        "' AND parent.is_effect = " + IsEffectEnum.NORMAL.getCode() +
                        " AND parent.status = " + EnableEnum.ENABLE.getCode() +
                        " AND dict_data.parent_id = parent.id");
        DictData dictData = dictDataMapper.selectOne(wrapper);
        return dictData != null ? dictData.getDictLabel() : StrUtil.EMPTY;
    }

    /**
     * 保存附件
     *
     * @param executionStandardId   执行标准id
     * @param executionStandardCode 执行标准编号
     * @param effectiveDate         生效日期
     * @param version               版本
     * @param fileRecord            文件记录
     * @param adminUser             当前用户
     */
    private StaticDataAttachment saveAttachment(Long executionStandardId, String executionStandardCode, Date effectiveDate, String version, FileRecord fileRecord, AdminUser adminUser) {
        // 新增附件
        StaticDataAttachment staticDataAttachment = new StaticDataAttachment();
        staticDataAttachment.setId(IdUtil.getSnowflakeNextId());
        staticDataAttachment.setDataId(executionStandardId);
        staticDataAttachment.setAttachmentType(StrUtil.EMPTY);
        staticDataAttachment.setAttachmentCode(executionStandardCode);
        staticDataAttachment.setEffectiveDate(effectiveDate);
        staticDataAttachment.setVersion(version);
        staticDataAttachment.setFileId(fileRecord.getId());
        staticDataAttachment.setAttachmentUrl(fileRecord.getAttachmentUrl());
        staticDataAttachment.setAttachmentName(fileRecord.getAttachmentName());
        staticDataAttachment.setAttachmentSuffix(fileRecord.getAttachmentSuffix());
        staticDataAttachment.setAttachmentSize(fileRecord.getAttachmentSize());
        staticDataAttachment.setCreateBy(adminUser.getId());
        staticDataAttachment.setCreateName(adminUser.getRealName());
        staticDataAttachment.setCreateTime(DateUtil.date());
        staticDataAttachment.setUpdateBy(adminUser.getId());
        staticDataAttachment.setUpdateName(adminUser.getRealName());
        staticDataAttachment.setUpdateTime(staticDataAttachment.getCreateTime());
        staticDataAttachment.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        staticDataAttachment.setAuditName(StrUtil.EMPTY);
        staticDataAttachment.setAuditBy(null);
        staticDataAttachment.setAuditTime(null);
        staticDataAttachment.setAuditRemark(StrUtil.EMPTY);
        staticDataAttachmentMapper.insert(staticDataAttachment);
        return staticDataAttachment;
    }

    /**
     * 更新执行标准状态
     *
     * @param id         执行标准id
     * @param enableEnum 启用状态
     * @param adminUser  当前用户
     */
    private void updateStatus(Long id, EnableEnum enableEnum, AdminUser adminUser) {
        // 校验数据
        ExecutionStandard executionStandard = executionStandardMapper.selectById(id);
        if (executionStandard == null || executionStandard.getIsEffect() != IsEffectEnum.NORMAL.getCode()) {
            throw new BusinessException(PcsResultCode.EXECUTION_STANDARD_NOT_EXIST);
        }
        executionStandard.setStatus(enableEnum.getCode());
        executionStandard.setUpdateBy(adminUser.getId());
        executionStandard.setUpdateName(adminUser.getRealName());
        executionStandard.setUpdateTime(DateUtil.date());
        executionStandard.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        executionStandardMapper.updateById(executionStandard);
    }
}
