package com.lanhu.lims.gateway.admin.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/********************************
 * @title EquipmentMaintenanceRecord
 * @package com.lanhu.lims.gateway.admin.model
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/14 22:23
 * @version 0.0.1
 *********************************/

@ApiModel(description = "设备维护记录表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "t_equipment_maintenance_record")
public class EquipmentMaintenanceRecord {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 设备ID
     */
    @TableField(value = "equipment_id")
    @ApiModelProperty(value = "设备ID")
    private Long equipmentId;

    /**
     * 维护人ID
     */
    @TableField(value = "maintainer_id")
    @ApiModelProperty(value = "维护人ID")
    private Long maintainerId;

    /**
     * 维护人姓名
     */
    @TableField(value = "maintainer_name")
    @ApiModelProperty(value = "维护人姓名")
    private String maintainerName;

    /**
     * 维护内容
     */
    @TableField(value = "maintenance_content")
    @ApiModelProperty(value = "维护内容")
    private String maintenanceContent;

    /**
     * 维护时间
     */
    @TableField(value = "maintenance_time")
    @ApiModelProperty(value = "维护时间")
    private Date maintenanceTime;

    /**
     * 下次维护时间
     */
    @TableField(value = "next_maintenance_time")
    @ApiModelProperty(value = "下次维护时间")
    private Date nextMaintenanceTime;

    /**
     * 是否有效，0：正常，1：删除
     */
    @TableField(value = "is_effect")
    @ApiModelProperty(value = "是否有效，0：正常，1：删除")
    private Integer isEffect;

    /**
     * 创建人ID
     */
    @TableField(value = "create_by")
    @ApiModelProperty(value = "创建人ID")
    private Long createBy;

    /**
     * 创建人姓名
     */
    @TableField(value = "create_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createName;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 修改人ID
     */
    @TableField(value = "update_by")
    @ApiModelProperty(value = "修改人ID")
    private Long updateBy;

    /**
     * 修改人姓名
     */
    @TableField(value = "update_name")
    @ApiModelProperty(value = "修改人姓名")
    private String updateName;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
}