package com.lanhu.lims.gateway.admin.utils.excel;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/********************************
 * @title ExcelImageHandler
 * @package com.lanhu.lims.gateway.admin.utils.excel
 * @description Excel图片处理工具类，支持图片下载、缓存、压缩等功能
 *
 * <AUTHOR>
 * @date 2025/1/27 10:40
 * @version 0.0.1
 *********************************/
@Slf4j
public class ExcelImageHandler {

    private final ThreadPoolTaskExecutor imageDownloadExecutor;
    private final ConcurrentMap<String, byte[]> imageCache = new ConcurrentHashMap<>();

    public ExcelImageHandler(ThreadPoolTaskExecutor imageDownloadExecutor) {
        this.imageDownloadExecutor = imageDownloadExecutor;
    }

    /**
     * 异步下载图片并缓存
     *
     * @param imageUrls 图片URL列表
     * @return CompletableFuture
     */
    public CompletableFuture<Void> preloadImages(List<String> imageUrls) {
        if (ObjectUtil.isEmpty(imageUrls)) {
            return CompletableFuture.completedFuture(null);
        }
        return CompletableFuture.allOf(imageUrls.stream()
                .filter(StrUtil::isNotBlank)
                .map(url -> CompletableFuture.runAsync(() -> downloadAndCacheImage(url), imageDownloadExecutor)).toArray(CompletableFuture[]::new));
    }

    /**
     * 下载并缓存图片
     *
     * @param imageUrl 图片URL
     */
    private void downloadAndCacheImage(String imageUrl) {
        try {
            if (imageCache.containsKey(imageUrl)) {
                return;
            }
            byte[] imageBytes = HttpUtil.downloadBytes(imageUrl);
            if (imageBytes != null && imageBytes.length > 0) {
                imageCache.put(imageUrl, imageBytes);
                log.debug("图片下载成功: {}", imageUrl);
            }
        } catch (Exception e) {
            log.error("图片下载失败: {}, 错误: {}", imageUrl, e.getMessage());
        }
    }

    /**
     * 获取缓存的图片数据
     *
     * @param imageUrl 图片URL
     * @return 图片字节数组
     */
    public byte[] getCachedImage(String imageUrl) {
        return imageCache.get(imageUrl);
    }

    /**
     * 创建图片字节数组（用于Excel导出）
     *
     * @param imageUrl 图片URL
     * @return 图片字节数组
     */
    public byte[] createImageBytes(String imageUrl) {
        if (StrUtil.isBlank(imageUrl)) {
            return null;
        }
        // 先从缓存获取
        byte[] cachedImage = getCachedImage(imageUrl);
        if (cachedImage != null) {
            return cachedImage;
        }
        // 缓存中没有则下载
        downloadAndCacheImage(imageUrl);
        return getCachedImage(imageUrl);
    }

    /**
     * 压缩图片
     *
     * @param imageBytes 原始图片字节数组
     * @param maxWidth   最大宽度
     * @param maxHeight  最大高度
     * @param quality    压缩质量(0.0-1.0)
     * @return 压缩后的图片字节数组
     */
    public static byte[] compressImage(byte[] imageBytes, int maxWidth, int maxHeight, float quality) {
        if (imageBytes == null || imageBytes.length == 0) {
            return imageBytes;
        }
        try (ByteArrayInputStream bis = new ByteArrayInputStream(imageBytes);
             ByteArrayOutputStream bos = new ByteArrayOutputStream()) {

            BufferedImage originalImage = ImageIO.read(bis);
            if (originalImage == null) {
                return imageBytes;
            }

            // 计算缩放比例
            int originalWidth = originalImage.getWidth();
            int originalHeight = originalImage.getHeight();
            double scaleX = (double) maxWidth / originalWidth;
            double scaleY = (double) maxHeight / originalHeight;
            double scale = Math.min(scaleX, scaleY);

            if (scale >= 1.0) {
                return imageBytes; // 不需要压缩
            }

            int newWidth = (int) (originalWidth * scale);
            int newHeight = (int) (originalHeight * scale);

            // 创建缩放后的图片
            BufferedImage scaledImage = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
            scaledImage.getGraphics().drawImage(originalImage.getScaledInstance(newWidth, newHeight, java.awt.Image.SCALE_SMOOTH), 0, 0, null);

            // 输出压缩后的图片
            ImageIO.write(scaledImage, "jpg", bos);
            return bos.toByteArray();

        } catch (IOException e) {
            log.error("图片压缩失败: {}", e.getMessage());
            return imageBytes;
        }
    }

    /**
     * 从URL获取图片格式
     *
     * @param imageUrl 图片URL
     * @return 图片格式
     */
    public static String getImageFormat(String imageUrl) {
        if (StrUtil.isBlank(imageUrl)) {
            return "jpg";
        }
        String lowerUrl = imageUrl.toLowerCase();
        if (lowerUrl.contains(".png")) {
            return "png";
        } else if (lowerUrl.contains(".gif")) {
            return "gif";
        } else if (lowerUrl.contains(".bmp")) {
            return "bmp";
        } else {
            return "jpg";
        }
    }

    /**
     * 验证图片格式
     *
     * @param imageBytes 图片字节数组
     * @return 是否为有效图片
     */
    public static boolean isValidImage(byte[] imageBytes) {
        if (imageBytes == null || imageBytes.length == 0) {
            return false;
        }
        try (ByteArrayInputStream bis = new ByteArrayInputStream(imageBytes)) {
            BufferedImage image = ImageIO.read(bis);
            return image != null;
        } catch (IOException e) {
            return false;
        }
    }

    /**
     * 获取图片尺寸信息
     *
     * @param imageBytes 图片字节数组
     * @return 图片尺寸信息
     */
    public static ImageDimension getImageDimension(byte[] imageBytes) {
        if (imageBytes == null || imageBytes.length == 0) {
            return null;
        }
        try (ByteArrayInputStream bis = new ByteArrayInputStream(imageBytes)) {
            BufferedImage image = ImageIO.read(bis);
            if (image != null) {
                return new ImageDimension(image.getWidth(), image.getHeight());
            }
        } catch (IOException e) {
            log.error("获取图片尺寸失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 清空图片缓存
     */
    public void clearImageCache() {
        imageCache.clear();
        log.info("图片缓存已清空");
    }

    /**
     * 获取缓存大小
     *
     * @return 缓存中图片数量
     */
    public int getCacheSize() {
        return imageCache.size();
    }

    /**
     * 图片尺寸信息类
     */
    @Getter
    public static class ImageDimension {
        private final int width;
        private final int height;

        public ImageDimension(int width, int height) {
            this.width = width;
            this.height = height;
        }

        @Override
        public String toString() {
            return String.format("ImageDimension{width=%d, height=%d}", width, height);
        }
    }
}
