package com.lanhu.lims.gateway.admin.service;

import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2025/6/12 11:06
 */
@Service
@Slf4j
public class DiffService {

    /**
     * @description: 属性名称翻译注解解析
     * @param: [clazz]
     * @return: java.util.Map<java.lang.String,java.lang.String>
     * @author: liuyi
     * @date: 22:08 2025/6/11
     */
    private  Map<String, String> propertyNameTranslations(Class<?> clazz) {
        Map<String, String> translations = new HashMap<>();
        for (Field field : clazz.getDeclaredFields()) {
            if (field.isAnnotationPresent(ApiModelProperty.class)) {
                ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
                translations.put(field.getName(), annotation.value());
            }
        }
        return translations;
    }
}
