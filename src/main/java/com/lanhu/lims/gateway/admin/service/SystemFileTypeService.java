package com.lanhu.lims.gateway.admin.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lanhu.lims.gateway.admin.core.BusinessException;
import com.lanhu.lims.gateway.admin.core.PcsResultCode;
import com.lanhu.lims.gateway.admin.enums.IsEffectEnum;
import com.lanhu.lims.gateway.admin.mapper.SystemFileMapper;
import com.lanhu.lims.gateway.admin.mapper.SystemFileTypeMapper;
import com.lanhu.lims.gateway.admin.model.AdminUser;
import com.lanhu.lims.gateway.admin.model.SystemFile;
import com.lanhu.lims.gateway.admin.model.SystemFileType;
import com.lanhu.lims.gateway.admin.utils.ConvertUtil;
import com.lanhu.lims.gateway.admin.vo.req.SystemFileTypeAddForm;
import com.lanhu.lims.gateway.admin.vo.req.SystemFileTypeEditForm;
import com.lanhu.lims.gateway.admin.vo.req.SystemFileTypeSingleForm;
import com.lanhu.lims.gateway.admin.vo.resp.SystemFileTypeDetailVO;
import com.lanhu.lims.gateway.admin.vo.resp.SystemFileTypeListVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/********************************
 * @title SystemFileTypeService
 * @package com.lanhu.lims.gateway.admin.service
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/8 10:43
 * @version 0.0.1
 *********************************/
@Service
public class SystemFileTypeService {
    @Resource
    private SystemFileTypeMapper systemFileTypeMapper;

    @Resource
    private SystemFileMapper systemFileMapper;

    /**
     * 获取文件类型树形列表
     */
    @DS("slave_1")
    public List<Tree<Long>> treeList() {
        LambdaQueryWrapper<SystemFileType> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SystemFileType::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.orderByDesc(CollUtil.newArrayList(SystemFileType::getOrderNum, SystemFileType::getCreateTime));
        List<SystemFileType> list = systemFileTypeMapper.selectList(wrapper);
        // 转换为VO列表并构建树形结构
        List<SystemFileTypeListVO> vos = BeanUtil.copyToList(list, SystemFileTypeListVO.class);
        return TreeUtil.build(vos, 0L, (vo, tree) -> {
            tree.setId(vo.getId());
            tree.setParentId(vo.getParentId());
            tree.putExtra("typeName", vo.getTypeName());
            tree.putExtra("orderNum", vo.getOrderNum());
            tree.putExtra("createBy", vo.getCreateBy());
            tree.putExtra("createName", vo.getCreateName());
            tree.putExtra("createTime", vo.getCreateTime());
            tree.putExtra("updateBy", vo.getUpdateBy());
            tree.putExtra("updateName", vo.getUpdateName());
            tree.putExtra("updateTime", vo.getUpdateTime());
            tree.setWeight(vo.getOrderNum());
        });
    }

    /**
     * 获取文件类型详情
     */
    @DS("slave_1")
    public SystemFileTypeDetailVO detail(SystemFileTypeSingleForm form) {
        SystemFileType systemFileType = systemFileTypeMapper.selectById(form.getId());
        if (systemFileType == null || ObjectUtil.notEqual(IsEffectEnum.NORMAL.getCode(), systemFileType.getIsEffect())) {
            throw new BusinessException(PcsResultCode.SYSTEM_FILE_TYPE_NOT_EXIST);
        }
        return ConvertUtil.convert(systemFileType, SystemFileTypeDetailVO.class);
    }

    /**
     * 新增文件类型
     *
     * @param form      新增入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void add(SystemFileTypeAddForm form, AdminUser adminUser) {
        // 校验参数是否合法 父级类别是否存在
        checkParentExist(form.getParentId());
        // 校验文件类型名是否已存在
        LambdaQueryWrapper<SystemFileType> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SystemFileType::getTypeName, form.getTypeName());
        wrapper.eq(SystemFileType::getIsEffect, IsEffectEnum.NORMAL.getCode());
        SystemFileType select = systemFileTypeMapper.selectOne(wrapper);
        if (select != null) {
            throw new BusinessException(PcsResultCode.SYSTEM_FILE_TYPE_NAME_EXISTS);
        }
        // 新增文件类型
        SystemFileType systemFileType = new SystemFileType();
        systemFileType.setId(IdUtil.getSnowflakeNextId());
        systemFileType.setTypeName(form.getTypeName());
        systemFileType.setParentId(form.getParentId());
        systemFileType.setOrderNum(form.getOrderNum());
        systemFileType.setIsEffect(IsEffectEnum.NORMAL.getCode());
        systemFileType.setCreateBy(adminUser.getId());
        systemFileType.setCreateName(adminUser.getRealName());
        systemFileType.setCreateTime(DateUtil.date());
        systemFileType.setUpdateBy(adminUser.getId());
        systemFileType.setUpdateName(adminUser.getRealName());
        systemFileType.setUpdateTime(systemFileType.getCreateTime());
        systemFileTypeMapper.insert(systemFileType);
        // todo 发起流程
    }

    /**
     * 修改文件类型
     *
     * @param form      修改入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void edit(SystemFileTypeEditForm form, AdminUser adminUser) {
        // 校验数据
        SystemFileType systemFileType = systemFileTypeMapper.selectById(form.getId());
        if (systemFileType == null || ObjectUtil.notEqual(IsEffectEnum.NORMAL.getCode(), systemFileType.getIsEffect())) {
            throw new BusinessException(PcsResultCode.SYSTEM_FILE_TYPE_NOT_EXIST);
        }
        // 校验参数是否合法 父级类别是否存在
        checkParentExist(form.getParentId());
        // 校验当前父分类下文件类型名是否已存在
        LambdaQueryWrapper<SystemFileType> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SystemFileType::getParentId, form.getParentId());
        wrapper.eq(SystemFileType::getTypeName, form.getTypeName());
        wrapper.eq(SystemFileType::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.ne(SystemFileType::getId, form.getId());
        SystemFileType other = systemFileTypeMapper.selectOne(wrapper);
        if (other != null) {
            throw new BusinessException(PcsResultCode.SYSTEM_FILE_TYPE_NAME_EXISTS);
        }
        // 修改文件类型
        systemFileType.setTypeName(form.getTypeName());
        systemFileType.setParentId(form.getParentId());
        systemFileType.setOrderNum(form.getOrderNum());
        systemFileType.setUpdateBy(adminUser.getId());
        systemFileType.setUpdateName(adminUser.getRealName());
        systemFileType.setUpdateTime(DateUtil.date());
        systemFileTypeMapper.updateById(systemFileType);
    }

    /**
     * 删除文件类型
     *
     * @param form      删除入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void del(SystemFileTypeSingleForm form, AdminUser adminUser) {
        // 校验数据
        SystemFileType systemFileType = systemFileTypeMapper.selectById(form.getId());
        if (systemFileType == null || ObjectUtil.notEqual(IsEffectEnum.NORMAL.getCode(), systemFileType.getIsEffect())) {
            throw new BusinessException(PcsResultCode.SYSTEM_FILE_TYPE_NOT_EXIST);
        }
        // 校验文件类型是否有子类型
        checkHasChildren(form.getId());
        // 校验文件类型是否有文件
        checkHasFile(form.getId());
        // 删除文件类型
        systemFileType.setIsEffect(IsEffectEnum.DELETE.getCode());
        systemFileType.setUpdateBy(adminUser.getId());
        systemFileType.setUpdateName(adminUser.getRealName());
        systemFileType.setUpdateTime(DateUtil.date());
        systemFileTypeMapper.updateById(systemFileType);
    }

    // =================================================================== 私有方法区 =================================================================== //

    /**
     * 校验文件类型是否存在
     *
     * @param parentId 文件类父类型ID
     */
    private void checkParentExist(Long parentId) {
        if (parentId == null || parentId == 0) {
            return;
        }
        SystemFileType systemFileType = systemFileTypeMapper.selectById(parentId);
        if (systemFileType == null || ObjectUtil.notEqual(IsEffectEnum.NORMAL.getCode(), systemFileType.getIsEffect())) {
            throw new BusinessException(PcsResultCode.SYSTEM_FILE_PARENT_TYPE_NOT_EXIST);
        }
    }

    /**
     * 校验文件类型是否有子类型
     *
     * @param id 文件类型ID
     */
    private void checkHasChildren(Long id) {
        LambdaQueryWrapper<SystemFileType> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SystemFileType::getParentId, id);
        wrapper.eq(SystemFileType::getIsEffect, IsEffectEnum.NORMAL.getCode());
        Long count = systemFileTypeMapper.selectCount(wrapper);
        if (count > 0) {
            throw new BusinessException(PcsResultCode.SYSTEM_FILE_TYPE_HAS_CHILDREN);
        }
    }

    /**
     * 校验文件类型是否有文件
     *
     * @param id 文件类型ID
     */
    private void checkHasFile(Long id) {
        LambdaQueryWrapper<SystemFile> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SystemFile::getFileTypeId, id);
        wrapper.eq(SystemFile::getIsEffect, IsEffectEnum.NORMAL.getCode());
        Long count = systemFileMapper.selectCount(wrapper);
        if (count > 0) {
            throw new BusinessException(PcsResultCode.SYSTEM_FILE_TYPE_HAS_FILE);
        }
    }
}
