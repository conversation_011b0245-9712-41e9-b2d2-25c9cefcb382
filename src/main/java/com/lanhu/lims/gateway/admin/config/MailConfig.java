package com.lanhu.lims.gateway.admin.config;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/04/26
 */
@Data
public class MailConfig {
    private List<MailProperties> configs;

    @Data
    public static class MailProperties {

        /**
         * 密码
         */
        private String username;

        /**
         * 密码
         */
        private String password;

        /**
         * host
         */
        private String host;

        /**
         * 协议
         */
        private String protocol;

        /**
         * 默认编码
         */
        private String defaultEncoding;

        private Integer port;

    }
}
