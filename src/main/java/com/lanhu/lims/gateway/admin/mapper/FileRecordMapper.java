package com.lanhu.lims.gateway.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanhu.lims.gateway.admin.model.FileRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/********************************
 * @title FileRecordMapper
 * @package com.lanhu.lims.gateway.admin.mapper
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/13 20:49
 * @version 0.0.1
 *********************************/
@Mapper
public interface FileRecordMapper extends BaseMapper<FileRecord> {
    /**
     * 批量插入
     *
     * @param list 文件记录列表
     * @return 插入记录数
     */
    int batchInsert(@Param("list") List<FileRecord> list);
}