package com.lanhu.lims.gateway.admin.controller;

import com.lanhu.lims.gateway.admin.auth.annotation.IgnoreSecurity;
import com.lanhu.lims.gateway.admin.auth.annotation.RequiresPermissions;
import com.lanhu.lims.gateway.admin.core.BaseController;
import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.core.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(value = "服务监控相关", tags = "服务监控相关")
@RestController
public class MonitorController extends BaseController {



    @ApiOperation(value = "服务监控",notes = "服务监控")
    @GetMapping("/monitor")
    @IgnoreSecurity
    public PcsResult monitor(){
        return Result.ok();
    }




}
