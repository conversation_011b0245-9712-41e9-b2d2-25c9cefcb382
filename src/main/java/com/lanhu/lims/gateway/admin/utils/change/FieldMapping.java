package com.lanhu.lims.gateway.admin.utils.change;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/********************************
 * @title FieldMapping
 * @package com.lanhu.lims.gateway.admin.utils.change
 * @description 字段映射注解，用于标注字段的显示名称和值映射关系
 *
 * <AUTHOR>
 * @date 2025/6/8 16:36
 * @version 1.0.0
 *********************************/
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface FieldMapping {
    
    /**
     * 字段显示名称（中文名）
     */
    String name();
    
    /**
     * 值映射关系
     * 例如：性别字段 -1:未知, 0:女, 1:男
     */
    ValueMapping[] mappings() default {};
    
    /**
     * 是否在对比时忽略此字段
     */
    boolean ignore() default false;
    
    /**
     * 字段描述
     */
    String description() default "";
}
