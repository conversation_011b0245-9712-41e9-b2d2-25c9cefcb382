package com.lanhu.lims.gateway.admin.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @description: ${DESCRIPTION}
 * @author: huangzheng
 * @date: 2025/6/13 13:11
 */

/**
 * 委托单附件文件表 （合同文件，需求文件）
 */
@ApiModel(description = "委托单附件文件表 （合同文件，需求文件）")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "t_entrust_order_attachment")
public class EntrustOrderAttachment {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键id")
    private Long id;

    /**
     * 委托单ID
     */
    @TableField(value = "entrust_order_id")
    @ApiModelProperty(value = "委托单ID")
    private Long entrustOrderId;

    /**
     * 附件名称
     */
    @TableField(value = "attachment_name")
    @ApiModelProperty(value = "附件名称")
    private String attachmentName;

    /**
     * 附件大小
     */
    @TableField(value = "attachment_size")
    @ApiModelProperty(value = "附件大小")
    private Long attachmentSize;

    /**
     * 附件后缀名
     */
    @TableField(value = "attachment_suffix")
    @ApiModelProperty(value = "附件后缀名")
    private String attachmentSuffix;

    /**
     * 删除 0:正常 , 1:删除
     */
    @TableField(value = "is_effect")
    @ApiModelProperty(value = "删除 0:正常 , 1:删除")
    private Integer isEffect;

    /**
     * 附件地址url
     */
    @TableField(value = "url")
    @ApiModelProperty(value = "附件地址url")
    private String url;

    /**
     * 备注
     */
    @TableField(value = "remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建人id
     */
    @TableField(value = "create_by")
    @ApiModelProperty(value = "创建人id")
    private Long createBy;

    /**
     * 创建人姓名
     */
    @TableField(value = "create_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createName;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 修改人id
     */
    @TableField(value = "update_by")
    @ApiModelProperty(value = "修改人id")
    private Long updateBy;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 修改人姓名
     */
    @TableField(value = "update_name")
    @ApiModelProperty(value = "修改人姓名")
    private String updateName;

    /**
     * 文件ID, 对应t_file_record
     */
    @TableField(value = "file_id")
    @ApiModelProperty(value = "文件ID, 对应t_file_record")
    private Long fileId;
}