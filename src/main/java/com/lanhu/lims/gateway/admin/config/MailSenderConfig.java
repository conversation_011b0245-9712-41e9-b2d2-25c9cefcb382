package com.lanhu.lims.gateway.admin.config;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Properties;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * 多邮件源发邮件
 * <AUTHOR>
 * @since 2021/04/26
 */
@Slf4j
//@Component
@AllArgsConstructor
public class MailSenderConfig {

    private final MailConfig mailConfig;

    private final List<JavaMailSenderImpl> senderList;
    /**
     * 初始化 sender
     */
    @PostConstruct
    public void buildMailSender(){
        List<MailConfig.MailProperties> mailConfigs = mailConfig.getConfigs();
        log.info("初始化mailSender");
        mailConfigs.forEach(mailProperties -> {
            log.info("mailProperties... name:{}",mailProperties.getUsername());
            // 邮件发送者
            JavaMailSenderImpl javaMailSender = new JavaMailSenderImpl();
            javaMailSender.setDefaultEncoding(mailProperties.getDefaultEncoding());
            javaMailSender.setHost(mailProperties.getHost());
            javaMailSender.setProtocol(mailProperties.getProtocol());
            javaMailSender.setUsername(mailProperties.getUsername().trim());
            javaMailSender.setPassword(mailProperties.getPassword().trim());
            javaMailSender.setPort(mailProperties.getPort());
            if(javaMailSender.getJavaMailProperties().size() == 0){
                Properties properties = javaMailSender.getJavaMailProperties();
              //  properties.put("mail.smtp.ssl.socketFactory.fallback","false");
                properties.put("mail.smtp.auth","true");
                properties.put("mail.smtp.starttls.enable","true");
                properties.put("mail.smtp.ssl.enable","true");
               // properties.put("mail.smtp.ssl.socketFactory.class","com.sun.mail.util.MailSSLSocketFactory");
            }
            // 添加数据
            senderList.add(javaMailSender);
        });
    }

    /**
     * 获取MailSender
     * @return CustomMailSender
     */
    public JavaMailSenderImpl getSender(){
        if(senderList.isEmpty()){
            buildMailSender();
        }
        // 随机返回一个JavaMailSender
        return senderList.get(new Random().nextInt(senderList.size()));
    }

    public JavaMailSenderImpl getOtherSender(String userName){
        if(senderList.isEmpty()){
            buildMailSender();
        }
        // 随机返回一个JavaMailSender
        List<JavaMailSenderImpl> newSenderList = senderList
                .stream()
                .filter(e->!e.getUsername().equals(userName))
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(newSenderList)){
            log.warn("获取邮箱服务器失败");
            return null;
        }
        return newSenderList.get(new Random().nextInt(newSenderList.size()));
    }

    /**
     * 清理 sender
     */
    public void clear(){
        senderList.clear();
    }
}
