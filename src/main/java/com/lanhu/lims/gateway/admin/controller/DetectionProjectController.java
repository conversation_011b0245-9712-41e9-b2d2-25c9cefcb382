package com.lanhu.lims.gateway.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanhu.lims.gateway.admin.annotation.Log;
import com.lanhu.lims.gateway.admin.auth.constants.SecurityConstants;
import com.lanhu.lims.gateway.admin.auth.context.SecurityContextHolder;
import com.lanhu.lims.gateway.admin.core.BaseController;
import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.core.Result;
import com.lanhu.lims.gateway.admin.enums.LogBusinessType;
import com.lanhu.lims.gateway.admin.enums.LogOperatorType;
import com.lanhu.lims.gateway.admin.model.LoginUser;
import com.lanhu.lims.gateway.admin.service.DetectionProjectService;
import com.lanhu.lims.gateway.admin.vo.req.*;
import com.lanhu.lims.gateway.admin.vo.resp.DetectionProjectDetailVO;
import com.lanhu.lims.gateway.admin.vo.resp.DetectionProjectListVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/********************************
 * @title DetectionProjectController
 * @package com.lanhu.lims.gateway.admin.controller
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/14 14:51
 * @version 0.0.1
 *********************************/
@RestController
@Api(tags = "检测项目相关接口", value = "检测项目相关接口")
public class DetectionProjectController extends BaseController {
    @Resource
    private DetectionProjectService detectionProjectService;

    /**
     * 查询检测项目列表
     */
    @ApiOperation(value = "查询检测项目列表")
    @PostMapping("/detectionProject/v1/list")
    public PcsResult<List<DetectionProjectListVO>> list(@Validated @RequestBody DetectionProjectListForm form) {
        return Result.ok(detectionProjectService.list(form));
    }

    /**
     * 查询检测项目分页列表
     */
    @ApiOperation(value = "查询检测项目分页列表")
    @PostMapping("/detectionProject/v1/listPage")
    public PcsResult<IPage<DetectionProjectListVO>> listPage(@Validated @RequestBody DetectionProjectListPageForm form) {
        return Result.ok(detectionProjectService.listPage(form));
    }

    /**
     * 查询检测项目详情
     */
    @ApiOperation(value = "查询检测项目详情")
    @PostMapping("/detectionProject/v1/detail")
    public PcsResult<DetectionProjectDetailVO> detail(@Validated @RequestBody DetectionProjectSingleForm form) {
        return Result.ok(detectionProjectService.detail(form));
    }

    /**
     * 新增检测项目
     */
    @ApiOperation(value = "新增检测项目")
    @PostMapping("/detectionProject/v1/add")
    @Log(title = "新增检测项目", businessType = LogBusinessType.INSERT, operatorType = LogOperatorType.PC)
    public PcsResult<Void> add(@Validated @RequestBody DetectionProjectAddForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        detectionProjectService.add(form, loginUser.getSysUser());
        return Result.ok();
    }

    /**
     * 修改检测项目
     */
    @ApiOperation(value = "修改检测项目")
    @PostMapping("/detectionProject/v1/edit")
    @Log(title = "修改检测项目", businessType = LogBusinessType.UPDATE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> edit(@Validated @RequestBody DetectionProjectEditForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        detectionProjectService.edit(form, loginUser.getSysUser());
        return Result.ok();
    }

    /**
     * 删除检测项目
     */
    @ApiOperation(value = "删除检测项目")
    @PostMapping("/detectionProject/v1/del")
    @Log(title = "删除检测项目", businessType = LogBusinessType.LOGICDELETE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> del(@Validated @RequestBody DetectionProjectDelForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        detectionProjectService.del(form, loginUser.getSysUser());
        return Result.ok();
    }

    /**
     * 启用检测项目
     */
    @ApiOperation(value = "启用检测项目")
    @PostMapping("/detectionProject/v1/enable")
    @Log(title = "启用检测项目", businessType = LogBusinessType.ENABLE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> enable(@Validated @RequestBody DetectionProjectSingleForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        detectionProjectService.enable(form, loginUser.getSysUser());
        return Result.ok();
    }

    /**
     * 禁用检测项目
     */
    @ApiOperation(value = "禁用检测项目")
    @PostMapping("/detectionProject/v1/disable")
    @Log(title = "禁用检测项目", businessType = LogBusinessType.DISABLE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> disable(@Validated @RequestBody DetectionProjectSingleForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        detectionProjectService.disable(form, loginUser.getSysUser());
        return Result.ok();
    }
}
