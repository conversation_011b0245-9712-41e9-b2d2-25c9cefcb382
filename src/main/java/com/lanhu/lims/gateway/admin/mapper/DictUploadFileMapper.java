package com.lanhu.lims.gateway.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanhu.lims.gateway.admin.model.DictUploadFile;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface DictUploadFileMapper extends BaseMapper<DictUploadFile> {
    int updateBatch(List<DictUploadFile> list);

    int batchInsert(@Param("list") List<DictUploadFile> list);

    int insertOrUpdate(DictUploadFile record);

    int insertOrUpdateSelective(DictUploadFile record);


    DictUploadFile selectByFileType(String fileType);
}