package com.lanhu.lims.gateway.admin.utils.sequence.formatter;

/********************************
 * @title ZeroPaddingFormatter
 * @package com.lanhu.lims.gateway.admin.utils.sequence.formatter
 * @description 数字补零格式化器
 *
 * <AUTHOR>
 * @date 2025/6/9 22:00
 * @version 0.0.1
 *********************************/
public class ZeroPaddingFormatter implements SequenceFormatter {
    private final int length;

    public ZeroPaddingFormatter(int length) {
        this.length = length;
    }

    @Override
    public String format(int value) {
        return String.format("%0" + length + "d", value);
    }
}
