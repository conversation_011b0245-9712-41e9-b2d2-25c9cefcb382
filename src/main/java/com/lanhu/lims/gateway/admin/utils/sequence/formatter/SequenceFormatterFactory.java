package com.lanhu.lims.gateway.admin.utils.sequence.formatter;

/********************************
 * @title SequenceFormatterFactory
 * @package com.lanhu.lims.gateway.admin.utils.sequence.formatter
 * @description 序列格式化器工厂类
 *
 * <AUTHOR>
 * @date 2025/6/9 22:00
 * @version 0.0.1
 *********************************/
public class SequenceFormatterFactory {
    
    /**
     * 创建补零格式化器
     *
     * @param length 补零长度
     * @return 补零格式化器
     */
    public static SequenceFormatter zeroPadding(int length) {
        return new ZeroPaddingFormatter(length);
    }
    
    /**
     * 创建自定义模式格式化器
     *
     * @param pattern 格式化模式
     * @return 自定义模式格式化器
     */
    public static SequenceFormatter customPattern(String pattern) {
        return new CustomPatternFormatter(pattern);
    }
    
    /**
     * 创建日期+序列号格式化器
     *
     * @param prefix 前缀
     * @param seqLength 序列号长度
     * @return 格式化器
     */
    public static SequenceFormatter dateSequence(String prefix, int seqLength) {
        return new CustomPatternFormatter(prefix + "{yyyy}{MM}{dd}{seq:" + seqLength + "}");
    }
    
    /**
     * 创建时间戳+序列号格式化器
     *
     * @param prefix 前缀
     * @param seqLength 序列号长度
     * @return 格式化器
     */
    public static SequenceFormatter timestampSequence(String prefix, int seqLength) {
        return new CustomPatternFormatter(prefix + "{yyyy}{MM}{dd}{HH}{mm}{ss}{seq:" + seqLength + "}");
    }
    
    /**
     * 创建前缀+补零序列号格式化器
     *
     * @param prefix 前缀
     * @param seqLength 序列号长度
     * @return 格式化器
     */
    public static SequenceFormatter prefixSequence(String prefix, int seqLength) {
        return new CustomPatternFormatter(prefix + "{seq:" + seqLength + "}");
    }
    
    /**
     * 创建年月+序列号格式化器
     *
     * @param prefix 前缀
     * @param seqLength 序列号长度
     * @return 格式化器
     */
    public static SequenceFormatter yearMonthSequence(String prefix, int seqLength) {
        return new CustomPatternFormatter(prefix + "{yyyy}{MM}{seq:" + seqLength + "}");
    }
}
