package com.lanhu.lims.gateway.admin.flow.vo.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:
 * @author: huangzheng
 * @date: 2025/5/29 16:41
 */

@Data
@ApiModel("流程实例分页查询出参")
public class InstanceListVO {

    /**
     * 流程实例id
     */
    @ApiModelProperty("流程实例id")
    private Long instanceId;


    /**
     * 流程定义id
     */
    @ApiModelProperty("流程定义id")
    private Long definitionId;


    /**
     * 流程定义名称
     */
    @ApiModelProperty("流程定义名称")
    private String instanceName;


    /**
     * 任务名称
     */
    @ApiModelProperty("任务名称")
    private String nodeName;


    @ApiModelProperty("业务类型名称")
    private String categoryName;


    /**
     * 流程编码
     */
    @ApiModelProperty("流程编码")
    private String flowCode;



    /**
     * 流程状态
     */
    @ApiModelProperty("流程状态")
    private String flowStatus;


    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    private String version;



    /**
     * 实例申请人
     */
    @ApiModelProperty("实例申请人")
    private String applyUser;



    /**
     * 实例创建时间
     */
    @ApiModelProperty("实例创建时间")
    private String createTime;



    /**
     * 实例更新时间
     */
    @ApiModelProperty("实例更新时间")
    private String updateTime;








}
