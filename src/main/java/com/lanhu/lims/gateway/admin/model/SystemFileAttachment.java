package com.lanhu.lims.gateway.admin.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/********************************
 * @title SystemFileAttachment
 * @package com.lanhu.lims.gateway.admin.model
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/13 20:44
 * @version 0.0.1
 *********************************/
@ApiModel(description = "t_system_file_attachment")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "t_system_file_attachment")
public class SystemFileAttachment {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.INPUT)
    @ApiModelProperty(value = "主键id")
    private Long id;

    /**
     * 文件id
     */
    @TableField(value = "system_file_id")
    @ApiModelProperty(value = "文件id")
    private Long systemFileId;

    /**
     * 版本
     */
    @TableField(value = "version")
    @ApiModelProperty(value = "版本")
    private String version;

    /**
     * 文件ID, 对应t_file_record
     */
    @TableField(value = "file_id")
    @ApiModelProperty(value = "文件ID, 对应t_file_record")
    private Long fileId;

    /**
     * 附件url
     */
    @TableField(value = "attachment_url")
    @ApiModelProperty(value = "附件url")
    private String attachmentUrl;

    /**
     * 附件名称
     */
    @TableField(value = "attachment_name")
    @ApiModelProperty(value = "附件名称")
    private String attachmentName;

    /**
     * 附件后缀
     */
    @TableField(value = "attachment_suffix")
    @ApiModelProperty(value = "附件后缀")
    private String attachmentSuffix;

    /**
     * 附件大小
     */
    @TableField(value = "attachment_size")
    @ApiModelProperty(value = "附件大小")
    private Long attachmentSize;

    /**
     * 是否有效，0：正常，1：删除
     */
    @TableField(value = "is_effect")
    @ApiModelProperty(value = "是否有效，0：正常，1：删除")
    private Integer isEffect;

    /**
     * 上传人id
     */
    @TableField(value = "create_by")
    @ApiModelProperty(value = "上传人id")
    private Long createBy;

    /**
     * 上传人姓名
     */
    @TableField(value = "create_name")
    @ApiModelProperty(value = "上传人姓名")
    private String createName;

    /**
     * 上传时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value = "上传时间")
    private Date createTime;

    /**
     * 更新人id
     */
    @TableField(value = "update_by")
    @ApiModelProperty(value = "更新人id")
    private Long updateBy;

    /**
     * 更新人姓名
     */
    @TableField(value = "update_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updateName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 审核状态(-2: 审核不通过 0:待审核 1: 审核中 2:审核通过/正常)
     */
    @TableField(value = "audit_status")
    @ApiModelProperty(value = "审核状态(-2: 审核不通过 0:待审核 1: 审核中 2:审核通过/正常)")
    private Integer auditStatus;

    /**
     * 最近一次审核人ID
     */
    @TableField(value = "audit_by")
    @ApiModelProperty(value = "最近一次审核人ID")
    private Long auditBy;

    /**
     * 最近一次审核时间
     */
    @TableField(value = "audit_time")
    @ApiModelProperty(value = "最近一次审核时间")
    private Date auditTime;

    /**
     * 最近一次审核人名称
     */
    @TableField(value = "audit_name")
    @ApiModelProperty(value = "最近一次审核人名称")
    private String auditName;

    /**
     * 审核备注/审核不通过原因
     */
    @TableField(value = "audit_remark")
    @ApiModelProperty(value = "审核备注/审核不通过原因")
    private String auditRemark;
}