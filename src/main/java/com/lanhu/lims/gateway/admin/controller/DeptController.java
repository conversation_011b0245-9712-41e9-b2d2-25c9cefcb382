package com.lanhu.lims.gateway.admin.controller;

import com.lanhu.lims.gateway.admin.auth.annotation.IgnoreSecurity;
import com.lanhu.lims.gateway.admin.auth.annotation.RequiresPermissions;
import com.lanhu.lims.gateway.admin.core.BaseController;
import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.model.Dept;
import com.lanhu.lims.gateway.admin.service.DeptService;
import com.lanhu.lims.gateway.admin.vo.req.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * @description: 部门管理相关接口
 * @author: huangzheng
 * @date: 2025/5/12 14:38
 */

@RestController
@Validated
@Slf4j
@Api(tags = "部门管理相关接口",value = "部门管理相关接口")
public class DeptController extends BaseController {

    @Resource
    private DeptService deptService;


    /**
     * 分页查询查询部门
     * @return
     */
    @PostMapping("/dept/v1/treeList")
    @ApiOperation("查询部门树形列表")
    @RequiresPermissions("system:dept:treeList")
    public PcsResult<List<Dept>> treeList(@Validated  @RequestBody DeptTreeListForm deptTreeListForm) {
        return deptService.treeList(deptTreeListForm);
    }



    /**
     * 新增部门
     * @return
     */
    @PostMapping("/dept/v1/add")
    @ApiOperation("新增部门")
    @RequiresPermissions("system:dept:add")
    public PcsResult add(@Validated @RequestBody  DeptAddForm deptAddForm) {
        return deptService.add(deptAddForm);
    }



    /**
     * 修改部门
     */
    @PostMapping("/dept/v1/edit")
    @ApiOperation("修改部门")
    @RequiresPermissions("system:dept:edit")
    public PcsResult edit(@Validated @RequestBody DeptEditForm deptEditForm) {
        return deptService.edit(deptEditForm);
    }


    /**
     * 删除部门
     */
    @PostMapping("/dept/v1/del")
    @ApiOperation("删除部门")
    @RequiresPermissions("system:dept:del")
    public PcsResult del(@Validated @RequestBody DeptDeleteForm deptDeleteForm) {
        return deptService.del(deptDeleteForm.getDeptId());
    }



    @PostMapping("/dept/v1/detail")
    @ApiOperation("部门详情")
    @RequiresPermissions("system:dept:detail")
    public PcsResult<Dept> detail(@Validated @RequestBody DeptDetailForm deptDetailForm) {
        return deptService.detail(deptDetailForm.getDeptId());
    }


    @PostMapping("/dept/v1/enable")
    @ApiOperation("部门启用")
    @RequiresPermissions("system:dept:enable")
    public PcsResult enable(@Validated @RequestBody DeptEnableForm deptEnableForm) {
        return deptService.enable(deptEnableForm.getDeptId());
    }

    @PostMapping("/dept/v1/disable")
    @ApiOperation("部门禁用")
    @RequiresPermissions("system:dept:disable")
    public PcsResult disable(@Validated @RequestBody DeptDisableForm deptDisableForm) {
        return deptService.disable(deptDisableForm.getDeptId());
    }

















}
