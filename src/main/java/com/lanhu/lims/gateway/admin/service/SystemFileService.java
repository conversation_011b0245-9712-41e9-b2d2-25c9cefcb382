package com.lanhu.lims.gateway.admin.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanhu.lims.gateway.admin.annotation.LhTransaction;
import com.lanhu.lims.gateway.admin.core.BusinessException;
import com.lanhu.lims.gateway.admin.core.PcsResultCode;
import com.lanhu.lims.gateway.admin.enums.AuditStatusEnum;
import com.lanhu.lims.gateway.admin.enums.IsEffectEnum;
import com.lanhu.lims.gateway.admin.mapper.FileRecordMapper;
import com.lanhu.lims.gateway.admin.mapper.SystemFileAttachmentMapper;
import com.lanhu.lims.gateway.admin.mapper.SystemFileMapper;
import com.lanhu.lims.gateway.admin.mapper.SystemFileTypeMapper;
import com.lanhu.lims.gateway.admin.model.*;
import com.lanhu.lims.gateway.admin.utils.ConvertUtil;
import com.lanhu.lims.gateway.admin.utils.sequence.SequenceNameEnum;
import com.lanhu.lims.gateway.admin.utils.sequence.SequenceUtil;
import com.lanhu.lims.gateway.admin.vo.req.*;
import com.lanhu.lims.gateway.admin.vo.resp.SystemFileAttachmentVO;
import com.lanhu.lims.gateway.admin.vo.resp.SystemFileDetailVO;
import com.lanhu.lims.gateway.admin.vo.resp.SystemFileListVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/********************************
 * @title SystemFileService
 * @package com.lanhu.lims.gateway.admin.service
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/8 10:38
 * @version 0.0.1
 *********************************/
@Service
public class SystemFileService {
    @Resource
    private SystemFileMapper systemFileMapper;

    @Resource
    private SystemFileAttachmentMapper systemFileAttachmentMapper;

    @Resource
    private SystemFileTypeMapper systemFileTypeMapper;

    @Resource
    private FileRecordMapper fileRecordMapper;

    /**
     * 文件列表查询
     *
     * @param form 查询入参
     * @return 文件列表
     */
    @DS("slave_1")
    public List<SystemFileListVO> list(SystemFileListForm form) {
        // 获取文件类型id以及所有的子类id
        List<Long> allFileTypeIds = getAllIdsByType(form.getFileTypeId());
        LambdaQueryWrapper<SystemFile> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SystemFile::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.like(StrUtil.isNotBlank(form.getFileName()), SystemFile::getFileName, form.getFileName());
        wrapper.like(StrUtil.isNotBlank(form.getFileCode()), SystemFile::getFileCode, form.getFileCode());
        wrapper.in(ObjectUtil.isNotNull(form.getFileTypeId()) && CollUtil.isNotEmpty(allFileTypeIds), SystemFile::getFileTypeId, allFileTypeIds);
        wrapper.eq(ObjectUtil.isNotNull(form.getFileTypeId()) && CollUtil.isEmpty(allFileTypeIds), SystemFile::getFileTypeId, -1);
        wrapper.orderByDesc(SystemFile::getFileCode);
        List<SystemFile> list = systemFileMapper.selectList(wrapper);
        return ConvertUtil.convertList(list, SystemFileListVO.class);
    }

    /**
     * 文件分页列表查询
     *
     * @param form 查询入参
     * @return 文件分页列表
     */
    @DS("slave_1")
    public IPage<SystemFileListVO> listPage(SystemFileListPageForm form) {
        // 获取文件类型id以及所有的子类id
        List<Long> allFileTypeIds = getAllIdsByType(form.getFileTypeId());
        LambdaQueryWrapper<SystemFile> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SystemFile::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.like(StrUtil.isNotBlank(form.getFileName()), SystemFile::getFileName, form.getFileName());
        wrapper.like(StrUtil.isNotBlank(form.getFileCode()), SystemFile::getFileCode, form.getFileCode());
        wrapper.in(ObjectUtil.isNotNull(form.getFileTypeId()) && CollUtil.isNotEmpty(allFileTypeIds), SystemFile::getFileTypeId, allFileTypeIds);
        wrapper.eq(ObjectUtil.isNotNull(form.getFileTypeId()) && CollUtil.isEmpty(allFileTypeIds), SystemFile::getFileTypeId, -1);
        wrapper.orderByDesc(SystemFile::getFileCode);
        Page<SystemFile> page = new Page<>(form.getPageIndex(), form.getPageSize());
        return ConvertUtil.convertPage(page, SystemFileListVO.class);
    }

    /**
     * 文件详情查询
     *
     * @param form 查询入参
     * @return 文件详情
     */
    @DS("slave_1")
    public SystemFileDetailVO detail(SystemFileSingleForm form) {
        SystemFile systemFile = systemFileMapper.selectById(form.getId());
        if (systemFile == null || ObjectUtil.notEqual(IsEffectEnum.NORMAL.getCode(), systemFile.getIsEffect())) {
            throw new BusinessException(PcsResultCode.SYSTEM_FILE_NOT_EXIST);
        }
        // 查询附件
        LambdaQueryWrapper<SystemFileAttachment> attachmentWrapper = Wrappers.lambdaQuery();
        attachmentWrapper.eq(SystemFileAttachment::getSystemFileId, systemFile.getId());
        attachmentWrapper.eq(SystemFileAttachment::getIsEffect, IsEffectEnum.NORMAL.getCode());
        attachmentWrapper.orderByDesc(SystemFileAttachment::getCreateTime);
        List<SystemFileAttachment> attachments = systemFileAttachmentMapper.selectList(attachmentWrapper);
        // 转换为详情VO
        return ConvertUtil.convertDetailWithChildren(systemFile, SystemFileDetailVO.class, attachments, SystemFileAttachmentVO.class, SystemFileDetailVO::setAttachments);
    }

    /**
     * 新增文件
     *
     * @param form      新增入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    @LhTransaction
    public void add(SystemFileAddForm form, AdminUser adminUser) {
        // 校验文件类型是否存在
        SystemFileType systemFileType = systemFileTypeMapper.selectById(form.getFileTypeId());
        if (systemFileType == null || ObjectUtil.notEqual(IsEffectEnum.NORMAL.getCode(), systemFileType.getIsEffect())) {
            throw new BusinessException(PcsResultCode.SYSTEM_FILE_TYPE_NOT_EXIST);
        }
        // 获取文件记录
        FileRecord fileRecord = fileRecordMapper.selectById(form.getFileRecordId());
        if (fileRecord == null) {
            throw new BusinessException(PcsResultCode.FILE_RECORD_NOT_EXIST);
        }
        // 获取文件编码
        String fileCode = SequenceUtil.getNextFormattedValue(SequenceNameEnum.SYSTEM_FILE_CODE);
        // 新增文件
        SystemFile systemFile = new SystemFile();
        systemFile.setId(IdUtil.getSnowflakeNextId());
        systemFile.setFileCode(fileCode);
        systemFile.setFileName(form.getFileName());
        systemFile.setFileTypeId(form.getFileTypeId());
        systemFile.setVersion(form.getVersion());
        systemFile.setRemark(StrUtil.blankToDefault(form.getRemark(), StrUtil.EMPTY));
        systemFile.setIsEffect(IsEffectEnum.NORMAL.getCode());
        systemFile.setCreateBy(adminUser.getId());
        systemFile.setCreateName(adminUser.getRealName());
        systemFile.setCreateTime(DateUtil.date());
        systemFile.setUpdateBy(adminUser.getId());
        systemFile.setUpdateName(adminUser.getRealName());
        systemFile.setUpdateTime(systemFile.getCreateTime());
        systemFile.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        systemFile.setAuditName(StrUtil.EMPTY);
        systemFile.setAuditBy(null);
        systemFile.setAuditTime(null);
        systemFile.setAuditRemark(StrUtil.EMPTY);
        systemFileMapper.insert(systemFile);
        // 保存附件
        saveAttachment(systemFile, form.getVersion(), fileRecord, adminUser);
        // todo 发起流程
    }

    /**
     * 修改文件
     *
     * @param form      修改入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void edit(SystemFileEditForm form, AdminUser adminUser) {
        // 检验数据
        SystemFile systemFile = checkCanEditOrDelete(form.getId());
        // 修改文件
        systemFile.setFileName(form.getFileName());
        systemFile.setRemark(StrUtil.blankToDefault(form.getRemark(), StrUtil.EMPTY));
        systemFile.setUpdateBy(adminUser.getId());
        systemFile.setUpdateName(adminUser.getRealName());
        systemFile.setUpdateTime(DateUtil.date());
        systemFile.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        systemFileMapper.updateById(systemFile);
        // todo 发起流程
    }

    /**
     * 删除文件
     *
     * @param form      删除入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void del(SystemFileDelForm form, AdminUser adminUser) {
        // 检验数据
        SystemFile systemFile = checkCanEditOrDelete(form.getId());
        // 删除文件
        systemFile.setIsEffect(IsEffectEnum.DELETE.getCode());
        systemFile.setUpdateBy(adminUser.getId());
        systemFile.setUpdateName(adminUser.getRealName());
        systemFile.setUpdateTime(DateUtil.date());
        systemFile.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        systemFileMapper.updateById(systemFile);
        // todo 发起流程
    }

    /**
     * 新增文件版本
     *
     * @param form      新增入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void addVersion(SystemFileVersionAddForm form, AdminUser adminUser) {
        // 检验数据
        SystemFile systemFile = checkCanEditOrDelete(form.getId());
        // 获取文件记录
        FileRecord fileRecord = fileRecordMapper.selectById(form.getFileRecordId());
        if (fileRecord == null) {
            throw new BusinessException(PcsResultCode.FILE_RECORD_NOT_EXIST);
        }
        // 保存附件
        saveAttachment(systemFile, form.getVersion(), fileRecord, adminUser);
        // todo 发起流程
    }

    // =================================================================== 工作流回调 =================================================================== //

    // =================================================================== 私有方法区 =================================================================== //

    /**
     * 获取文件类型id以及所有的子类型id
     *
     * @param typeId 文件类型id
     * @return 类型id以及所有的子类型id
     */
    private List<Long> getAllIdsByType(Long typeId) {
        if (typeId == null) {
            return CollUtil.newArrayList();
        }
        List<Long> allIds = CollUtil.newArrayList(typeId);
        // 获取所有的文件类型
        LambdaQueryWrapper<SystemFileType> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SystemFileType::getIsEffect, IsEffectEnum.NORMAL.getCode());
        List<SystemFileType> allTypes = systemFileTypeMapper.selectList(wrapper);
        // 递归获取所有子类型ID
        collectChildrenIds(typeId, allIds, allTypes);
        return allIds;
    }

    /**
     * 递归收集所有子类型ID
     *
     * @param parentId 父类型ID
     * @param idList   收集ID的列表
     * @param allTypes 所有类型列表
     */
    private void collectChildrenIds(Long parentId, List<Long> idList, List<SystemFileType> allTypes) {
        List<SystemFileType> children = allTypes.stream().filter(type -> parentId.equals(type.getParentId())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(children)) {
            for (SystemFileType child : children) {
                idList.add(child.getId());
                // 递归查找子类型的子类型
                collectChildrenIds(child.getId(), idList, allTypes);
            }
        }
    }

    /**
     * 校验文件是否允许修改或删除
     *
     * @param id 文件ID
     * @return 文件
     */
    private SystemFile checkCanEditOrDelete(Long id) {
        SystemFile systemFile = systemFileMapper.selectById(id);
        if (systemFile == null || ObjectUtil.notEqual(IsEffectEnum.NORMAL.getCode(), systemFile.getIsEffect())) {
            throw new BusinessException(PcsResultCode.SYSTEM_FILE_NOT_EXIST);
        }
        if (ObjectUtil.notEqual(AuditStatusEnum.PASS.getCode(), systemFile.getAuditStatus())) {
            throw new BusinessException(PcsResultCode.DATA_WAS_AUDITING_CANT_EDIT_OR_DELETE);
        }
        return systemFile;
    }

    /**
     * 保存附件
     *
     * @param systemFile 文件
     * @param version    版本
     * @param fileRecord 文件记录
     * @param adminUser  当前用户
     */
    private void saveAttachment(SystemFile systemFile, String version, FileRecord fileRecord, AdminUser adminUser) {
        // 新增附件
        SystemFileAttachment systemFileAttachment = new SystemFileAttachment();
        systemFileAttachment.setId(IdUtil.getSnowflakeNextId());
        systemFileAttachment.setSystemFileId(systemFile.getId());
        systemFileAttachment.setVersion(version);
        systemFileAttachment.setFileId(fileRecord.getId());
        systemFileAttachment.setAttachmentUrl(fileRecord.getAttachmentUrl());
        systemFileAttachment.setAttachmentName(fileRecord.getAttachmentName());
        systemFileAttachment.setAttachmentSuffix(fileRecord.getAttachmentSuffix());
        systemFileAttachment.setAttachmentSize(fileRecord.getAttachmentSize());
        systemFileAttachment.setIsEffect(IsEffectEnum.NORMAL.getCode());
        systemFileAttachment.setCreateBy(adminUser.getId());
        systemFileAttachment.setCreateName(adminUser.getRealName());
        systemFileAttachment.setCreateTime(DateUtil.date());
        systemFileAttachment.setUpdateBy(adminUser.getId());
        systemFileAttachment.setUpdateName(adminUser.getRealName());
        systemFileAttachment.setUpdateTime(systemFileAttachment.getCreateTime());
        systemFileAttachment.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        systemFileAttachment.setAuditName(StrUtil.EMPTY);
        systemFileAttachment.setAuditBy(null);
        systemFileAttachment.setAuditTime(null);
        systemFileAttachment.setAuditRemark(StrUtil.EMPTY);
        systemFileAttachmentMapper.insert(systemFileAttachment);
    }
}
