package com.lanhu.lims.gateway.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanhu.lims.gateway.admin.model.EntrustOrder;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @description: ${DESCRIPTION}
 * @author: huangzheng
 * @date: 2025/6/16 15:26
 */
@Mapper
public interface EntrustOrderMapper extends BaseMapper<EntrustOrder> {
    int updateBatch(@Param("list") List<EntrustOrder> list);

    int updateBatchSelective(@Param("list") List<EntrustOrder> list);

    int batchInsert(@Param("list") List<EntrustOrder> list);

    int batchInsertOrUpdate(@Param("list") List<EntrustOrder> list);

    int insertOnDuplicateUpdate(EntrustOrder record);

    int insertOnDuplicateUpdateSelective(EntrustOrder record);
}