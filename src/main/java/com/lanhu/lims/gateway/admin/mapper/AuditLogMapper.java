package com.lanhu.lims.gateway.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanhu.lims.gateway.admin.model.AuditLog;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @description: ${DESCRIPTION}
 * @author: huangzheng
 * @date: 2025/5/26 15:24
 */
@Mapper
public interface AuditLogMapper extends BaseMapper<AuditLog> {
    int updateBatch(@Param("list") List<AuditLog> list);

    int updateBatchSelective(@Param("list") List<AuditLog> list);

    int batchInsert(@Param("list") List<AuditLog> list);

    int batchInsertOrUpdate(@Param("list") List<AuditLog> list);

    int insertOnDuplicateUpdate(AuditLog record);

    int insertOnDuplicateUpdateSelective(AuditLog record);
}