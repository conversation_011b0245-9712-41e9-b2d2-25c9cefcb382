package com.lanhu.lims.gateway.admin.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanhu.lims.gateway.admin.core.BusinessException;
import com.lanhu.lims.gateway.admin.core.PcsResultCode;
import com.lanhu.lims.gateway.admin.enums.AuditStatusEnum;
import com.lanhu.lims.gateway.admin.enums.DictDataTypeEnum;
import com.lanhu.lims.gateway.admin.enums.EnableEnum;
import com.lanhu.lims.gateway.admin.enums.IsEffectEnum;
import com.lanhu.lims.gateway.admin.mapper.ConsumableMapper;
import com.lanhu.lims.gateway.admin.mapper.ConsumableSpecificationMapper;
import com.lanhu.lims.gateway.admin.mapper.DictDataMapper;
import com.lanhu.lims.gateway.admin.model.AdminUser;
import com.lanhu.lims.gateway.admin.model.Consumable;
import com.lanhu.lims.gateway.admin.model.ConsumableSpecification;
import com.lanhu.lims.gateway.admin.model.DictData;
import com.lanhu.lims.gateway.admin.utils.ConvertUtil;
import com.lanhu.lims.gateway.admin.utils.sequence.SequenceNameEnum;
import com.lanhu.lims.gateway.admin.utils.sequence.SequenceUtil;
import com.lanhu.lims.gateway.admin.vo.req.*;
import com.lanhu.lims.gateway.admin.vo.resp.ConsumableDetailVO;
import com.lanhu.lims.gateway.admin.vo.resp.ConsumableListVO;
import com.lanhu.lims.gateway.admin.vo.resp.ConsumableSpecificationVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/********************************
 * @title ConsumableService
 * @package com.lanhu.lims.gateway.admin.service
 * @description 耗材管理服务
 *
 * <AUTHOR> Assistant
 * @date 2025/6/17
 * @version 0.0.1
 *********************************/
@Service
public class ConsumableService {
    @Resource
    private ConsumableMapper consumableMapper;

    @Resource
    private ConsumableSpecificationMapper consumableSpecificationMapper;

    @Resource
    private DictDataMapper dictDataMapper;

    /**
     * 查询耗材列表
     *
     * @param form 查询入参
     * @return 列表
     */
    @DS("slave_1")
    public List<ConsumableListVO> list(ConsumableListForm form) {
        LambdaQueryWrapper<Consumable> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Consumable::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.eq(Consumable::getStatus, EnableEnum.ENABLE.getCode());
        wrapper.like(StrUtil.isNotBlank(form.getConsumableCode()), Consumable::getConsumableCode, form.getConsumableCode());
        wrapper.like(StrUtil.isNotBlank(form.getConsumableName()), Consumable::getConsumableName, form.getConsumableName());
        wrapper.like(StrUtil.isNotBlank(form.getShortName()), Consumable::getShortName, form.getShortName());
        wrapper.orderByDesc(Consumable::getCreateTime);
        List<Consumable> list = consumableMapper.selectList(wrapper);
        return ConvertUtil.convertList(list, ConsumableListVO.class);
    }

    /**
     * 查询耗材分页列表
     *
     * @param form 查询入参
     * @return 分页列表
     */
    @DS("slave_1")
    public IPage<ConsumableListVO> listPage(ConsumableListPageForm form) {
        LambdaQueryWrapper<Consumable> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Consumable::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.eq(form.getStatus() != null, Consumable::getStatus, form.getStatus());
        wrapper.like(StrUtil.isNotBlank(form.getConsumableCode()), Consumable::getConsumableCode, form.getConsumableCode());
        wrapper.like(StrUtil.isNotBlank(form.getConsumableName()), Consumable::getConsumableName, form.getConsumableName());
        wrapper.like(StrUtil.isNotBlank(form.getShortName()), Consumable::getShortName, form.getShortName());
        wrapper.orderByDesc(Consumable::getCreateTime);
        Page<Consumable> page = new Page<>(form.getPageIndex(), form.getPageSize());
        IPage<Consumable> result = consumableMapper.selectPage(page, wrapper);
        return ConvertUtil.convertPage(result, ConsumableListVO.class);
    }

    /**
     * 查询耗材详情
     *
     * @param form 查询入参
     * @return 详情
     */
    @DS("slave_1")
    public ConsumableDetailVO detail(ConsumableSingleForm form) {
        Consumable consumable = consumableMapper.selectById(form.getId());
        if (consumable == null || consumable.getIsEffect() != IsEffectEnum.NORMAL.getCode()) {
            throw new BusinessException(PcsResultCode.CONSUMABLE_NOT_EXIST);
        }
        // 转换为详情VO
        return ConvertUtil.convert(consumable, ConsumableDetailVO.class);
    }

    /**
     * 新增耗材
     *
     * @param form      新增入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void add(ConsumableAddForm form, AdminUser adminUser) {
        // 生成耗材编号
        String consumableCode = SequenceUtil.getNextFormattedValue(SequenceNameEnum.CONSUMABLE_CODE);
        // 新增耗材
        Consumable consumable = new Consumable();
        consumable.setConsumableCode(consumableCode);
        consumable.setConsumableName(form.getConsumableName());
        consumable.setShortName(form.getShortName());
        consumable.setStatus(EnableEnum.ENABLE.getCode());
        consumable.setIsEffect(IsEffectEnum.NORMAL.getCode());
        consumable.setCreateBy(adminUser.getId());
        consumable.setCreateName(adminUser.getRealName());
        consumable.setCreateTime(DateUtil.date());
        consumable.setUpdateBy(adminUser.getId());
        consumable.setUpdateName(adminUser.getRealName());
        consumable.setUpdateTime(DateUtil.date());
        consumable.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        consumableMapper.insert(consumable);
        // todo 发起流程
    }

    /**
     * 修改耗材
     *
     * @param form      修改入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void edit(ConsumableEditForm form, AdminUser adminUser) {
        // 校验数据
        Consumable consumable = checkCanEditOrDelete(form.getId());
        // 修改耗材
        consumable.setConsumableName(form.getConsumableName());
        consumable.setShortName(form.getShortName());
        consumable.setUpdateBy(adminUser.getId());
        consumable.setUpdateName(adminUser.getRealName());
        consumable.setUpdateTime(DateUtil.date());
        consumable.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        consumableMapper.updateById(consumable);
        // todo 发起流程
    }

    /**
     * 删除耗材
     *
     * @param form      删除入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void del(ConsumableSingleForm form, AdminUser adminUser) {
        // 校验数据
        Consumable consumable = checkCanEditOrDelete(form.getId());
        // 校验是否有规格数据
        LambdaQueryWrapper<ConsumableSpecification> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ConsumableSpecification::getConsumableId, form.getId());
        wrapper.eq(ConsumableSpecification::getIsEffect, IsEffectEnum.NORMAL.getCode());
        Long count = consumableSpecificationMapper.selectCount(wrapper);
        if (count > 0) {
            throw new BusinessException(PcsResultCode.CONSUMABLE_HAS_SPECIFICATIONS);
        }
        // 删除耗材
        consumable.setIsEffect(IsEffectEnum.DELETE.getCode());
        consumable.setUpdateBy(adminUser.getId());
        consumable.setUpdateName(adminUser.getRealName());
        consumable.setUpdateTime(DateUtil.date());
        consumable.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        consumableMapper.updateById(consumable);
        // todo 发起流程
    }

    /**
     * 启用耗材
     *
     * @param form      启用入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void enable(ConsumableSingleForm form, AdminUser adminUser) {
        // 更新状态
        updateStatus(form.getId(), EnableEnum.ENABLE, adminUser);
    }

    /**
     * 禁用耗材
     *
     * @param form      禁用入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void disable(ConsumableSingleForm form, AdminUser adminUser) {
        // 更新状态
        updateStatus(form.getId(), EnableEnum.DISABLE, adminUser);
    }

    /**
     * 分页查询耗材规格列表
     *
     * @param form 查询入参
     * @return 分页列表
     */
    @DS("slave_1")
    public IPage<ConsumableSpecificationVO> listPageSpecifications(ConsumableSpecificationListPageForm form) {
        LambdaQueryWrapper<ConsumableSpecification> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ConsumableSpecification::getConsumableId, form.getConsumableId());
        wrapper.eq(ConsumableSpecification::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.like(StrUtil.isNotBlank(form.getBrand()), ConsumableSpecification::getBrand, form.getBrand());
        wrapper.like(StrUtil.isNotBlank(form.getCatalogNumber()), ConsumableSpecification::getCatalogNumber, form.getCatalogNumber());
        wrapper.like(StrUtil.isNotBlank(form.getSpecification()), ConsumableSpecification::getSpecification, form.getSpecification());
        wrapper.like(StrUtil.isNotBlank(form.getUnit()), ConsumableSpecification::getUnit, form.getUnit());
        wrapper.like(StrUtil.isNotBlank(form.getStorageCondition()), ConsumableSpecification::getStorageCondition, form.getStorageCondition());
        wrapper.like(StrUtil.isNotBlank(form.getMaterial()), ConsumableSpecification::getMaterial, form.getMaterial());
        wrapper.orderByDesc(ConsumableSpecification::getCreateTime);
        Page<ConsumableSpecification> page = new Page<>(form.getPageIndex(), form.getPageSize());
        IPage<ConsumableSpecification> result = consumableSpecificationMapper.selectPage(page, wrapper);
        return ConvertUtil.convertPage(result, ConsumableSpecificationVO.class);
    }

    /**
     * 查询耗材规格列表
     *
     * @param form 查询入参
     * @return 规格列表
     */
    @DS("slave_1")
    public List<ConsumableSpecificationVO> listSpecifications(ConsumableSpecificationListForm form) {
        LambdaQueryWrapper<ConsumableSpecification> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ConsumableSpecification::getConsumableId, form.getConsumableId());
        wrapper.eq(ConsumableSpecification::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.like(StrUtil.isNotBlank(form.getBrand()), ConsumableSpecification::getBrand, form.getBrand());
        wrapper.like(StrUtil.isNotBlank(form.getCatalogNumber()), ConsumableSpecification::getCatalogNumber, form.getCatalogNumber());
        wrapper.like(StrUtil.isNotBlank(form.getSpecification()), ConsumableSpecification::getSpecification, form.getSpecification());
        wrapper.like(StrUtil.isNotBlank(form.getUnit()), ConsumableSpecification::getUnit, form.getUnit());
        wrapper.like(StrUtil.isNotBlank(form.getStorageCondition()), ConsumableSpecification::getStorageCondition, form.getStorageCondition());
        wrapper.like(StrUtil.isNotBlank(form.getMaterial()), ConsumableSpecification::getMaterial, form.getMaterial());
        wrapper.orderByDesc(ConsumableSpecification::getCreateTime);
        List<ConsumableSpecification> list = consumableSpecificationMapper.selectList(wrapper);
        return ConvertUtil.convertList(list, ConsumableSpecificationVO.class);
    }

    /**
     * 新增耗材规格
     *
     * @param form      新增入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void addSpecification(ConsumableSpecificationAddForm form, AdminUser adminUser) {
        // 校验耗材是否存在
        Consumable consumable = consumableMapper.selectById(form.getConsumableId());
        if (consumable == null || consumable.getIsEffect() != IsEffectEnum.NORMAL.getCode()) {
            throw new BusinessException(PcsResultCode.CONSUMABLE_NOT_EXIST);
        }
        // 校验字典数据
        validateDictData(form.getBrand(), DictDataTypeEnum.CONSUMABLE_BRAND, true, PcsResultCode.INVALID_CONSUMABLE_BRAND_DICT);
        validateDictData(form.getUnit(), DictDataTypeEnum.CONSUMABLE_UNIT, true, PcsResultCode.INVALID_CONSUMABLE_UNIT_DICT);
        validateDictData(form.getStorageCondition(), DictDataTypeEnum.CONSUMABLE_STORAGE_CONDITION, true, PcsResultCode.INVALID_CONSUMABLE_STORAGE_CONDITION_DICT);
        validateDictData(form.getMaterial(), DictDataTypeEnum.CONSUMABLE_MATERIAL, true, PcsResultCode.INVALID_CONSUMABLE_MATERIAL_DICT);
        // 新增规格
        ConsumableSpecification specification = new ConsumableSpecification();
        specification.setConsumableId(form.getConsumableId());
        specification.setBrand(form.getBrand());
        specification.setCatalogNumber(StrUtil.blankToDefault(form.getCatalogNumber(), StrUtil.EMPTY));
        specification.setSpecification(StrUtil.blankToDefault(form.getSpecification(), StrUtil.EMPTY));
        specification.setUnit(form.getUnit());
        specification.setStorageCondition(form.getStorageCondition());
        specification.setMaterial(form.getMaterial());
        specification.setIsEffect(IsEffectEnum.NORMAL.getCode());
        specification.setCreateBy(adminUser.getId());
        specification.setCreateName(adminUser.getRealName());
        specification.setCreateTime(DateUtil.date());
        specification.setUpdateBy(adminUser.getId());
        specification.setUpdateName(adminUser.getRealName());
        specification.setUpdateTime(DateUtil.date());
        consumableSpecificationMapper.insert(specification);
    }

    /**
     * 修改耗材规格
     *
     * @param form      修改入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void editSpecification(ConsumableSpecificationEditForm form, AdminUser adminUser) {
        // 校验规格是否存在
        ConsumableSpecification specification = consumableSpecificationMapper.selectById(form.getId());
        if (specification == null || specification.getIsEffect() != IsEffectEnum.NORMAL.getCode()) {
            throw new BusinessException(PcsResultCode.CONSUMABLE_SPECIFICATION_NOT_EXIST);
        }
        // 校验字典数据
        validateDictData(form.getBrand(), DictDataTypeEnum.CONSUMABLE_BRAND, StrUtil.equals(specification.getBrand(), form.getBrand()), PcsResultCode.INVALID_CONSUMABLE_BRAND_DICT);
        validateDictData(form.getUnit(), DictDataTypeEnum.CONSUMABLE_UNIT, StrUtil.equals(specification.getUnit(), form.getUnit()), PcsResultCode.INVALID_CONSUMABLE_UNIT_DICT);
        validateDictData(form.getStorageCondition(), DictDataTypeEnum.CONSUMABLE_STORAGE_CONDITION, StrUtil.equals(specification.getStorageCondition(), form.getStorageCondition()), PcsResultCode.INVALID_CONSUMABLE_STORAGE_CONDITION_DICT);
        validateDictData(form.getMaterial(), DictDataTypeEnum.CONSUMABLE_MATERIAL, StrUtil.equals(specification.getMaterial(), form.getMaterial()), PcsResultCode.INVALID_CONSUMABLE_MATERIAL_DICT);
        // 修改规格
        specification.setBrand(form.getBrand());
        specification.setCatalogNumber(StrUtil.blankToDefault(form.getCatalogNumber(), StrUtil.EMPTY));
        specification.setSpecification(StrUtil.blankToDefault(form.getSpecification(), StrUtil.EMPTY));
        specification.setUnit(form.getUnit());
        specification.setStorageCondition(form.getStorageCondition());
        specification.setMaterial(form.getMaterial());
        specification.setUpdateBy(adminUser.getId());
        specification.setUpdateName(adminUser.getRealName());
        specification.setUpdateTime(DateUtil.date());
        consumableSpecificationMapper.updateById(specification);
    }

    /**
     * 删除耗材规格
     *
     * @param form      删除入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void delSpecification(ConsumableSpecificationSingleForm form, AdminUser adminUser) {
        // 校验规格是否存在
        ConsumableSpecification specification = consumableSpecificationMapper.selectById(form.getId());
        if (specification == null || specification.getIsEffect() != IsEffectEnum.NORMAL.getCode()) {
            throw new BusinessException(PcsResultCode.CONSUMABLE_SPECIFICATION_NOT_EXIST);
        }
        // 删除规格
        specification.setIsEffect(IsEffectEnum.DELETE.getCode());
        specification.setUpdateBy(adminUser.getId());
        specification.setUpdateName(adminUser.getRealName());
        specification.setUpdateTime(DateUtil.date());
        consumableSpecificationMapper.updateById(specification);
    }

    // ================================================================== 私有方法 ================================================================== //

    /**
     * 校验耗材是否可以编辑或删除
     *
     * @param id 耗材ID
     * @return 耗材对象
     */
    private Consumable checkCanEditOrDelete(Long id) {
        Consumable consumable = consumableMapper.selectById(id);
        if (consumable == null || consumable.getIsEffect() != IsEffectEnum.NORMAL.getCode()) {
            throw new BusinessException(PcsResultCode.CONSUMABLE_NOT_EXIST);
        }
        if (ObjectUtil.notEqual(AuditStatusEnum.PASS.getCode(), consumable.getAuditStatus())) {
            throw new BusinessException(PcsResultCode.DATA_WAS_AUDITING_CANT_EDIT_OR_DELETE);
        }
        return consumable;
    }

    /**
     * 更新耗材状态
     *
     * @param id        耗材ID
     * @param status    状态
     * @param adminUser 当前用户
     */
    private void updateStatus(Long id, EnableEnum status, AdminUser adminUser) {
        Consumable consumable = consumableMapper.selectById(id);
        if (consumable == null || consumable.getIsEffect() != IsEffectEnum.NORMAL.getCode()) {
            throw new BusinessException(PcsResultCode.CONSUMABLE_NOT_EXIST);
        }
        consumable.setStatus(status.getCode());
        consumable.setUpdateBy(adminUser.getId());
        consumable.setUpdateName(adminUser.getRealName());
        consumable.setUpdateTime(DateUtil.date());
        consumableMapper.updateById(consumable);
    }

    /**
     * 校验字典数据是否有效
     *
     * @param dictValue 字典值
     * @param dictType  字典类型
     * @param errorCode 错误码
     */
    private void validateDictData(String dictValue, DictDataTypeEnum dictType, boolean isMustEnable, PcsResultCode errorCode) {
        if (StrUtil.isBlank(dictValue)) {
            return;
        }
        LambdaQueryWrapper<DictData> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(DictData::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.eq(isMustEnable, DictData::getStatus, EnableEnum.ENABLE.getCode());
        wrapper.eq(DictData::getDictValue, dictValue);
        wrapper.apply(isMustEnable, "parent_id IN (SELECT id FROM t_dict_data WHERE dict_value = '" + dictType.getCode() + "' AND is_effect = 0 AND status = 1)", "parent_id IN (SELECT id FROM t_dict_data WHERE dict_value = '" + dictType.getCode() + "' AND is_effect = 0)");
        Long count = dictDataMapper.selectCount(wrapper);
        if (count == 0) {
            throw new BusinessException(errorCode);
        }
    }
}
