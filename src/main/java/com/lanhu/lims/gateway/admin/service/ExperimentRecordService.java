package com.lanhu.lims.gateway.admin.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.idev.excel.write.metadata.style.WriteCellStyle;
import cn.idev.excel.write.metadata.style.WriteFont;
import cn.idev.excel.write.style.HorizontalCellStyleStrategy;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lanhu.lims.gateway.admin.core.BusinessException;
import com.lanhu.lims.gateway.admin.core.PcsResultCode;
import com.lanhu.lims.gateway.admin.enums.IsEffectEnum;
import com.lanhu.lims.gateway.admin.mapper.AdminUserMapper;
import com.lanhu.lims.gateway.admin.mapper.ExperimentRecordMapper;
import com.lanhu.lims.gateway.admin.mapper.FileRecordMapper;
import com.lanhu.lims.gateway.admin.model.AdminUser;
import com.lanhu.lims.gateway.admin.model.ExperimentRecord;
import com.lanhu.lims.gateway.admin.model.FileRecord;
import com.lanhu.lims.gateway.admin.utils.ConvertUtil;
import com.lanhu.lims.gateway.admin.utils.excel.ExcelUtil;
import com.lanhu.lims.gateway.admin.vo.req.ExperimentRecordListForm;
import com.lanhu.lims.gateway.admin.vo.req.ExperimentRecordSaveForm;
import com.lanhu.lims.gateway.admin.vo.req.ExperimentRecordSingleForm;
import com.lanhu.lims.gateway.admin.vo.resp.ExperimentRecordDetailVO;
import com.lanhu.lims.gateway.admin.vo.resp.ExperimentRecordListVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.temporal.WeekFields;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/********************************
 * @title ExperimentRecordService
 * @package com.lanhu.lims.gateway.admin.service
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/10 15:05
 * @version 0.0.1
 *********************************/
@Slf4j
@Service
public class ExperimentRecordService {
    @Resource
    private ExperimentRecordMapper experimentRecordMapper;

    @Resource
    private AdminUserMapper adminUserMapper;

    @Resource
    private FileRecordMapper fileRecordMapper;

    /**
     * 图片下载线程池
     */
    @Resource(name = "imageDownloadExecutor")
    private ThreadPoolTaskExecutor imageDownloadExecutor;

    /**
     * 实验记录列表
     *
     * @param form 查询入参
     * @return 实验记录列表
     */
    @DS("slave_1")
    public List<ExperimentRecordListVO> list(ExperimentRecordListForm form) {
        List<ExperimentRecord> list = CollUtil.newArrayList();
        // 获取当前用户
        AdminUser adminUser = adminUserMapper.selectById(form.getUserId());
        if (adminUser == null) {
            throw new BusinessException(PcsResultCode.PLA_USER_NOT_EXISTS);
        }
        // 获取周数Map
        LinkedHashMap<Integer, String> weekMap = getWeekNumberMap(form.getYear());
        // 查询实验记录
        LambdaQueryWrapper<ExperimentRecord> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ExperimentRecord::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.eq(ExperimentRecord::getUserId, form.getUserId());
        wrapper.eq(ExperimentRecord::getYear, form.getYear());
        wrapper.orderByDesc(ExperimentRecord::getWeekNumber);
        List<ExperimentRecord> records = experimentRecordMapper.selectList(wrapper);
        Map<Integer, ExperimentRecord> weekRecordMap = records.stream().collect(Collectors.toMap(ExperimentRecord::getWeekNumber, r -> r, (k1, k2) -> k1));
        // 遍历weekMap，将没有记录的周数补上
        String userName = CollUtil.isNotEmpty(records) ? records.get(0).getUserName() : adminUser.getRealName();
        for (Map.Entry<Integer, String> entry : weekMap.entrySet()) {
            ExperimentRecord experimentRecord = weekRecordMap.get(entry.getKey());
            if (experimentRecord == null) {
                experimentRecord = new ExperimentRecord();
                experimentRecord.setUserId(form.getUserId());
                experimentRecord.setUserName(userName);
                experimentRecord.setYear(form.getYear());
                experimentRecord.setWeekNumber(entry.getKey());
                experimentRecord.setTimePeriod(entry.getValue());
                experimentRecord.setExperimentContent(StrUtil.EMPTY);
                experimentRecord.setAttachmentUrl(StrUtil.EMPTY);
                experimentRecord.setAttachmentName(StrUtil.EMPTY);
            }
            list.add(experimentRecord);
        }
        if (form.getStatus() == 0) {
            list = list.stream().filter(r -> ObjectUtil.isNull(r.getId())).collect(Collectors.toList());
        } else if (form.getStatus() == 1) {
            list = list.stream().filter(r -> ObjectUtil.isNotNull(r.getId())).collect(Collectors.toList());
        }
        // 转换为VO列表
        return list.stream().map(entity -> {
            ExperimentRecordListVO vo = new ExperimentRecordListVO();
            BeanUtil.copyProperties(entity, vo);
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 实验记录详情
     *
     * @param form 查询入参
     * @return 实验记录详情
     */
    @DS("slave_1")
    public ExperimentRecordDetailVO detail(ExperimentRecordSingleForm form) {
        // 查询实验记录
        LambdaQueryWrapper<ExperimentRecord> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ExperimentRecord::getUserId, form.getUserId());
        wrapper.eq(ExperimentRecord::getYear, form.getYear());
        wrapper.eq(ExperimentRecord::getWeekNumber, form.getWeekNumber());
        wrapper.eq(ExperimentRecord::getIsEffect, IsEffectEnum.NORMAL.getCode());
        ExperimentRecord experimentRecord = experimentRecordMapper.selectOne(wrapper);
        if (experimentRecord == null) {
            experimentRecord = new ExperimentRecord();
            experimentRecord.setUserId(form.getUserId());
            experimentRecord.setYear(form.getYear());
            experimentRecord.setWeekNumber(form.getWeekNumber());
            experimentRecord.setTimePeriod(getWeekNumberMap(form.getYear()).get(form.getWeekNumber()));
            experimentRecord.setExperimentContent(StrUtil.EMPTY);
            experimentRecord.setAttachmentUrl(StrUtil.EMPTY);
            experimentRecord.setAttachmentName(StrUtil.EMPTY);
        }
        return ConvertUtil.convert(experimentRecord, ExperimentRecordDetailVO.class);
    }

    /**
     * 保存实验记录
     *
     * @param form      保存入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public synchronized void save(ExperimentRecordSaveForm form, AdminUser adminUser) {
        if (ObjectUtil.notEqual(form.getUserId(), adminUser.getId())) {
            throw new BusinessException(PcsResultCode.NO_PERMISSION);
        }
        // 查询文件记录
        FileRecord fileRecord = null;
        if (ObjectUtil.isNotNull(form.getFileRecordId())) {
            fileRecord = fileRecordMapper.selectById(form.getFileRecordId());
        }
        // 查询实验记录
        LambdaQueryWrapper<ExperimentRecord> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ExperimentRecord::getUserId, form.getUserId());
        wrapper.eq(ExperimentRecord::getYear, form.getYear());
        wrapper.eq(ExperimentRecord::getWeekNumber, form.getWeekNumber());
        wrapper.eq(ExperimentRecord::getIsEffect, IsEffectEnum.NORMAL.getCode());
        ExperimentRecord experimentRecord = experimentRecordMapper.selectOne(wrapper);
        if (experimentRecord == null) {
            experimentRecord = new ExperimentRecord();
            experimentRecord.setId(IdUtil.getSnowflakeNextId());
            experimentRecord.setUserId(adminUser.getId());
            experimentRecord.setUserName(adminUser.getRealName());
            experimentRecord.setYear(form.getYear());
            experimentRecord.setWeekNumber(form.getWeekNumber());
            experimentRecord.setTimePeriod(getWeekNumberMap(form.getYear()).get(form.getWeekNumber()));
            experimentRecord.setExperimentContent(form.getExperimentContent());
            experimentRecord.setAttachmentUrl(fileRecord != null ? fileRecord.getAttachmentUrl() : StrUtil.EMPTY);
            experimentRecord.setAttachmentName(fileRecord != null ? fileRecord.getAttachmentName() : StrUtil.EMPTY);
            experimentRecord.setIsEffect(IsEffectEnum.NORMAL.getCode());
            experimentRecord.setCreateBy(adminUser.getId());
            experimentRecord.setCreateName(adminUser.getRealName());
            experimentRecord.setCreateTime(DateUtil.date());
            experimentRecord.setUpdateBy(adminUser.getId());
            experimentRecord.setUpdateName(adminUser.getRealName());
            experimentRecord.setUpdateTime(experimentRecord.getCreateTime());
            experimentRecordMapper.insert(experimentRecord);
        } else {
            experimentRecord.setExperimentContent(form.getExperimentContent());
            experimentRecord.setAttachmentUrl(fileRecord != null ? fileRecord.getAttachmentUrl() : StrUtil.EMPTY);
            experimentRecord.setAttachmentName(fileRecord != null ? fileRecord.getAttachmentName() : StrUtil.EMPTY);
            experimentRecord.setUpdateBy(adminUser.getId());
            experimentRecord.setUpdateName(adminUser.getRealName());
            experimentRecord.setUpdateTime(DateUtil.date());
            experimentRecordMapper.updateById(experimentRecord);
        }
    }

    /**
     * 导出实验记录
     *
     * @param form 导出入参
     */
    @DS("master_1")
    public void export(ExperimentRecordListForm form, HttpServletResponse response) {
        List<ExperimentRecordListVO> voList = list(form);
        // 转换回ExperimentRecord用于导出
        List<ExperimentRecord> list = voList.stream().map(vo -> {
            ExperimentRecord entity = new ExperimentRecord();
            BeanUtil.copyProperties(vo, entity);
            return entity;
        }).collect(Collectors.toList());
        // 使用CompletableFuture异步下载所有图片
        List<CompletableFuture<Void>> downloadFutures = list.stream().filter(record -> StrUtil.isNotBlank(record.getAttachmentUrl())).map(record -> CompletableFuture.runAsync(() -> {
            try {
                byte[] imageBytes = HttpUtil.downloadBytes(record.getAttachmentUrl());
                record.setAttachment(imageBytes);
            } catch (Exception e) {
                // 如果下载失败，记录日志但不影响整体导出
                log.error("下载图片失败: {}, 错误: {}", record.getAttachmentUrl(), e.getMessage());
                record.setAttachment(null);
            }
        }, imageDownloadExecutor)).collect(Collectors.toList());
        // 等待所有图片下载完成
        try {
            CompletableFuture.allOf(downloadFutures.toArray(new CompletableFuture[0])).get(60, TimeUnit.SECONDS);
        } catch (Exception e) {
            // 如果等待超时或出现异常，记录日志但继续导出
            log.error("图片下载超时或出现异常: {}", e.getMessage());
        }
        list.stream().filter(record -> StrUtil.isBlank(record.getAttachmentUrl())).forEach(record -> record.setAttachment(null));
        // 导出 Excel
        String fileName;
        try {
            String userName = list.get(0).getUserName();
            Integer year = list.get(0).getYear();
            // 格式化文件名，包含用户名、年份和当前时间戳
            fileName = String.format("%s_%d_实验记录_%d.xlsx", userName, year, DateUtil.current());
        } catch (Exception e) {
            log.warn("生成文件名时发生异常，将使用默认文件名。错误信息: {}", e.getMessage());
            fileName = "实验记录_" + DateUtil.current() + ".xlsx";
        }
        ExcelUtil.exportToResponseWithStyle(response, list, ExperimentRecord.class, fileName, "实验记录", createCellStyle());
    }

    // =================================================================== 私有方法区 =================================================================== //

    /**
     * 获取年份下的所有周数
     *
     * @param year 年份
     * @return 周数Map
     */
    private LinkedHashMap<Integer, String> getWeekNumberMap(Integer year) {
        LinkedHashMap<Integer, String> weekMap = new LinkedHashMap<>();
        // 获取当前时间
        LocalDateTime now = LocalDateTimeUtil.now();
        // 获取当前年份
        int nowYear = now.getYear();
        if (year > nowYear) {
            throw new BusinessException(PcsResultCode.YEAR_CANT_BE_GREATER_THAN_CURRENT_YEAR);
        } else if (year == nowYear) {
            // 获取到这周的周日日期
            LocalDateTime sunday = now.with(WeekFields.ISO.dayOfWeek(), 7);
            // 再获取当前是第几周
            int nowWeek = now.get(WeekFields.ISO.weekOfWeekBasedYear());
            for (int i = nowWeek; i >= 1; i--) {
                // 获取这一周的周一日期
                LocalDateTime monday = sunday.with(WeekFields.ISO.dayOfWeek(), 1);
                // 获取这一周的周日日期
                sunday = monday.with(WeekFields.ISO.dayOfWeek(), 7);
                weekMap.put(i, LocalDateTimeUtil.format(monday, "yyyy-MM-dd") + " ~ " + LocalDateTimeUtil.format(sunday, "yyyy-MM-dd"));
                // 更新下一周的周一日期
                sunday = monday.minusDays(1);
            }
        } else {
            // 获取到这周的周日日期
            LocalDateTime sunday = LocalDateTime.of(year, 12, 31, 0, 0, 0);
            // 再获取当前是第几周
            int nowWeek = sunday.get(WeekFields.ISO.weekOfWeekBasedYear());
            sunday = nowWeek == 1 ? sunday.minusDays(7) : sunday;
            nowWeek = sunday.get(WeekFields.ISO.weekOfWeekBasedYear());
            for (int i = nowWeek; i >= 1; i--) {
                // 获取这一周的周一日期
                LocalDateTime monday = sunday.with(WeekFields.ISO.dayOfWeek(), 1);
                // 获取这一周的周日日期
                sunday = monday.with(WeekFields.ISO.dayOfWeek(), 7);
                weekMap.put(i, LocalDateTimeUtil.format(monday, "yyyy-MM-dd") + " ~ " + LocalDateTimeUtil.format(sunday, "yyyy-MM-dd"));
                // 更新下一周的周一日期
                sunday = monday.minusDays(1);
            }
        }
        return weekMap;
    }

    /**
     * 创建自定义单元格样式
     *
     * @return 样式策略
     */
    public static HorizontalCellStyleStrategy createCellStyle() {
        // 头部样式
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headWriteCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontName("宋体");
        headWriteFont.setFontHeightInPoints((short) 13);
        headWriteFont.setBold(true);
        headWriteCellStyle.setWriteFont(headWriteFont);
        headWriteCellStyle.setBorderTop(BorderStyle.THIN);
        headWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        headWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        headWriteCellStyle.setBorderRight(BorderStyle.THIN);

        // 内容样式
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        WriteFont contentWriteFont = new WriteFont();
        contentWriteFont.setFontName("宋体");
        contentWriteFont.setFontHeightInPoints((short) 11);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }
}
