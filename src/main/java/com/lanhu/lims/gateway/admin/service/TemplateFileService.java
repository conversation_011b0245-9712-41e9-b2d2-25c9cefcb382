package com.lanhu.lims.gateway.admin.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanhu.lims.gateway.admin.core.BusinessException;
import com.lanhu.lims.gateway.admin.core.PcsResultCode;
import com.lanhu.lims.gateway.admin.enums.AuditStatusEnum;
import com.lanhu.lims.gateway.admin.enums.DictDataTypeEnum;
import com.lanhu.lims.gateway.admin.enums.EnableEnum;
import com.lanhu.lims.gateway.admin.enums.IsEffectEnum;
import com.lanhu.lims.gateway.admin.mapper.DictDataMapper;
import com.lanhu.lims.gateway.admin.mapper.FileRecordMapper;
import com.lanhu.lims.gateway.admin.mapper.StaticDataAttachmentMapper;
import com.lanhu.lims.gateway.admin.mapper.TemplateFileMapper;
import com.lanhu.lims.gateway.admin.model.*;
import com.lanhu.lims.gateway.admin.utils.ConvertUtil;
import com.lanhu.lims.gateway.admin.utils.sequence.SequenceNameEnum;
import com.lanhu.lims.gateway.admin.utils.sequence.SequenceUtil;
import com.lanhu.lims.gateway.admin.vo.req.*;
import com.lanhu.lims.gateway.admin.vo.resp.StaticDataAttachmentVO;
import com.lanhu.lims.gateway.admin.vo.resp.TemplateFileDetailVO;
import com.lanhu.lims.gateway.admin.vo.resp.TemplateFileListVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/********************************
 * @title TemplateFileService
 * @package com.lanhu.lims.gateway.admin.service
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/14 16:50
 * @version 0.0.1
 *********************************/
@Service
public class TemplateFileService {
    @Resource
    private TemplateFileMapper templateFileMapper;

    @Resource
    private StaticDataAttachmentMapper staticDataAttachmentMapper;

    @Resource
    private FileRecordMapper fileRecordMapper;

    @Resource
    private DictDataMapper dictDataMapper;

    /**
     * 查询模板文件列表
     *
     * @param form 查询入参
     * @return 列表
     */
    @DS("slave_1")
    public List<TemplateFileListVO> list(TemplateFileListForm form) {
        LambdaQueryWrapper<TemplateFile> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(TemplateFile::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.eq(TemplateFile::getStatus, EnableEnum.ENABLE.getCode());
        wrapper.like(StrUtil.isNotBlank(form.getTemplateCode()), TemplateFile::getTemplateCode, form.getTemplateCode());
        wrapper.like(StrUtil.isNotBlank(form.getTemplateName()), TemplateFile::getTemplateName, form.getTemplateName());
        wrapper.eq(StrUtil.isNotBlank(form.getTemplateType()), TemplateFile::getTemplateType, form.getTemplateType());
        wrapper.ge(form.getEffectiveStartDate() != null, TemplateFile::getEffectiveDate, form.getEffectiveStartDate());
        wrapper.le(form.getEffectiveEndDate() != null, TemplateFile::getEffectiveDate, form.getEffectiveEndDate());
        wrapper.orderByDesc(TemplateFile::getCreateTime);
        List<TemplateFile> list = templateFileMapper.selectList(wrapper);
        // 转换为VO列表
        List<TemplateFileListVO> vos = BeanUtil.copyToList(list, TemplateFileListVO.class);
        vos.forEach(vo -> {
            // 设置模板类型名称
            if (StrUtil.isNotBlank(vo.getTemplateType())) {
                vo.setTemplateTypeName(getTemplateTypeName(vo.getTemplateType()));
            }
        });
        return vos;
    }

    /**
     * 分页查询模板文件
     *
     * @param form 查询入参
     * @return 分页列表
     */
    @DS("slave_1")
    public IPage<TemplateFileListVO> listPage(TemplateFileListPageForm form) {
        LambdaQueryWrapper<TemplateFile> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(TemplateFile::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.eq(form.getStatus() != null, TemplateFile::getStatus, form.getStatus());
        wrapper.like(StrUtil.isNotBlank(form.getTemplateCode()), TemplateFile::getTemplateCode, form.getTemplateCode());
        wrapper.like(StrUtil.isNotBlank(form.getTemplateName()), TemplateFile::getTemplateName, form.getTemplateName());
        wrapper.eq(StrUtil.isNotBlank(form.getTemplateType()), TemplateFile::getTemplateType, form.getTemplateType());
        wrapper.ge(form.getEffectiveStartDate() != null, TemplateFile::getEffectiveDate, form.getEffectiveStartDate());
        wrapper.le(form.getEffectiveEndDate() != null, TemplateFile::getEffectiveDate, form.getEffectiveEndDate());
        wrapper.orderByDesc(TemplateFile::getCreateTime);
        Page<TemplateFile> page = new Page<>(form.getPageIndex(), form.getPageSize());
        IPage<TemplateFile> result = templateFileMapper.selectPage(page, wrapper);
        // 使用ConvertUtil进行自定义转换
        return ConvertUtil.convertPage(result, entity -> {
            TemplateFileListVO vo = BeanUtil.copyProperties(entity, TemplateFileListVO.class);
            // 设置模板类型名称
            if (StrUtil.isNotBlank(vo.getTemplateType())) {
                vo.setTemplateTypeName(getTemplateTypeName(vo.getTemplateType()));
            }
            return vo;
        });
    }

    /**
     * 查询模板文件详情
     *
     * @param form 查询入参
     * @return 详情
     */
    @DS("slave_1")
    public TemplateFileDetailVO detail(TemplateFileSingleForm form) {
        TemplateFile templateFile = templateFileMapper.selectById(form.getId());
        if (templateFile == null || templateFile.getIsEffect() != IsEffectEnum.NORMAL.getCode()) {
            throw new BusinessException(PcsResultCode.TEMPLATE_FILE_NOT_EXIST);
        }
        // 查询附件
        LambdaQueryWrapper<StaticDataAttachment> attachmentWrapper = Wrappers.lambdaQuery();
        attachmentWrapper.eq(StaticDataAttachment::getDataId, templateFile.getId());
        attachmentWrapper.orderByDesc(StaticDataAttachment::getCreateTime);
        List<StaticDataAttachment> attachments = staticDataAttachmentMapper.selectList(attachmentWrapper);
        // 转换
        TemplateFileDetailVO vo = ConvertUtil.convert(templateFile, TemplateFileDetailVO.class);
        vo.setAttachments(ConvertUtil.convertList(attachments, StaticDataAttachmentVO.class));
        return vo;
    }

    /**
     * 新增模板文件
     *
     * @param form      新增入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void add(TemplateFileAddForm form, AdminUser adminUser) {
        // 查询文件记录
        FileRecord fileRecord = fileRecordMapper.selectById(form.getFileRecordId());
        if (fileRecord == null) {
            throw new BusinessException(PcsResultCode.FILE_RECORD_NOT_EXIST);
        }
        // 生成模板编号
        String templateCode = SequenceUtil.getNextFormattedValue(SequenceNameEnum.TEMPLATE_FILE_CODE);
        // 新增模板文件
        TemplateFile templateFile = new TemplateFile();
        templateFile.setTemplateCode(templateCode);
        templateFile.setTemplateName(form.getTemplateName());
        templateFile.setTemplateType(form.getTemplateType());
        templateFile.setVersion(form.getVersion());
        templateFile.setAttachmentUrl(fileRecord.getAttachmentUrl());
        templateFile.setRemark(StrUtil.blankToDefault(form.getRemark(), StrUtil.EMPTY));
        templateFile.setEffectiveDate(form.getEffectiveDate());
        templateFile.setStatus(EnableEnum.ENABLE.getCode());
        templateFile.setIsEffect(IsEffectEnum.NORMAL.getCode());
        templateFile.setCreateBy(adminUser.getId());
        templateFile.setCreateName(adminUser.getRealName());
        templateFile.setCreateTime(DateUtil.date());
        templateFile.setUpdateBy(adminUser.getId());
        templateFile.setUpdaterName(adminUser.getRealName());
        templateFile.setUpdateTime(templateFile.getCreateTime());
        templateFile.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        templateFile.setAuditName(StrUtil.EMPTY);
        templateFile.setAuditBy(null);
        templateFile.setAuditTime(null);
        templateFile.setAuditRemark(StrUtil.EMPTY);
        templateFileMapper.insert(templateFile);
        // 保存附件
        StaticDataAttachment attachment = saveAttachment(templateFile.getId(), templateFile.getTemplateCode(), templateFile.getEffectiveDate(), templateFile.getVersion(), fileRecord, adminUser);
        // todo 发起流程
    }

    /**
     * 修改模板文件
     *
     * @param form      修改入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void edit(TemplateFileEditForm form, AdminUser adminUser) {
        // 校验数据
        TemplateFile templateFile = checkCanEditOrDelete(form.getId());
        // 修改模板文件
        templateFile.setTemplateName(form.getTemplateName());
        templateFile.setTemplateType(form.getTemplateType());
        templateFile.setRemark(StrUtil.blankToDefault(form.getRemark(), StrUtil.EMPTY));
        templateFile.setUpdateBy(adminUser.getId());
        templateFile.setUpdaterName(adminUser.getRealName());
        templateFile.setUpdateTime(DateUtil.date());
        templateFile.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        templateFileMapper.updateById(templateFile);
        // todo 发起流程
    }

    /**
     * 删除模板文件
     *
     * @param form      删除入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void del(TemplateFileDelForm form, AdminUser adminUser) {
        // 校验数据
        TemplateFile templateFile = checkCanEditOrDelete(form.getId());
        // todo 校验是否有其他业务引用
        // 删除模板文件
        templateFile.setIsEffect(IsEffectEnum.DELETE.getCode());
        templateFile.setUpdateBy(adminUser.getId());
        templateFile.setUpdaterName(adminUser.getRealName());
        templateFile.setUpdateTime(DateUtil.date());
        templateFile.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        templateFileMapper.updateById(templateFile);
        // todo 发起流程
    }

    /**
     * 启用模板文件
     *
     * @param form      启用入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void enable(TemplateFileSingleForm form, AdminUser adminUser) {
        // 更新状态
        updateStatus(form.getId(), EnableEnum.ENABLE, adminUser);
    }

    /**
     * 禁用模板文件
     *
     * @param form      禁用入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void disable(TemplateFileSingleForm form, AdminUser adminUser) {
        // 更新状态
        updateStatus(form.getId(), EnableEnum.DISABLE, adminUser);
    }

    /**
     * 新增模板文件版本
     *
     * @param form      新增版本入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void addVersion(TemplateFileVersionAddForm form, AdminUser adminUser) {
        // 校验数据
        TemplateFile templateFile = templateFileMapper.selectById(form.getId());
        if (templateFile == null || templateFile.getIsEffect() != IsEffectEnum.NORMAL.getCode()) {
            throw new BusinessException(PcsResultCode.TEMPLATE_FILE_NOT_EXIST);
        }
        // 查询文件记录
        FileRecord fileRecord = fileRecordMapper.selectById(form.getFileRecordId());
        if (fileRecord == null) {
            throw new BusinessException(PcsResultCode.FILE_RECORD_NOT_EXIST);
        }
        // 新增模板文件版本
        TemplateFile newVersion = new TemplateFile();
        newVersion.setTemplateCode(templateFile.getTemplateCode());
        newVersion.setTemplateName(templateFile.getTemplateName());
        newVersion.setTemplateType(templateFile.getTemplateType());
        newVersion.setVersion(form.getVersion());
        newVersion.setAttachmentUrl(fileRecord.getAttachmentUrl());
        newVersion.setRemark(templateFile.getRemark());
        newVersion.setEffectiveDate(form.getEffectiveDate());
        newVersion.setStatus(EnableEnum.ENABLE.getCode());
        newVersion.setIsEffect(IsEffectEnum.NORMAL.getCode());
        newVersion.setCreateBy(adminUser.getId());
        newVersion.setCreateName(adminUser.getRealName());
        newVersion.setCreateTime(DateUtil.date());
        newVersion.setUpdateBy(adminUser.getId());
        newVersion.setUpdaterName(adminUser.getRealName());
        newVersion.setUpdateTime(newVersion.getCreateTime());
        newVersion.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        newVersion.setAuditName(StrUtil.EMPTY);
        newVersion.setAuditBy(null);
        newVersion.setAuditTime(null);
        newVersion.setAuditRemark(StrUtil.EMPTY);
        templateFileMapper.insert(newVersion);
        // 保存附件
        StaticDataAttachment attachment = saveAttachment(newVersion.getId(), newVersion.getTemplateCode(), newVersion.getEffectiveDate(), newVersion.getVersion(), fileRecord, adminUser);
        // todo 发起流程
    }

    // =================================================================== 工作流回调 =================================================================== //

    // =================================================================== 私有方法区 =================================================================== //

    /**
     * 校验模板文件是否允许修改或删除
     *
     * @param id 模板文件ID
     * @return 模板文件
     */
    private TemplateFile checkCanEditOrDelete(Long id) {
        TemplateFile templateFile = templateFileMapper.selectById(id);
        if (templateFile == null || ObjectUtil.notEqual(IsEffectEnum.NORMAL.getCode(), templateFile.getIsEffect())) {
            throw new BusinessException(PcsResultCode.TEMPLATE_FILE_NOT_EXIST);
        }
        if (ObjectUtil.notEqual(AuditStatusEnum.PASS.getCode(), templateFile.getAuditStatus())) {
            throw new BusinessException(PcsResultCode.DATA_WAS_AUDITING_CANT_EDIT_OR_DELETE);
        }
        return templateFile;
    }

    /**
     * 获取模板类型名称
     *
     * @param templateType 模板类型值
     * @return 模板类型名称
     */
    private String getTemplateTypeName(String templateType) {
        if (StrUtil.isBlank(templateType)) {
            return StrUtil.EMPTY;
        }
        // 通过关联查询一次性获取模板类型名称
        LambdaQueryWrapper<DictData> wrapper = Wrappers.lambdaQuery();
        wrapper.select(DictData::getDictLabel).eq(DictData::getDictValue, templateType).exists("SELECT 1 FROM dict_data parent WHERE parent.dict_value = '" + DictDataTypeEnum.TEMPLATE_FILE_TYPE.getCode() + "' AND parent.is_effect = " + IsEffectEnum.NORMAL.getCode() + " AND parent.status = " + EnableEnum.ENABLE.getCode() + " AND dict_data.parent_id = parent.id");
        DictData dictData = dictDataMapper.selectOne(wrapper);
        return dictData != null ? dictData.getDictLabel() : StrUtil.EMPTY;

    }

    /**
     * 保存附件
     *
     * @param templateFileId   模板文件id
     * @param templateFileCode 模板文件编号
     * @param effectiveDate    生效日期
     * @param version          版本
     * @param fileRecord       文件记录
     * @param adminUser        当前用户
     */
    private StaticDataAttachment saveAttachment(Long templateFileId, String templateFileCode, Date effectiveDate, String version, FileRecord fileRecord, AdminUser adminUser) {
        // 新增附件
        StaticDataAttachment staticDataAttachment = new StaticDataAttachment();
        staticDataAttachment.setId(IdUtil.getSnowflakeNextId());
        staticDataAttachment.setDataId(templateFileId);
        staticDataAttachment.setAttachmentType(StrUtil.EMPTY);
        staticDataAttachment.setAttachmentCode(templateFileCode);
        staticDataAttachment.setEffectiveDate(effectiveDate);
        staticDataAttachment.setVersion(version);
        staticDataAttachment.setFileId(fileRecord.getId());
        staticDataAttachment.setAttachmentUrl(fileRecord.getAttachmentUrl());
        staticDataAttachment.setAttachmentName(fileRecord.getAttachmentName());
        staticDataAttachment.setAttachmentSuffix(fileRecord.getAttachmentSuffix());
        staticDataAttachment.setAttachmentSize(fileRecord.getAttachmentSize());
        staticDataAttachment.setCreateBy(adminUser.getId());
        staticDataAttachment.setCreateName(adminUser.getRealName());
        staticDataAttachment.setCreateTime(DateUtil.date());
        staticDataAttachment.setUpdateBy(adminUser.getId());
        staticDataAttachment.setUpdateName(adminUser.getRealName());
        staticDataAttachment.setUpdateTime(staticDataAttachment.getCreateTime());
        staticDataAttachment.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        staticDataAttachment.setAuditName(StrUtil.EMPTY);
        staticDataAttachment.setAuditBy(null);
        staticDataAttachment.setAuditTime(null);
        staticDataAttachment.setAuditRemark(StrUtil.EMPTY);
        staticDataAttachmentMapper.insert(staticDataAttachment);
        return staticDataAttachment;
    }

    /**
     * 更新模板文件状态
     *
     * @param id         模板文件id
     * @param enableEnum 启用状态
     * @param adminUser  当前用户
     */
    private void updateStatus(Long id, EnableEnum enableEnum, AdminUser adminUser) {
        // 校验数据
        TemplateFile templateFile = templateFileMapper.selectById(id);
        if (templateFile == null || templateFile.getIsEffect() != IsEffectEnum.NORMAL.getCode()) {
            throw new BusinessException(PcsResultCode.TEMPLATE_FILE_NOT_EXIST);
        }
        templateFile.setStatus(enableEnum.getCode());
        templateFile.setUpdateBy(adminUser.getId());
        templateFile.setUpdaterName(adminUser.getRealName());
        templateFile.setUpdateTime(DateUtil.date());
        templateFile.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        templateFileMapper.updateById(templateFile);
    }
}
