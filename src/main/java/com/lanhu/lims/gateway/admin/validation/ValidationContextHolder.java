package com.lanhu.lims.gateway.admin.validation;

/********************************
 * @title ValidationContextHolder
 * @package com.lanhu.lims.gateway.admin.validation.dict
 * @description 验证上下文持有者，用于在验证过程中传递对象引用
 * <AUTHOR>
 * @date 2025-06-19
 * @version 1.0.0
 *********************************/
public class ValidationContextHolder {
    
    private static final ThreadLocal<Object> CURRENT_TARGET = new ThreadLocal<>();
    
    /**
     * 设置当前验证的目标对象
     */
    public static void setCurrentTarget(Object target) {
        CURRENT_TARGET.set(target);
    }
    
    /**
     * 获取当前验证的目标对象
     */
    public static Object getCurrentTarget() {
        return CURRENT_TARGET.get();
    }
    
    /**
     * 清除当前验证的目标对象
     */
    public static void clear() {
        CURRENT_TARGET.remove();
    }
}
