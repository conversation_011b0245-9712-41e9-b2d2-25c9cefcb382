package com.lanhu.lims.gateway.admin.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.lanhu.lims.gateway.admin.annotation.LhTransaction;
import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.core.PcsResultCode;
import com.lanhu.lims.gateway.admin.core.ProjectConstant;
import com.lanhu.lims.gateway.admin.core.Result;
import com.lanhu.lims.gateway.admin.enums.IsEffectEnum;
import com.lanhu.lims.gateway.admin.mapper.AdminUserRoleMapper;
import com.lanhu.lims.gateway.admin.mapper.RoleMapper;
import com.lanhu.lims.gateway.admin.mapper.RoleMenuMapper;
import com.lanhu.lims.gateway.admin.model.*;
import com.lanhu.lims.gateway.admin.vo.req.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/1/11 4:48 下午
 */

@Service
@Slf4j
public class RoleService {

    @Autowired
    private RoleMapper roleMapper;


    @Autowired
    private RoleMenuMapper roleMenuMapper;

    @Autowired
    private MenuService menuService;


    @Autowired
    private UserRoleService userRoleService;




    @Autowired
    private AdminUserRoleMapper adminUserRoleMapper;




    /**
    * @description: 角色添加
    * @param: [request]
    * @return: com.lanhu.imenu.gateway.pc.core.PcsResult
    * @author: liuyi
    * @date: 5:00 下午 2023/1/11
    */
    @DS("master_1")
    @LhTransaction
    public PcsResult add(AdminUser adminUser, RoleAddForm request){


        LambdaQueryWrapper<Role> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Role::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.eq(Role::getRoleName,request.getRoleName());



        //角色名称是否存在
        Role select = roleMapper.selectOne(wrapper);

        if(select != null){
            return Result.error(PcsResultCode.ROLE_NAME_EXISTS);
        }




        Role role = Role.builder()
                .createTime(new Date())
                .isEffect(IsEffectEnum.NORMAL.getCode())
                .createBy(adminUser.getId())
                .remark(request.getRoleDesc())
                .roleName(request.getRoleName())
                .updateTime(new Date())
                .updateBy(adminUser.getId())
                .roleId(IdUtil.getSnowflakeNextId())
                .build();



        roleMapper.insert(role);


        Set<Long> menuIdList = Sets.newHashSet();

        //去重
        if(CollectionUtil.isNotEmpty(request.getMenuIds())){
            menuIdList.addAll(request.getMenuIds());
        }


        List<RoleMenu> roleMenuList = Lists.newArrayList();

        if(CollectionUtil.isNotEmpty(menuIdList)){

            for (Long menuId : menuIdList) {
                RoleMenu roleMenu = RoleMenu.builder()
                        .menuId(menuId)
                        .roleId(role.getRoleId())
                        .build();

                roleMenuList.add(roleMenu);


            }
        }


        if(CollectionUtil.isNotEmpty(roleMenuList)){

            roleMenuMapper.batchInsert(roleMenuList);
        }





        return Result.ok();

    }



    /**
     * @description: 角色修改
     * @param: [request]
     * @return: com.lanhu.imenu.gateway.pc.core.PcsResult
     * @author: liuyi
     * @date: 5:00 下午 2023/1/11
     */
    @DS("master_1")
    @LhTransaction
    public PcsResult edit(AdminUser adminUser, RoleEditForm request){

        //如果是超级管理员，则不允许修改
        if(request.getRoleId().equals(ProjectConstant.SUPER_ADMIN_ROLE_ID)){
            return Result.error(PcsResultCode.NO_PERMISSION);
        }


        Role role = roleMapper.selectById(request.getRoleId());

        if(role == null){
            return Result.error(PcsResultCode.PLA_ROLE_NOT_EXISTS);
        }

        LambdaQueryWrapper<Role> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Role::getRoleName,request.getRoleName());
        wrapper.eq(Role::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.ne(Role::getRoleId,request.getRoleId());

        //角色名称是否存在
        Role select = roleMapper.selectOne(wrapper);

        if(select != null){
            return Result.error(PcsResultCode.ROLE_NAME_EXISTS);
        }



        Role update = Role.builder()
                .updateBy(adminUser.getId())
                .remark(request.getRoleDesc())
                .roleName(request.getRoleName())
                .updateTime(new Date())
                .roleId(role.getRoleId())
                .build();



        roleMapper.updateById(update);


        //删除所有角色菜单
        LambdaQueryWrapper<RoleMenu> deleteWrapper = Wrappers.lambdaQuery();
        deleteWrapper.eq(RoleMenu::getRoleId,request.getRoleId());
        roleMenuMapper.delete(deleteWrapper);




        Set<Long> menuIdList = Sets.newHashSet();

        //去重
        if(CollectionUtil.isNotEmpty(request.getMenuIds())){
            menuIdList.addAll(request.getMenuIds());
        }


        List<RoleMenu> roleMenuList = Lists.newArrayList();

        if(CollectionUtil.isNotEmpty(menuIdList)){

            for (Long menuId : menuIdList) {
                RoleMenu roleMenu = RoleMenu.builder()
                        .menuId(menuId)
                        .roleId(role.getRoleId())
                        .build();

                roleMenuList.add(roleMenu);


            }
        }


        if(CollectionUtil.isNotEmpty(roleMenuList)){

            roleMenuMapper.batchInsert(roleMenuList);
        }





        return Result.ok();

    }


    /**
     * @description: 角色启用
     * @param: [request]
     * @return: com.lanhu.imenu.gateway.pc.core.PcsResult
     * @author: liuyi
     * @date: 5:00 下午 2023/1/11
     */
    @DS("master_1")
    @LhTransaction
    public PcsResult enable(AdminUser adminUser, RoleEnableForm request){


        //如果是超级管理员，则不允许修改
        if(request.getRoleId().equals(ProjectConstant.SUPER_ADMIN_ROLE_ID)){
            return Result.error(PcsResultCode.NO_PERMISSION);
        }



        Role role = roleMapper.selectById(request.getRoleId());

        if(role == null){
            return Result.error(PcsResultCode.PLA_ROLE_NOT_EXISTS);
        }



        Role update = Role.builder()
                .updateBy(adminUser.getId())
                .updateTime(new Date())
                .roleId(request.getRoleId())
                .isEffect(request.getIsEffect())
                .build();



        roleMapper.updateById(update);



        return Result.ok();

    }


    /**
    * @description: 角色详情
    * @param: [request]
    * @return: com.lanhu.imenu.gateway.pc.core.PcsResult<com.lanhu.imenu.gateway.pc.model.pla.PlaPcRole>
    * @author: liuyi
    * @date: 10:29 上午 2023/1/16 
    */
    @DS("slave_1")
    public PcsResult<Role> detail(AdminUser adminUser, RoleDetailForm request){

        Role role = roleMapper.selectById(request.getRoleId());



        if(role != null){

            MenuTreeListForm form = new MenuTreeListForm();
            form.setRoleId(request.getRoleId());

            PcsResult<List<Menu>> r = menuService.treeList(adminUser,form);

            if(r.getCode() == PcsResultCode.SUCCESS.getCode()){
                role.setMenuList(r.getData());
            }

        }
        
        
        
        return  Result.ok(role);

    }



    /**
     * @description: 分页查询
     * @param: [plaPcUser, request]
     * @return: com.lanhu.imenu.gateway.pc.core.PcsResult
     * @author: liuyi
     * @date: 8:21 下午 2023/1/15
     */
    @DS("slave_1")
    public PcsResult<IPage<Role>> listPage(AdminUser adminUser, RoleListPageForm request) {



        LambdaQueryWrapper<Role> wrapper = Wrappers.lambdaQuery();


        if(StringUtils.isNotBlank(request.getRoleName())){
            wrapper.like(Role::getRoleName,request.getRoleName());
        }

        wrapper.eq(Role::getIsEffect,IsEffectEnum.NORMAL.getCode());


        // 默认根据addTime 倒序排列
        wrapper.orderByDesc(Role::getCreateTime);

        Page<Role> page = new Page<>(request.getPageIndex(), request.getPageSize());


        IPage<Role> pageList = roleMapper.selectPage(page, wrapper);



        return Result.ok(pageList);


    }




    /**
     * @description: 分页查询
     * @param: [plaPcUser, request]
     * @return: com.lanhu.imenu.gateway.pc.core.PcsResult
     * @author: liuyi
     * @date: 8:21 下午 2023/1/15
     */
    @DS("slave_1")
    public PcsResult<List<Role>> list(AdminUser adminUser, RoleListForm request) {

        List<Long> userRoleIdList = Lists.newArrayList();

        //如果当前用户不为空，则加载当前用户的角色列表
        if(request.getUserId() != null){
            List<AdminUserRole> userRoleList = Lists.newArrayList();
            LambdaQueryWrapper<AdminUserRole> wrapper0 = Wrappers.lambdaQuery();
            wrapper0.eq(AdminUserRole::getAdminUserId,request.getUserId());
            userRoleList = adminUserRoleMapper.selectList(wrapper0);

            if(CollectionUtil.isNotEmpty(userRoleList)){
                userRoleIdList = userRoleList.stream().map(u->u.getRoleId()).collect(Collectors.toList());
            }

        }


        LambdaQueryWrapper<Role> wrapper = Wrappers.lambdaQuery();


        wrapper.like(Role::getIsEffect, IsEffectEnum.NORMAL.getCode());


        // 默认根据addTime 倒序排列
        wrapper.orderByDesc(Role::getCreateTime);




        List<Role> roleList = roleMapper.selectList(wrapper);

        if(CollectionUtil.isNotEmpty(roleList)){

            for (Role plaPcRole : roleList) {

                if(userRoleIdList.contains(plaPcRole.getRoleId())){
                    plaPcRole.setChecked(true);
                }

            }

        }





        return Result.ok(roleList);

    }


    /**
     * @description: 角色启用
     * @param: [request]
     * @return: com.lanhu.imenu.gateway.pc.core.PcsResult
     * @author: liuyi
     * @date: 5:00 下午 2023/1/11
     */
    @DS("master_1")
    @LhTransaction
    public PcsResult batchDel(AdminUser adminUser, RoleBatchDelForm request){


       List<Long> roleIdList = Arrays.asList(request.getRoleIds().split(ProjectConstant.COMMA)).stream().map(r->Long.valueOf(r)).collect(Collectors.toList());

       List<Role> updateList = Lists.newArrayList();

        for (Long aLong : roleIdList) {

            //如果是超级管理员，则不允许修改
            if(aLong.equals(ProjectConstant.SUPER_ADMIN_ROLE_ID)){
                return Result.error(PcsResultCode.NO_PERMISSION);
            }

            Role update = Role.builder()
                    .updateBy(adminUser.getId())
                    .updateTime(new Date())
                    .roleId(aLong)
                    .isEffect(IsEffectEnum.DELETE.getCode())
                    .build();

            updateList.add(update);

        }



        roleMapper.updateBatchSelective(updateList);



        return Result.ok();

    }









}
