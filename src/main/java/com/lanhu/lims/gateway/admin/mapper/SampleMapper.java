package com.lanhu.lims.gateway.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanhu.lims.gateway.admin.model.Sample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: ${DESCRIPTION}
 * @author: huangzheng
 * @date: 2025/6/16 9:37
 */
@Mapper
public interface SampleMapper extends BaseMapper<Sample> {
    int updateBatch(@Param("list") List<Sample> list);

    int updateBatchSelective(@Param("list") List<Sample> list);

    int batchInsert(@Param("list") List<Sample> list);

    int batchInsertOrUpdate(@Param("list") List<Sample> list);

    int insertOnDuplicateUpdate(Sample record);

    int insertOnDuplicateUpdateSelective(Sample record);

//    int batchInsertOrUpdateSelective(@Param("list") List<Sample> list);
}