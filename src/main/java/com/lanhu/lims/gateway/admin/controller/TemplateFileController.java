package com.lanhu.lims.gateway.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanhu.lims.gateway.admin.annotation.Log;
import com.lanhu.lims.gateway.admin.auth.constants.SecurityConstants;
import com.lanhu.lims.gateway.admin.auth.context.SecurityContextHolder;
import com.lanhu.lims.gateway.admin.core.BaseController;
import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.core.Result;
import com.lanhu.lims.gateway.admin.enums.LogBusinessType;
import com.lanhu.lims.gateway.admin.enums.LogOperatorType;
import com.lanhu.lims.gateway.admin.model.LoginUser;
import com.lanhu.lims.gateway.admin.service.TemplateFileService;
import com.lanhu.lims.gateway.admin.vo.req.*;
import com.lanhu.lims.gateway.admin.vo.resp.TemplateFileDetailVO;
import com.lanhu.lims.gateway.admin.vo.resp.TemplateFileListVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/********************************
 * @title TemplateFileController
 * @package com.lanhu.lims.gateway.admin.controller
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/14 16:50
 * @version 0.0.1
 *********************************/
@RestController
@Api(tags = "模板文件相关接口", value = "模板文件相关接口")
public class TemplateFileController extends BaseController {
    @Resource
    private TemplateFileService templateFileService;

    /**
     * 查询模板文件列表
     */
    @ApiOperation(value = "查询模板文件列表")
    @PostMapping("/templateFile/v1/list")
    public PcsResult<List<TemplateFileListVO>> list(@Validated @RequestBody TemplateFileListForm form) {
        return Result.ok(templateFileService.list(form));
    }

    /**
     * 查询模板文件分页列表
     */
    @ApiOperation(value = "查询模板文件分页列表")
    @PostMapping("/templateFile/v1/listPage")
    public PcsResult<IPage<TemplateFileListVO>> listPage(@Validated @RequestBody TemplateFileListPageForm form) {
        return Result.ok(templateFileService.listPage(form));
    }

    /**
     * 查询模板文件详情
     */
    @ApiOperation(value = "查询模板文件详情")
    @PostMapping("/templateFile/v1/detail")
    public PcsResult<TemplateFileDetailVO> detail(@Validated @RequestBody TemplateFileSingleForm form) {
        return Result.ok(templateFileService.detail(form));
    }

    /**
     * 新增模板文件
     */
    @ApiOperation(value = "新增模板文件")
    @PostMapping("/templateFile/v1/add")
    @Log(title = "新增模板文件", businessType = LogBusinessType.INSERT, operatorType = LogOperatorType.PC)
    public PcsResult<Void> add(@Validated @RequestBody TemplateFileAddForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        templateFileService.add(form, loginUser.getSysUser());
        return Result.ok();
    }

    /**
     * 修改模板文件
     */
    @ApiOperation(value = "修改模板文件")
    @PostMapping("/templateFile/v1/edit")
    @Log(title = "修改模板文件", businessType = LogBusinessType.UPDATE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> edit(@Validated @RequestBody TemplateFileEditForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        templateFileService.edit(form, loginUser.getSysUser());
        return Result.ok();
    }

    /**
     * 删除模板文件
     */
    @ApiOperation(value = "删除模板文件")
    @PostMapping("/templateFile/v1/del")
    @Log(title = "删除模板文件", businessType = LogBusinessType.LOGICDELETE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> del(@Validated @RequestBody TemplateFileDelForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        templateFileService.del(form, loginUser.getSysUser());
        return Result.ok();
    }

    /**
     * 启用模板文件
     */
    @ApiOperation(value = "启用模板文件")
    @PostMapping("/templateFile/v1/enable")
    @Log(title = "启用模板文件", businessType = LogBusinessType.ENABLE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> enable(@Validated @RequestBody TemplateFileSingleForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        templateFileService.enable(form, loginUser.getSysUser());
        return Result.ok();
    }

    /**
     * 禁用模板文件
     */
    @ApiOperation(value = "禁用模板文件")
    @PostMapping("/templateFile/v1/disable")
    @Log(title = "禁用模板文件", businessType = LogBusinessType.DISABLE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> disable(@Validated @RequestBody TemplateFileSingleForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        templateFileService.disable(form, loginUser.getSysUser());
        return Result.ok();
    }

    /**
     * 新增模板文件版本
     */
    @ApiOperation(value = "新增模板文件版本")
    @PostMapping("/templateFile/v1/addVersion")
    @Log(title = "新增模板文件版本", businessType = LogBusinessType.INSERT, operatorType = LogOperatorType.PC)
    public PcsResult<Void> addVersion(@Validated @RequestBody TemplateFileVersionAddForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        templateFileService.addVersion(form, loginUser.getSysUser());
        return Result.ok();
    }
}
