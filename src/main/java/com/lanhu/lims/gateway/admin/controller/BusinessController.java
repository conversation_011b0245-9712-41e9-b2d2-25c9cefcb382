package com.lanhu.lims.gateway.admin.controller;

import com.lanhu.lims.gateway.admin.auth.constants.SecurityConstants;
import com.lanhu.lims.gateway.admin.auth.context.SecurityContextHolder;
import com.lanhu.lims.gateway.admin.core.BaseController;
import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.model.LoginUser;
import com.lanhu.lims.gateway.admin.service.BusinessService;
import com.lanhu.lims.gateway.admin.vo.req.BusinessRevokeForm;
import com.lanhu.lims.gateway.admin.vo.req.BusinessTerminationForm;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @description: 业务通用接口
 * @author: huangzheng
 * @date: 2025/5/8 16:19
 */

@RestController
@RequestMapping
@Slf4j
@Api(tags = "流程业务通用接口", value = "流程业务通用接口")
public class BusinessController extends BaseController {

    @Resource
    private BusinessService businessService;

    @PostMapping("/businessFlow/v1/revoke")
    @ApiOperation("撤销请假申请")
    public PcsResult revoke(@Validated @RequestBody BusinessRevokeForm businessRevokeForm) {

        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER,LoginUser.class);


        return businessService.revoke(businessRevokeForm.getBusinessId(),loginUser.getSysUser());

    }





    @PostMapping("/businessFlow/v1/termination")
    @ApiOperation("终止业务申请")
    public PcsResult termination(@Validated @RequestBody BusinessTerminationForm businessTerminationForm) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER,LoginUser.class);

        return businessService.termination(businessTerminationForm.getBusinessId(),loginUser.getSysUser());
    }










}
