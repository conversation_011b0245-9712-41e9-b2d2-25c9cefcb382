package com.lanhu.lims.gateway.admin.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanhu.lims.gateway.admin.core.BusinessException;
import com.lanhu.lims.gateway.admin.core.PcsResultCode;
import com.lanhu.lims.gateway.admin.enums.AuditStatusEnum;
import com.lanhu.lims.gateway.admin.enums.EnableEnum;
import com.lanhu.lims.gateway.admin.enums.IsEffectEnum;
import com.lanhu.lims.gateway.admin.mapper.*;
import com.lanhu.lims.gateway.admin.model.*;
import com.lanhu.lims.gateway.admin.utils.ConvertUtil;
import com.lanhu.lims.gateway.admin.utils.sequence.SequenceNameEnum;
import com.lanhu.lims.gateway.admin.utils.sequence.SequenceUtil;
import com.lanhu.lims.gateway.admin.vo.req.*;
import com.lanhu.lims.gateway.admin.vo.resp.DetectionMethodListVO;
import com.lanhu.lims.gateway.admin.vo.resp.DetectionProjectDetailVO;
import com.lanhu.lims.gateway.admin.vo.resp.DetectionProjectListVO;
import com.lanhu.lims.gateway.admin.vo.resp.StaticDataAttachmentVO;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/********************************
 * @title DetectionProjectService
 * @package com.lanhu.lims.gateway.admin.service
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/14 14:51
 * @version 0.0.1
 *********************************/
@Service
public class DetectionProjectService {
    @Resource
    private DetectionProjectMapper detectionProjectMapper;

    @Resource
    private DetectionMethodMapper detectionMethodMapper;

    @Resource
    private StaticDataAttachmentMapper staticDataAttachmentMapper;

    @Resource
    private FileRecordMapper fileRecordMapper;

    @Resource
    private EntrustOrderMapper entrustOrderMapper;

    @Resource(name = "commonExecutor")
    private ThreadPoolTaskExecutor commonExecutor;

    /**
     * 查询检测项目列表
     *
     * @param form 查询入参
     * @return 列表
     */
    @DS("slave_1")
    public List<DetectionProjectListVO> list(DetectionProjectListForm form) {
        LambdaQueryWrapper<DetectionProject> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(DetectionProject::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.eq(DetectionProject::getStatus, EnableEnum.ENABLE.getCode());
        wrapper.like(StrUtil.isNotBlank(form.getProjectCode()), DetectionProject::getProjectCode, form.getProjectCode());
        wrapper.like(StrUtil.isNotBlank(form.getProjectName()), DetectionProject::getProjectName, form.getProjectName());
        wrapper.orderByDesc(DetectionProject::getCreateTime);
        List<DetectionProject> list = detectionProjectMapper.selectList(wrapper);
        return convertList2VoList(list);
    }

    /**
     * 分页查询检测项目
     *
     * @param form 查询入参
     * @return 分页列表
     */
    @DS("slave_1")
    public IPage<DetectionProjectListVO> listPage(DetectionProjectListPageForm form) {
        LambdaQueryWrapper<DetectionProject> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(DetectionProject::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.eq(form.getStatus() != null, DetectionProject::getStatus, form.getStatus());
        wrapper.like(StrUtil.isNotBlank(form.getProjectCode()), DetectionProject::getProjectCode, form.getProjectCode());
        wrapper.like(StrUtil.isNotBlank(form.getProjectName()), DetectionProject::getProjectName, form.getProjectName());
        wrapper.orderByDesc(DetectionProject::getCreateTime);
        Page<DetectionProject> page = new Page<>(form.getPageIndex(), form.getPageSize());
        IPage<DetectionProject> result = detectionProjectMapper.selectPage(page, wrapper);
        return ConvertUtil.createPageWithRecords(convertList2VoList(result.getRecords()), result);
    }

    /**
     * 查询检测项目详情
     *
     * @param form 查询入参
     * @return 详情
     */
    @DS("slave_1")
    public DetectionProjectDetailVO detail(DetectionProjectSingleForm form) {
        DetectionProject detectionProject = detectionProjectMapper.selectById(form.getId());
        if (detectionProject == null || detectionProject.getIsEffect() != IsEffectEnum.NORMAL.getCode()) {
            throw new BusinessException(PcsResultCode.DETECTION_PROJECT_NOT_EXIST);
        }
        // 查询关联的检测方法并设置模板类型名称
        DetectionProjectDetailVO vo = ConvertUtil.convertWithPostProcess(detectionProject, DetectionProjectDetailVO.class, (source, target) -> {
            // 设置检测方法列表
            if (StrUtil.isNotBlank(source.getDetectionMethod())) {
                List<Long> ids = Arrays.stream(source.getDetectionMethod().split(",")).filter(StrUtil::isNotBlank).map(String::trim).map(Long::valueOf).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(ids)) {
                    LambdaQueryWrapper<DetectionMethod> wrapper = Wrappers.lambdaQuery();
                    wrapper.in(DetectionMethod::getId, ids);
                    List<DetectionMethod> detectionMethods = detectionMethodMapper.selectList(wrapper);
                    detectionMethods.sort(Comparator.comparingInt(o -> ids.indexOf(o.getId())));
                    target.setDetectionMethods(ConvertUtil.convertList(detectionMethods, DetectionMethodListVO.class));
                }
            }
        });
        // 查询附件
        List<StaticDataAttachmentVO> attachmentVos = ConvertUtil.convertList(staticDataAttachmentMapper.selectList(Wrappers.<StaticDataAttachment>lambdaQuery().eq(StaticDataAttachment::getDataId, detectionProject.getId())), StaticDataAttachmentVO.class);
        if (CollUtil.isNotEmpty(attachmentVos)) {
            // 处理附件其他信息
            int size = CollUtil.size(attachmentVos);
            Date now = DateUtil.date();
            for (int i = 0; i < size; i++) {
                StaticDataAttachmentVO attachmentVo = attachmentVos.get(i);
                attachmentVo.setExpirationDate(i == 0 ? null : attachmentVos.get(i - 1).getEffectiveDate());
                attachmentVo.setIsEffective(DateUtil.compare(attachmentVo.getEffectiveDate(), now) <= 0 && DateUtil.compare(now, attachmentVo.getExpirationDate()) < 0);
            }
        }
        vo.setAttachments(attachmentVos);
        return vo;
    }

    /**
     * 新增检测项目
     *
     * @param form      新增入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void add(DetectionProjectAddForm form, AdminUser adminUser) {
        FileRecord fileRecord = null;
        if (form.getFileRecordId() != null) {
            // 查询文件记录
            fileRecord = fileRecordMapper.selectById(form.getFileRecordId());
            if (fileRecord == null) {
                throw new BusinessException(PcsResultCode.FILE_RECORD_NOT_EXIST);
            }
        }
        // 校验检测方法
        validateDetectionMethods(form.getDetectionMethod());
        // 生成项目编号
        String projectCode = SequenceUtil.getNextFormattedValue(SequenceNameEnum.DETECTION_PROJECT_CODE);
        // 新增检测项目
        DetectionProject detectionProject = new DetectionProject();
        detectionProject.setProjectCode(projectCode);
        detectionProject.setProjectName(form.getProjectName());
        detectionProject.setDetectionMethod(CollUtil.isNotEmpty(form.getDetectionMethod()) ? StrUtil.join(",", form.getDetectionMethod()) : StrUtil.EMPTY);
        detectionProject.setGuidingPrice(form.getGuidingPrice());
        detectionProject.setAttachmentUrl(fileRecord != null ? fileRecord.getAttachmentUrl() : StrUtil.EMPTY);
        detectionProject.setRemarks(StrUtil.blankToDefault(form.getRemarks(), StrUtil.EMPTY));
        detectionProject.setStatus(EnableEnum.ENABLE.getCode());
        detectionProject.setIsEffect(IsEffectEnum.NORMAL.getCode());
        detectionProject.setCreateBy(adminUser.getId());
        detectionProject.setCreateName(adminUser.getRealName());
        detectionProject.setCreateTime(DateUtil.date());
        detectionProject.setUpdateBy(adminUser.getId());
        detectionProject.setUpdateName(adminUser.getRealName());
        detectionProject.setUpdateTime(detectionProject.getCreateTime());
        detectionProject.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        detectionProject.setAuditName(StrUtil.EMPTY);
        detectionProject.setAuditBy(null);
        detectionProject.setAuditTime(null);
        detectionProject.setAuditRemark(StrUtil.EMPTY);
        detectionProjectMapper.insert(detectionProject);
        if (fileRecord != null) {
            // 保存附件
            StaticDataAttachment attachment = saveAttachment(detectionProject.getId(), detectionProject.getProjectCode(), DateUtil.date(), fileRecord, adminUser);
        }
        // todo 发起流程
    }

    /**
     * 修改检测项目
     *
     * @param form      修改入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void edit(DetectionProjectEditForm form, AdminUser adminUser) {
        // 校验数据
        DetectionProject detectionProject = checkCanEditOrDelete(form.getId());
        // 校验检测方法
        validateDetectionMethods(form.getDetectionMethod());
        // 如果有新的文件记录，查询并更新附件
        if (form.getFileRecordId() != null) {
            FileRecord fileRecord = fileRecordMapper.selectById(form.getFileRecordId());
            if (fileRecord == null) {
                throw new BusinessException(PcsResultCode.FILE_RECORD_NOT_EXIST);
            }
            // 更新附件地址
            detectionProject.setAttachmentUrl(fileRecord.getAttachmentUrl());
            // 保存新附件
            StaticDataAttachment attachment = saveAttachment(detectionProject.getId(), detectionProject.getProjectCode(), DateUtil.date(), fileRecord, adminUser);
        }
        // 修改检测项目
        detectionProject.setProjectName(form.getProjectName());
        detectionProject.setDetectionMethod(CollUtil.isNotEmpty(form.getDetectionMethod()) ? StrUtil.join(",", form.getDetectionMethod()) : StrUtil.EMPTY);
        detectionProject.setGuidingPrice(form.getGuidingPrice());
        detectionProject.setRemarks(StrUtil.blankToDefault(form.getRemarks(), StrUtil.EMPTY));
        detectionProject.setUpdateBy(adminUser.getId());
        detectionProject.setUpdateName(adminUser.getRealName());
        detectionProject.setUpdateTime(DateUtil.date());
        detectionProject.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        detectionProjectMapper.updateById(detectionProject);
        // todo 发起流程
    }

    /**
     * 删除检测项目
     *
     * @param form      删除入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void del(DetectionProjectDelForm form, AdminUser adminUser) {
        // 校验数据
        DetectionProject detectionProject = checkCanEditOrDelete(form.getId());
        // 校验是否有委托单引用
        LambdaQueryWrapper<EntrustOrder> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EntrustOrder::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.apply("find_in_set(" + form.getId() + ", inspect_item)");
        Long count = entrustOrderMapper.selectCount(wrapper);
        if (count > 0) {
            throw new BusinessException(PcsResultCode.DETECTION_PROJECT_BIND_ENTRUST_ORDER);
        }
        // 删除检测项目
        detectionProject.setIsEffect(IsEffectEnum.DELETE.getCode());
        detectionProject.setUpdateBy(adminUser.getId());
        detectionProject.setUpdateName(adminUser.getRealName());
        detectionProject.setUpdateTime(DateUtil.date());
        detectionProject.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        detectionProjectMapper.updateById(detectionProject);
        // todo 发起流程
    }

    /**
     * 启用检测项目
     *
     * @param form      启用入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void enable(DetectionProjectSingleForm form, AdminUser adminUser) {
        // 更新状态
        updateStatus(form.getId(), EnableEnum.ENABLE, adminUser);
    }

    /**
     * 禁用检测项目
     *
     * @param form      禁用入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void disable(DetectionProjectSingleForm form, AdminUser adminUser) {
        // 更新状态
        updateStatus(form.getId(), EnableEnum.DISABLE, adminUser);
    }

    // =================================================================== 工作流回调 =================================================================== //

    // =================================================================== 私有方法区 =================================================================== //

    /**
     * 转换检测项目列表
     *
     * @param list 检测项目列表
     * @return 列表
     */
    private List<DetectionProjectListVO> convertList2VoList(List<DetectionProject> list) {
        List<DetectionProjectListVO> vos = ConvertUtil.convertList(list, DetectionProjectListVO.class);
        if (CollUtil.isNotEmpty(vos)) {
            // 异步加载检测方法
            CompletableFuture.allOf(vos.stream().map(vo -> CompletableFuture.runAsync(() -> {
                DetectionProject detectionProject = list.stream().filter(project -> project.getId().equals(vo.getId())).findFirst().orElse(null);
                // 设置检测方法列表
                List<DetectionMethod> detectionMethods = CollUtil.newArrayList();
                if (detectionProject != null && StrUtil.isNotBlank(detectionProject.getDetectionMethod())) {
                    List<Long> detectionMethodIds = Arrays.stream(detectionProject.getDetectionMethod().split(",")).filter(StrUtil::isNotBlank).map(String::trim).map(Long::valueOf).collect(Collectors.toList());
                    detectionMethods = detectionMethodMapper.selectList(Wrappers.<DetectionMethod>lambdaQuery().in(DetectionMethod::getId, detectionMethodIds).select(DetectionMethod::getMethodName));
                    detectionMethods.sort(Comparator.comparingInt(o -> detectionMethodIds.indexOf(o.getId())));
                }
                vo.setDetectionMethods(detectionMethods.stream().map(DetectionMethod::getMethodName).collect(Collectors.toList()));
            }, commonExecutor)).toArray(CompletableFuture[]::new)).join();
        }
        return vos;
    }

    /**
     * 校验检测项目是否允许修改或删除
     *
     * @param id 检测项目ID
     * @return 检测项目
     */
    private DetectionProject checkCanEditOrDelete(Long id) {
        DetectionProject detectionProject = detectionProjectMapper.selectById(id);
        if (detectionProject == null || ObjectUtil.notEqual(IsEffectEnum.NORMAL.getCode(), detectionProject.getIsEffect())) {
            throw new BusinessException(PcsResultCode.SYSTEM_FILE_NOT_EXIST);
        }
        if (ObjectUtil.notEqual(AuditStatusEnum.PASS.getCode(), detectionProject.getAuditStatus())) {
            throw new BusinessException(PcsResultCode.DATA_WAS_AUDITING_CANT_EDIT_OR_DELETE);
        }
        return detectionProject;
    }

    /**
     * 校验检测方法是否有效
     *
     * @param methodIds 检测方法ID列表
     */
    private void validateDetectionMethods(List<Long> methodIds) {
        if (CollUtil.isEmpty(methodIds)) {
            return;
        }
        // 去重
        methodIds = methodIds.stream().distinct().collect(Collectors.toList());
        // 查询所有需要校验的检测方法（已启用且未删除）
        LambdaQueryWrapper<DetectionMethod> wrapper = Wrappers.lambdaQuery();
        wrapper.in(DetectionMethod::getId, methodIds);
        wrapper.eq(DetectionMethod::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.eq(DetectionMethod::getStatus, EnableEnum.ENABLE.getCode());
        long count = detectionMethodMapper.selectCount(wrapper);
        // 检查是否所有ID都存在且有效
        if (count != methodIds.size()) {
            throw new BusinessException(PcsResultCode.DETECTION_METHOD_NOT_EXIST);
        }
    }

    /**
     * 解析检测方法ID字符串为检测方法列表
     *
     * @param detectionMethodIds 检测方法ID字符串，多个以逗号隔开
     * @return 检测方法列表
     */
    private List<DetectionMethod> parseDetectionMethods(String detectionMethodIds) {
        if (StrUtil.isBlank(detectionMethodIds)) {
            return CollUtil.newArrayList();
        }
        List<Long> ids = Arrays.stream(detectionMethodIds.split(",")).filter(StrUtil::isNotBlank).map(String::trim).map(Long::valueOf).collect(Collectors.toList());
        if (CollUtil.isEmpty(ids)) {
            return CollUtil.newArrayList();
        }
        LambdaQueryWrapper<DetectionMethod> wrapper = Wrappers.lambdaQuery();
        wrapper.in(DetectionMethod::getId, ids);
        return detectionMethodMapper.selectList(wrapper);
    }

    /**
     * 保存附件
     *
     * @param detectionProjectId   检测项目id
     * @param detectionProjectCode 检测项目编号
     * @param effectiveDate        生效日期
     * @param fileRecord           文件记录
     * @param adminUser            当前用户
     */
    private StaticDataAttachment saveAttachment(Long detectionProjectId, String detectionProjectCode, Date effectiveDate, FileRecord fileRecord, AdminUser adminUser) {
        // 新增附件
        StaticDataAttachment staticDataAttachment = new StaticDataAttachment();
        staticDataAttachment.setId(IdUtil.getSnowflakeNextId());
        staticDataAttachment.setDataId(detectionProjectId);
        staticDataAttachment.setAttachmentType(StrUtil.EMPTY);
        staticDataAttachment.setAttachmentCode(detectionProjectCode);
        staticDataAttachment.setEffectiveDate(effectiveDate);
        staticDataAttachment.setVersion(StrUtil.EMPTY);
        staticDataAttachment.setFileId(fileRecord.getId());
        staticDataAttachment.setAttachmentUrl(fileRecord.getAttachmentUrl());
        staticDataAttachment.setAttachmentName(fileRecord.getAttachmentName());
        staticDataAttachment.setAttachmentSuffix(fileRecord.getAttachmentSuffix());
        staticDataAttachment.setAttachmentSize(fileRecord.getAttachmentSize());
        staticDataAttachment.setCreateBy(adminUser.getId());
        staticDataAttachment.setCreateName(adminUser.getRealName());
        staticDataAttachment.setCreateTime(DateUtil.date());
        staticDataAttachment.setUpdateBy(adminUser.getId());
        staticDataAttachment.setUpdateName(adminUser.getRealName());
        staticDataAttachment.setUpdateTime(staticDataAttachment.getCreateTime());
        staticDataAttachment.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        staticDataAttachment.setAuditName(StrUtil.EMPTY);
        staticDataAttachment.setAuditBy(null);
        staticDataAttachment.setAuditTime(null);
        staticDataAttachment.setAuditRemark(StrUtil.EMPTY);
        staticDataAttachmentMapper.insert(staticDataAttachment);
        return staticDataAttachment;
    }

    /**
     * 更新检测项目状态
     *
     * @param id         检测项目id
     * @param enableEnum 启用状态
     * @param adminUser  当前用户
     */
    private void updateStatus(Long id, EnableEnum enableEnum, AdminUser adminUser) {
        // 校验数据
        DetectionProject detectionProject = detectionProjectMapper.selectById(id);
        if (detectionProject == null || detectionProject.getIsEffect() != IsEffectEnum.NORMAL.getCode()) {
            throw new BusinessException(PcsResultCode.DETECTION_PROJECT_NOT_EXIST);
        }
        detectionProject.setStatus(enableEnum.getCode());
        detectionProject.setUpdateBy(adminUser.getId());
        detectionProject.setUpdateName(adminUser.getRealName());
        detectionProject.setUpdateTime(DateUtil.date());
        detectionProject.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        detectionProjectMapper.updateById(detectionProject);
    }
}
