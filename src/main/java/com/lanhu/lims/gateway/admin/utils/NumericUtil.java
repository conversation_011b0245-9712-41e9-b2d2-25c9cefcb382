package com.lanhu.lims.gateway.admin.utils;

/**
 * @auth: huhouchun
 * @version: 1.0.0
 * @date: 2022/06/21 8:15
 */
public class NumericUtil {

    /**
     * 使用 ASCII 码判断字符串是否是数字
     *
     * @param str string 类型的字符串
     * @return <code>true</code> 该 string 类型的字符串是十进制正整数. 其他的会返回<code>false</code>
     */
    public static boolean isNumericByAscii(String str) {
        if (str == null || str.length() == 0) {
            return false;
        }
        return str.chars().allMatch(chr -> (chr >= 48 && chr <= 57));
    }

}
