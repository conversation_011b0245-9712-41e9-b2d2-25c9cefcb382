package com.lanhu.lims.gateway.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanhu.lims.gateway.admin.model.RoleMenu;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @description: ${DESCRIPTION}
 * @author: huangzheng
 * @date: 2025/4/29 15:16
 */
@Mapper
public interface RoleMenuMapper extends BaseMapper<RoleMenu> {
    int batchInsert(@Param("list") List<RoleMenu> list);

    int batchInsertOrUpdate(@Param("list") List<RoleMenu> list);

    int insertOnDuplicateUpdate(RoleMenu record);

    int insertOnDuplicateUpdateSelective(RoleMenu record);

    List<RoleMenu> selectListByRoleId(@Param("roleId") Long roleId);
}