package com.lanhu.lims.gateway.admin.utils.excel;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;

/********************************
 * @title ExcelValidationHandler
 * @package com.lanhu.lims.gateway.admin.utils.excel
 * @description Excel数据验证处理工具类，提供数据校验、格式验证、错误收集等功能
 *
 * <AUTHOR>
 * @date 2025/1/27 10:45
 * @version 0.0.1
 *********************************/
@Slf4j
public class ExcelValidationHandler {
    
    /**
     * 验证结果收集器
     */
    private final List<ValidationError> errors = new ArrayList<>();
    
    /**
     * 验证数据列表
     *
     * @param dataList 数据列表
     * @param validationRules 验证规则列表
     * @return 验证结果
     */
    public <T> ValidationResult<T> validateDataList(List<T> dataList, List<ValidationRule<T>> validationRules) {
        errors.clear();
        List<T> validData = new ArrayList<>();
        List<T> invalidData = new ArrayList<>();
        
        if (CollUtil.isEmpty(dataList)) {
            return new ValidationResult<>(validData, invalidData, errors);
        }
        
        for (int i = 0; i < dataList.size(); i++) {
            T data = dataList.get(i);
            boolean isValid = true;
            
            for (ValidationRule<T> rule : validationRules) {
                if (!rule.validate(data)) {
                    isValid = false;
                    errors.add(new ValidationError(i + 2, rule.getFieldName(), rule.getErrorMessage(), data));
                }
            }
            
            if (isValid) {
                validData.add(data);
            } else {
                invalidData.add(data);
            }
        }
        
        return new ValidationResult<>(validData, invalidData, new ArrayList<>(errors));
    }
    
    /**
     * 创建非空验证规则
     *
     * @param fieldName 字段名称
     * @param getter 字段获取器
     * @return 验证规则
     */
    public static <T> ValidationRule<T> notNull(String fieldName, Function<T, Object> getter) {
        return new ValidationRule<>(fieldName, data -> ObjectUtil.isNotNull(getter.apply(data)), fieldName + "不能为空");
    }
    
    /**
     * 创建非空字符串验证规则
     *
     * @param fieldName 字段名称
     * @param getter 字段获取器
     * @return 验证规则
     */
    public static <T> ValidationRule<T> notBlank(String fieldName, Function<T, String> getter) {
        return new ValidationRule<>(fieldName, data -> StrUtil.isNotBlank(getter.apply(data)), fieldName + "不能为空");
    }
    
    /**
     * 创建长度验证规则
     *
     * @param fieldName 字段名称
     * @param getter 字段获取器
     * @param minLength 最小长度
     * @param maxLength 最大长度
     * @return 验证规则
     */
    public static <T> ValidationRule<T> length(String fieldName, Function<T, String> getter, int minLength, int maxLength) {
        return new ValidationRule<>(fieldName, data -> {
            String value = getter.apply(data);
            if (value == null) {
                return true;
            }
            int length = value.length();
            return length >= minLength && length <= maxLength;
        }, String.format("%s长度必须在%d-%d之间", fieldName, minLength, maxLength));
    }
    
    /**
     * 创建数字范围验证规则
     *
     * @param fieldName 字段名称
     * @param getter 字段获取器
     * @param min 最小值
     * @param max 最大值
     * @return 验证规则
     */
    public static <T> ValidationRule<T> numberRange(String fieldName, Function<T, Number> getter, Number min, Number max) {
        return new ValidationRule<>(fieldName, data -> {
            Number value = getter.apply(data);
            if (value == null) {
                return true;
            }
            BigDecimal decimal = new BigDecimal(value.toString());
            BigDecimal minDecimal = new BigDecimal(min.toString());
            BigDecimal maxDecimal = new BigDecimal(max.toString());
            return decimal.compareTo(minDecimal) >= 0 && decimal.compareTo(maxDecimal) <= 0;
        }, String.format("%s必须在%s-%s之间", fieldName, min, max));
    }
    
    /**
     * 创建正数验证规则
     *
     * @param fieldName 字段名称
     * @param getter 字段获取器
     * @return 验证规则
     */
    public static <T> ValidationRule<T> positive(String fieldName, Function<T, Number> getter) {
        return new ValidationRule<>(fieldName, data -> {
            Number value = getter.apply(data);
            if (value == null) {
                return true;
            }
            return NumberUtil.isGreater(new BigDecimal(value.toString()), BigDecimal.ZERO);
        }, fieldName + "必须为正数");
    }
    
    /**
     * 创建邮箱验证规则
     *
     * @param fieldName 字段名称
     * @param getter 字段获取器
     * @return 验证规则
     */
    public static <T> ValidationRule<T> email(String fieldName, Function<T, String> getter) {
        String emailRegex = "^[A-Za-z0-9+_.-]+@([A-Za-z0-9.-]+\\.[A-Za-z]{2,})$";
        return new ValidationRule<>(fieldName, data -> {
            String value = getter.apply(data);
            if (StrUtil.isBlank(value)) {
                return true;
            }
            return ReUtil.isMatch(emailRegex, value);
        }, fieldName + "格式不正确");
    }
    
    /**
     * 创建手机号验证规则
     *
     * @param fieldName 字段名称
     * @param getter 字段获取器
     * @return 验证规则
     */
    public static <T> ValidationRule<T> mobile(String fieldName, Function<T, String> getter) {
        String mobileRegex = "^1[3-9]\\d{9}$";
        return new ValidationRule<>(fieldName, data -> {
            String value = getter.apply(data);
            if (StrUtil.isBlank(value)) {
                return true;
            }
            return ReUtil.isMatch(mobileRegex, value);
        }, fieldName + "格式不正确");
    }
    
    /**
     * 创建身份证验证规则
     *
     * @param fieldName 字段名称
     * @param getter 字段获取器
     * @return 验证规则
     */
    public static <T> ValidationRule<T> idCard(String fieldName, Function<T, String> getter) {
        String idCardRegex = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$";
        return new ValidationRule<>(fieldName, data -> {
            String value = getter.apply(data);
            if (StrUtil.isBlank(value)) {
                return true;
            }
            return ReUtil.isMatch(idCardRegex, value);
        }, fieldName + "格式不正确");
    }
    
    /**
     * 创建日期格式验证规则
     *
     * @param fieldName 字段名称
     * @param getter 字段获取器
     * @param pattern 日期格式
     * @return 验证规则
     */
    public static <T> ValidationRule<T> dateFormat(String fieldName, Function<T, String> getter, String pattern) {
        return new ValidationRule<>(fieldName, data -> {
            String value = getter.apply(data);
            if (StrUtil.isBlank(value)) {
                return true;
            }
            try {
                DateUtil.parse(value, pattern);
                return true;
            } catch (Exception e) {
                return false;
            }
        }, String.format("%s日期格式不正确，应为%s", fieldName, pattern));
    }
    
    /**
     * 创建枚举值验证规则
     *
     * @param fieldName 字段名称
     * @param getter 字段获取器
     * @param allowedValues 允许的值集合
     * @return 验证规则
     */
    public static <T, V> ValidationRule<T> enumValue(String fieldName, Function<T, V> getter, Set<V> allowedValues) {
        return new ValidationRule<>(fieldName, data -> {
            V value = getter.apply(data);
            if (value == null) {
                return true;
            }
            return allowedValues.contains(value);
        }, String.format("%s值不在允许范围内：%s", fieldName, allowedValues));
    }
    
    /**
     * 创建自定义验证规则
     *
     * @param fieldName 字段名称
     * @param predicate 验证谓词
     * @param errorMessage 错误消息
     * @return 验证规则
     */
    public static <T> ValidationRule<T> custom(String fieldName, Predicate<T> predicate, String errorMessage) {
        return new ValidationRule<>(fieldName, predicate, errorMessage);
    }
    
    /**
     * 验证规则类
     */
    @Getter
    public static class ValidationRule<T> {
        private final String fieldName;
        private final Predicate<T> validator;
        private final String errorMessage;
        
        public ValidationRule(String fieldName, Predicate<T> validator, String errorMessage) {
            this.fieldName = fieldName;
            this.validator = validator;
            this.errorMessage = errorMessage;
        }
        
        public boolean validate(T data) {
            return validator.test(data);
        }
    }
    
    /**
     * 验证错误类
     */
    @Getter
    public static class ValidationError {
        private final int rowIndex;
        private final String fieldName;
        private final String errorMessage;
        private final Object data;
        
        public ValidationError(int rowIndex, String fieldName, String errorMessage, Object data) {
            this.rowIndex = rowIndex;
            this.fieldName = fieldName;
            this.errorMessage = errorMessage;
            this.data = data;
        }

        @Override
        public String toString() {
            return String.format("第%d行，字段[%s]：%s", rowIndex, fieldName, errorMessage);
        }
    }
    
    /**
     * 验证结果类
     */
    @Getter
    public static class ValidationResult<T> {
        private final List<T> validData;
        private final List<T> invalidData;
        private final List<ValidationError> errors;
        
        public ValidationResult(List<T> validData, List<T> invalidData, List<ValidationError> errors) {
            this.validData = validData;
            this.invalidData = invalidData;
            this.errors = errors;
        }
        
        public boolean isValid() {
            return CollUtil.isEmpty(errors);
        }

        public String getErrorSummary() {
            if (CollUtil.isEmpty(errors)) {
                return "验证通过";
            }
            StringBuilder sb = new StringBuilder();
            sb.append(String.format("共发现%d个错误：\n", errors.size()));
            for (ValidationError error : errors) {
                sb.append(error.toString()).append("\n");
            }
            return sb.toString();
        }
    }
}
