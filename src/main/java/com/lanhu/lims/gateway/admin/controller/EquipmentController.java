package com.lanhu.lims.gateway.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanhu.lims.gateway.admin.annotation.Log;
import com.lanhu.lims.gateway.admin.auth.constants.SecurityConstants;
import com.lanhu.lims.gateway.admin.auth.context.SecurityContextHolder;
import com.lanhu.lims.gateway.admin.core.BaseController;
import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.core.Result;
import com.lanhu.lims.gateway.admin.enums.LogBusinessType;
import com.lanhu.lims.gateway.admin.enums.LogOperatorType;
import com.lanhu.lims.gateway.admin.model.LoginUser;
import com.lanhu.lims.gateway.admin.service.EquipmentService;
import com.lanhu.lims.gateway.admin.vo.req.*;
import com.lanhu.lims.gateway.admin.vo.resp.EquipmentDetailVO;
import com.lanhu.lims.gateway.admin.vo.resp.EquipmentListVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/********************************
 * @title EquipmentController
 * @package com.lanhu.lims.gateway.admin.controller
 * @description 仪器设备相关接口
 *
 * <AUTHOR>
 * @date 2025/6/15 10:30
 * @version 0.0.1
 *********************************/
@RestController
@Api(tags = "仪器设备相关接口", value = "仪器设备相关接口")
public class EquipmentController extends BaseController {
    @Resource
    private EquipmentService equipmentService;

    /**
     * 查询仪器设备列表
     */
    @ApiOperation(value = "查询仪器设备列表")
    @PostMapping("/equipment/v1/list")
    public PcsResult<List<EquipmentListVO>> list(@Validated @RequestBody EquipmentListForm form) {
        return Result.ok(equipmentService.list(form));
    }

    /**
     * 查询仪器设备分页列表
     */
    @ApiOperation(value = "查询仪器设备分页列表")
    @PostMapping("/equipment/v1/listPage")
    public PcsResult<IPage<EquipmentListVO>> listPage(@Validated @RequestBody EquipmentListPageForm form) {
        return Result.ok(equipmentService.listPage(form));
    }

    /**
     * 查询仪器设备详情
     */
    @ApiOperation(value = "查询仪器设备详情")
    @PostMapping("/equipment/v1/detail")
    public PcsResult<EquipmentDetailVO> detail(@Validated @RequestBody EquipmentSingleForm form) {
        return Result.ok(equipmentService.detail(form));
    }

    /**
     * 新增仪器设备
     */
    @ApiOperation(value = "新增仪器设备")
    @PostMapping("/equipment/v1/add")
    @Log(title = "新增仪器设备", businessType = LogBusinessType.INSERT, operatorType = LogOperatorType.PC)
    public PcsResult<Void> add(@Validated @RequestBody EquipmentAddForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        equipmentService.add(form, loginUser.getSysUser());
        return Result.ok();
    }

    /**
     * 修改仪器设备
     */
    @ApiOperation(value = "修改仪器设备")
    @PostMapping("/equipment/v1/edit")
    @Log(title = "修改仪器设备", businessType = LogBusinessType.UPDATE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> edit(@Validated @RequestBody EquipmentEditForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        equipmentService.edit(form, loginUser.getSysUser());
        return Result.ok();
    }

    /**
     * 删除仪器设备
     */
    @ApiOperation(value = "删除仪器设备")
    @PostMapping("/equipment/v1/del")
    @Log(title = "删除仪器设备", businessType = LogBusinessType.LOGICDELETE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> del(@Validated @RequestBody EquipmentDelForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        equipmentService.del(form, loginUser.getSysUser());
        return Result.ok();
    }

    /**
     * 设备维护
     */
    @ApiOperation(value = "设备维护")
    @PostMapping("/equipment/v1/maintenance")
    @Log(title = "设备维护", businessType = LogBusinessType.INSERT, operatorType = LogOperatorType.PC)
    public PcsResult<Void> maintenance(@Validated @RequestBody EquipmentMaintenanceForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        equipmentService.maintenance(form, loginUser.getSysUser());
        return Result.ok();
    }
}
