package com.lanhu.lims.gateway.admin.utils.change;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/********************************
 * @title ValueMapping
 * @package com.lanhu.lims.gateway.admin.utils.change
 * @description 值映射注解，用于定义字段值与显示文本的映射关系
 *
 * <AUTHOR>
 * @date 2025/6/8 16:36
 * @version 1.0.0
 *********************************/
@Target(ElementType.ANNOTATION_TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface ValueMapping {
    
    /**
     * 原始值（支持字符串表示的各种类型值）
     */
    String value();
    
    /**
     * 显示文本
     */
    String text();
}
