package com.lanhu.lims.gateway.admin.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.lanhu.lims.gateway.admin.annotation.LhTransaction;
import com.lanhu.lims.gateway.admin.auth.service.TokenService;
import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.core.PcsResultCode;
import com.lanhu.lims.gateway.admin.core.Result;
import com.lanhu.lims.gateway.admin.enums.IsEffectEnum;
import com.lanhu.lims.gateway.admin.enums.MenuLevelEnum;
import com.lanhu.lims.gateway.admin.enums.MenuVisibleEnum;
import com.lanhu.lims.gateway.admin.mapper.MenuMapper;
import com.lanhu.lims.gateway.admin.mapper.RoleMenuMapper;
import com.lanhu.lims.gateway.admin.model.AdminUser;
import com.lanhu.lims.gateway.admin.model.LoginUser;
import com.lanhu.lims.gateway.admin.model.Menu;
import com.lanhu.lims.gateway.admin.model.RoleMenu;
import com.lanhu.lims.gateway.admin.vo.req.MenuAddForm;
import com.lanhu.lims.gateway.admin.vo.req.MenuDelForm;
import com.lanhu.lims.gateway.admin.vo.req.MenuEditForm;
import com.lanhu.lims.gateway.admin.vo.req.MenuTreeListForm;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/5/21 12:15 下午
 */
@Service
@Slf4j
public class MenuService {

    @Autowired
    private RoleMenuMapper roleMenuMapper;

    @Autowired
    private UserRoleService userRoleService;

    @Autowired
    private MenuMapper menuMapper;


    @Autowired
    private TokenService tokenService;



    /**
     * @description: 属性菜单列表
     * @param: []
     * @return: com.lanhu.imenu.gateway.pc.core.PcsResult
     * @author: liuyi
     * @date: 9:25 上午 2023/1/16
     */
    @DS("slave_1")
    public PcsResult<List<Menu>> treeList(AdminUser adminUser, MenuTreeListForm request ){


        List<Menu> retList = Lists.newArrayList();

        List<Long> checkedMenuIdList = Lists.newArrayList();


        //查看当前角色已选中菜单
        if(request.getRoleId() != null){
            List<RoleMenu> roleMenuList = roleMenuMapper.selectListByRoleId(request.getRoleId());

            if(CollectionUtil.isNotEmpty(roleMenuList)){
                checkedMenuIdList = roleMenuList.stream().map(r->r.getMenuId()).collect(Collectors.toList());
            }
        }

        //加载权限菜单列表形成树形结构
        List<Menu> menuList = Lists.newArrayList();

        //是否是超级管理员
        boolean isSuperAdmin =  userRoleService.isSuperAdmin(adminUser);

        //如果超级管理员
        if(isSuperAdmin){
            menuList = menuMapper.selectMenuList();
        }else {
            menuList =  menuMapper.selectMenuListByUserId(adminUser.getId());
        }

        //菜单翻译
        if(CollectionUtil.isNotEmpty(menuList)){
            for (Menu menu : menuList) {
                tokenService.menuNameTranslate(menu);
            }
        }


        if(CollectionUtil.isNotEmpty(menuList)){


            List<Menu> menuList1 = menuList.stream().filter(m->(m.getLevel() == MenuLevelEnum.LEVEL_1.getCode() && m.getParentId().equals(0L))).sorted(Comparator.comparing(Menu::getOrd)).collect(Collectors.toList());

            //一级菜单
            if(CollectionUtil.isNotEmpty(menuList1)){


                for (Menu menu1 : menuList1) {

                    if(checkedMenuIdList.contains(menu1.getId())){
                        menu1.setChecked(true);
                    }


                    List<Menu> menuList2 = menuList.stream().filter(m->(m.getLevel() == MenuLevelEnum.LEVEL_2.getCode() && m.getParentId().equals(menu1.getId()))).sorted(Comparator.comparing(Menu::getOrd)).collect(Collectors.toList());


                    if(CollectionUtil.isNotEmpty(menuList2)){


                        for (Menu menu2 : menuList2) {

                            if(checkedMenuIdList.contains(menu2.getId())){
                                menu2.setChecked(true);
                            }



                            List<Menu> menuList3 = menuList.stream().filter(m->(m.getLevel() == MenuLevelEnum.LEVEL_3.getCode() && m.getParentId().equals(menu2.getId()))).sorted(Comparator.comparing(Menu::getOrd)).collect(Collectors.toList());


                            if(CollectionUtil.isNotEmpty(menuList3)){


                                for (Menu menu3 : menuList3) {

                                    if(checkedMenuIdList.contains(menu3.getId())){
                                        menu3.setChecked(true);
                                    }

                                }

                                menu2.setChildren(menuList3);

                            }else {
                                menu2.setChildren(Lists.newArrayList());
                            }

                        }



                        menu1.setChildren(menuList2);

                    }else {
                        menu1.setChildren(Lists.newArrayList());

                    }

                }




                retList.addAll(menuList1);
            }

        }


        return Result.ok(retList);


    }




    @DS("master_1")
    @LhTransaction
    public PcsResult add(MenuAddForm form,LoginUser loginUser){

        Menu menu = BeanUtil.copyProperties(form, Menu.class);

        menu.setVisible(MenuVisibleEnum.VISIBLE.getCode());

        menu.setCreateBy(loginUser.getUserId());
        menu.setCreateTime(DateUtil.date());
        menu.setUpdateTime(DateUtil.date());
        menu.setUpdateBy(loginUser.getUserId());
        menu.setCreateName(loginUser.getRealName());
        menu.setUpdateName(loginUser.getRealName());

        //如果是第一级
        if(form.getParentId() == null || form.getParentId().equals(0L)){
            menu.setLevel(MenuLevelEnum.LEVEL_1.getCode());
        }else {

           Menu parentMenu =  menuMapper.selectById(form.getParentId());

           //父节点不存在
           if(parentMenu == null){
              return  Result.error(PcsResultCode.MENU_NOT_EXIST);
           }


            menu.setLevel(parentMenu.getLevel() +1);


        }

        menuMapper.insert(menu);


        return Result.ok();

    }




    @DS("slave_1")
    @LhTransaction
    public PcsResult del(MenuDelForm form,LoginUser loginUser){

        // 查询是否有子菜单
        LambdaQueryWrapper<Menu> menuLambdaQueryWrapper = Wrappers.lambdaQuery();

        menuLambdaQueryWrapper.eq(Menu::getParentId, form.getId());
        menuLambdaQueryWrapper.eq(Menu::getIsEffect, IsEffectEnum.NORMAL.getCode());

        Long count = menuMapper.selectCount(menuLambdaQueryWrapper);
        // 有子菜单 不能删除
        if (count > 0) {
            return Result.error(PcsResultCode.MENU_HAS_CHILD);
        }



        Menu menu = Menu.builder()
                .id(form.getId())
                .isEffect(IsEffectEnum.DELETE.getCode())
                .updateBy(loginUser.getUserId())
                .updateTime(DateUtil.date())
                .updateName(loginUser.getRealName())
                .build();

        menuMapper.updateById(menu);

        return Result.ok();




    }



    @DS("master_1")
    @LhTransaction
    public PcsResult edit(MenuEditForm form,LoginUser loginUser) {


        Menu select =  menuMapper.selectById(form.getId());


        if(select == null){
            return  Result.error(PcsResultCode.MENU_NOT_EXIST);
        }

        // 编辑
        Menu menu = BeanUtil.copyProperties(form, Menu.class);
        menu.setUpdateTime(DateUtil.date());
        menu.setId(form.getId());
        menu.setUpdateBy(loginUser.getUserId());
        menu.setUpdateName(loginUser.getRealName());


        menuMapper.updateById(menu);



        return Result.ok();
    }
}
