package com.lanhu.lims.gateway.admin.utils.sequence.formatter;

/********************************
 * @title SequenceFormatter
 * @package com.lanhu.lims.gateway.admin.utils.sequence.formatter
 * @description 序列格式化接口
 *
 * <AUTHOR>
 * @date 2025/6/9 22:00
 * @version 0.0.1
 *********************************/
public interface SequenceFormatter {
    /**
     * 格式化序列值
     *
     * @param value 序列值
     * @return 格式化后的字符串
     */
    String format(int value);
}
