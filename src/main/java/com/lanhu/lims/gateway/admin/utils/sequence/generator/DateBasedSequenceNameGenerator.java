package com.lanhu.lims.gateway.admin.utils.sequence.generator;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/********************************
 * @title DateBasedSequenceNameGenerator
 * @package com.lanhu.lims.gateway.admin.utils.sequence.generator
 * @description 基于日期的动态序列名称生成器
 *
 * <AUTHOR>
 * @date 2025/6/9 22:30
 * @version 0.0.1
 *********************************/
public class DateBasedSequenceNameGenerator implements DynamicSequenceNameGenerator {
    private final String prefix;
    private final String datePattern;

    /**
     * 构造函数
     *
     * @param prefix 前缀
     * @param datePattern 日期格式模式
     */
    public DateBasedSequenceNameGenerator(String prefix, String datePattern) {
        this.prefix = prefix;
        this.datePattern = datePattern;
    }

    @Override
    public String generateSequenceName() {
        LocalDateTime now = LocalDateTime.now();
        String datePart = now.format(DateTimeFormatter.ofPattern(datePattern));
        return prefix + datePart;
    }

    /**
     * 创建年月格式的序列名称生成器
     *
     * @param prefix 前缀
     * @return 生成器实例
     */
    public static DateBasedSequenceNameGenerator yearMonth(String prefix) {
        return new DateBasedSequenceNameGenerator(prefix, "yyyyMM");
    }

    /**
     * 创建年月日格式的序列名称生成器
     *
     * @param prefix 前缀
     * @return 生成器实例
     */
    public static DateBasedSequenceNameGenerator yearMonthDay(String prefix) {
        return new DateBasedSequenceNameGenerator(prefix, "yyyyMMdd");
    }

    /**
     * 创建年格式的序列名称生成器
     *
     * @param prefix 前缀
     * @return 生成器实例
     */
    public static DateBasedSequenceNameGenerator year(String prefix) {
        return new DateBasedSequenceNameGenerator(prefix, "yyyy");
    }

    /**
     * 创建年周格式的序列名称生成器
     *
     * @param prefix 前缀
     * @return 生成器实例
     */
    public static DateBasedSequenceNameGenerator yearWeek(String prefix) {
        return new DateBasedSequenceNameGenerator(prefix, "yyyyww");
    }
}
