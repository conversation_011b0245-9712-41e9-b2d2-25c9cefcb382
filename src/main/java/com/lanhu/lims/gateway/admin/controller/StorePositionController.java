package com.lanhu.lims.gateway.admin.controller;

import cn.hutool.core.lang.tree.Tree;
import com.lanhu.lims.gateway.admin.annotation.Log;
import com.lanhu.lims.gateway.admin.auth.constants.SecurityConstants;
import com.lanhu.lims.gateway.admin.auth.context.SecurityContextHolder;
import com.lanhu.lims.gateway.admin.core.BaseController;
import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.core.Result;
import com.lanhu.lims.gateway.admin.enums.LogBusinessType;
import com.lanhu.lims.gateway.admin.enums.LogOperatorType;
import com.lanhu.lims.gateway.admin.model.LoginUser;
import com.lanhu.lims.gateway.admin.service.StorePositionService;
import com.lanhu.lims.gateway.admin.vo.req.*;
import com.lanhu.lims.gateway.admin.vo.resp.StorePositionDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/********************************
 * @title StorePositionController
 * @package com.lanhu.lims.gateway.admin.controller
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/12 21:25
 * @version 0.0.1
 *********************************/
@RestController
@Api(tags = "存储位置相关接口", value = "存储位置相关接口")
public class StorePositionController extends BaseController {
    @Resource
    private StorePositionService storePositionService;

    /**
     * 查询存储位置树形列表
     */
    @ApiOperation(value = "查询存储位置树形列表")
    @PostMapping("/storePosition/v1/treeList")
    public PcsResult<List<Tree<Long>>> typeTreeList(@Validated @RequestBody StorePositionListForm form) {
        return Result.ok(storePositionService.treeList(form));
    }

    /**
     * 查询存储位置详情
     */
    @ApiOperation(value = "查询存储位置详情")
    @PostMapping("/storePosition/v1/detail")
    public PcsResult<StorePositionDetailVO> detail(@Validated @RequestBody StorePositionSingleForm form) {
        return Result.ok(storePositionService.detail(form));
    }

    /**
     * 新增存储位置
     */
    @ApiOperation(value = "新增存储位置")
    @PostMapping("/storePosition/v1/add")
    @Log(title = "新增存储位置", businessType = LogBusinessType.INSERT, operatorType = LogOperatorType.PC)
    public PcsResult<Void> add(@Validated @RequestBody StorePositionAddForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        storePositionService.add(form, loginUser.getSysUser());
        return Result.ok();
    }

    /**
     * 编辑存储位置
     */
    @ApiOperation(value = "编辑存储位置")
    @PostMapping("/storePosition/v1/edit")
    @Log(title = "编辑存储位置", businessType = LogBusinessType.UPDATE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> edit(@Validated @RequestBody StorePositionEditForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        storePositionService.edit(form, loginUser.getSysUser());
        return Result.ok();
    }

    /**
     * 删除存储位置
     */
    @ApiOperation(value = "删除存储位置")
    @PostMapping("/storePosition/v1/del")
    @Log(title = "删除存储位置", businessType = LogBusinessType.LOGICDELETE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> del(@Validated @RequestBody StorePositionDelForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        storePositionService.del(form, loginUser.getSysUser());
        return Result.ok();
    }

    /**
     * 启用存储位置
     */
    @ApiOperation(value = "启用存储位置")
    @PostMapping("/storePosition/v1/enable")
    @Log(title = "启用存储位置", businessType = LogBusinessType.ENABLE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> enable(@Validated @RequestBody StorePositionSingleForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        storePositionService.enable(form, loginUser.getSysUser());
        return Result.ok();
    }

    /**
     * 禁用存储位置
     */
    @ApiOperation(value = "禁用存储位置")
    @PostMapping("/storePosition/v1/disable")
    @Log(title = "禁用存储位置", businessType = LogBusinessType.DISABLE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> disable(@Validated @RequestBody StorePositionSingleForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        storePositionService.disable(form, loginUser.getSysUser());
        return Result.ok();
    }
}
