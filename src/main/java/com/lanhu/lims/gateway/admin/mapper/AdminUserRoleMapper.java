package com.lanhu.lims.gateway.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanhu.lims.gateway.admin.model.AdminUserRole;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @description: ${DESCRIPTION}
 * @author: huangzheng
 * @date: 2025/4/29 15:16
 */
@Mapper
public interface AdminUserRoleMapper extends BaseMapper<AdminUserRole> {
    int batchInsert(@Param("list") List<AdminUserRole> list);

    int batchInsertOrUpdate(@Param("list") List<AdminUserRole> list);

    int insertOnDuplicateUpdate(AdminUserRole record);

    int insertOnDuplicateUpdateSelective(AdminUserRole record);

    void deleteByUserId(@Param("userId") Long userId);

}