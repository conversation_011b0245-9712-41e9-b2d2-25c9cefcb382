package com.lanhu.lims.gateway.admin.service;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lanhu.lims.gateway.admin.core.ProjectConstant;
import com.lanhu.lims.gateway.admin.mapper.AdminUserRoleMapper;
import com.lanhu.lims.gateway.admin.model.AdminUser;
import com.lanhu.lims.gateway.admin.model.AdminUserRole;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/1/15 8:26 下午
 */
@Service
@Slf4j
public class UserRoleService {


    @Autowired
    private AdminUserRoleMapper adminUserRoleMapper;



    /**
    * @description: 是否是平台运营者
    * @param: [plaPcUser]
    * @return: com.lanhu.imenu.gateway.pc.core.PcsResult<java.lang.Boolean>
    * @author: liuyi
    * @date: 8:28 下午 2023/1/15
    */
    @DS("slave_1")
    public Boolean isSuperAdmin(AdminUser adminUser){

        if(adminUser == null){
            return false;
        }


        LambdaQueryWrapper<AdminUserRole> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(AdminUserRole::getAdminUserId,adminUser.getId());

        List<AdminUserRole> adminUserRoleList = adminUserRoleMapper.selectList(wrapper);


        if(CollectionUtil.isEmpty(adminUserRoleList)){
            return false;
        }

        for (AdminUserRole userRole : adminUserRoleList) {

            if(userRole.getRoleId().equals(ProjectConstant.SUPER_ADMIN_ROLE_ID)){
                return true;
            }

        }

        return false;



    }
}
