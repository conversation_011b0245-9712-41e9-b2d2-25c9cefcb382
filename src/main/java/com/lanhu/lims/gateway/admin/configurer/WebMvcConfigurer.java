package com.lanhu.lims.gateway.admin.configurer;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.lanhu.lims.gateway.admin.auth.interceptor.HeaderInterceptor;
import com.lanhu.lims.gateway.admin.interceptor.MdcInterceptor;
import com.lanhu.lims.gateway.admin.resolver.I18nLocaleResolver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.TimeZone;

/********************************
 * @title WebMvcConfigurer
 * @package com.lanhu.imenu.gateway.client.configurer
 * @description description
 *
 * @date 2022/11/17 2:19 下午
 * @version 0.0.1
 *********************************/
@Slf4j
@Configuration
public class WebMvcConfigurer extends WebMvcConfigurationSupport {

    @Value("${spring.jackson.time-zone}")
    private String timeZone;


    @Resource
    private MdcInterceptor mdcInterceptor;


    @Resource
    private HeaderInterceptor headerInterceptor;


    /**
     * 解决跨域问题
     *
     * @param registry 注册
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowCredentials(true)
                .allowedHeaders("*")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "PUT", "OPTIONS");
    }

    /**
     * 添加拦截器
     *
     * @param registry 拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        String[] exclude = new String[]{
                "/swagger-resources/**",
                "/v2/api-docs",
                "/v2/api-docs-ext",
                "/configuration/security",
                "/configuration/ui",
                "/swagger-ui.html",
                "/error",
                "/webjars/**",
                "/favicon.ico"
               // "/warm-flow-ui/**",  // 放行warm-flow-ui
               // "/warm-flow/**"     // 放行warm-flow-ui
        };
        registry.addInterceptor(headerInterceptor).addPathPatterns("/**").excludePathPatterns(exclude);
        registry.addInterceptor(mdcInterceptor).addPathPatterns("/**").excludePathPatterns(exclude);
    }

    /**
     * 静态资源拦截器
     *
     * @param registry 拦截器
     */
    @Override
    protected void addResourceHandlers(ResourceHandlerRegistry registry) {
        //定向swagger 位置
        registry.addResourceHandler("/**").addResourceLocations("classpath:/static/");
        registry.addResourceHandler("doc.html").addResourceLocations("classpath:/META-INF/resources/");
        registry.addResourceHandler("service-worker.js").addResourceLocations("classpath:/META-INF/resources/");
        registry.addResourceHandler("/webjars/**").addResourceLocations("classpath:/META-INF/resources/webjars/");
    }


    @Override
    public LocaleResolver localeResolver() {
        return new I18nLocaleResolver();
    }

    @Override
    public void extendMessageConverters(List<HttpMessageConverter<?>> converters) {
        MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter();

        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.registerModule(new JavaTimeModule());
        SimpleModule simpleModule = new SimpleModule();
        simpleModule.addSerializer(Long.class, ToStringSerializer.instance);
        objectMapper.registerModule(simpleModule);
        //时区文件 ZoneInfoFile -- java
        objectMapper.setTimeZone(TimeZone.getTimeZone(timeZone)); //Asia/Shanghai
        objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        converter.setObjectMapper(objectMapper);
        converters.add(0, converter);
    }


}
