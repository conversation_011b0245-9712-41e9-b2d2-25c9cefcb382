package com.lanhu.lims.gateway.admin.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @description: miniConfig
 * @author: huangzheng
 * @date: 2025/4/30 17:14
 */
@Configuration
@ConfigurationProperties(prefix = "minio")
@Data
public class MinioProperties {

    /**
     * accessKeyId
     */
    private String accessKey;

    /**
     * accessKeySecret
     */
    private String secretKey;

    /**
     * endPoint
     */
    private String endPoint;


    /**
     * bucketName
     */
    private String bucketName;



}
