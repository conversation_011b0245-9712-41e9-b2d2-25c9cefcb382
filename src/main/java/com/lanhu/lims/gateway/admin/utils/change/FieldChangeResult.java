package com.lanhu.lims.gateway.admin.utils.change;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/********************************
 * @title FieldChangeResult
 * @package com.lanhu.lims.gateway.admin.utils.change
 * @description 字段变化结果
 *
 * <AUTHOR>
 * @date 2025/6/8 16:36
 * @version 1.0.0
 *********************************/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FieldChangeResult {
    
    /**
     * 对象类名
     */
    private String className;
    
    /**
     * 所有字段变化项
     */
    private List<FieldChangeItem> fieldChanges = new ArrayList<>();
    
    /**
     * 是否有变化
     */
    private boolean hasChanges;
    
    /**
     * 添加字段变化项
     * 
     * @param fieldChangeItem 字段变化项
     */
    public void addFieldChange(FieldChangeItem fieldChangeItem) {
        if (fieldChangeItem != null) {
            fieldChanges.add(fieldChangeItem);
            if (fieldChangeItem.isChanged()) {
                hasChanges = true;
            }
        }
    }
    
    /**
     * 获取所有发生变化的字段
     * 
     * @return 发生变化的字段列表
     */
    public List<FieldChangeItem> getChangedFields() {
        return fieldChanges.stream()
                .filter(FieldChangeItem::isChanged)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取所有未发生变化的字段
     * 
     * @return 未发生变化的字段列表
     */
    public List<FieldChangeItem> getUnchangedFields() {
        return fieldChanges.stream()
                .filter(item -> !item.isChanged())
                .collect(Collectors.toList());
    }
    
    /**
     * 获取变化字段的数量
     * 
     * @return 变化字段的数量
     */
    public int getChangedFieldCount() {
        return getChangedFields().size();
    }
    
    /**
     * 获取格式化的变化报告
     * 
     * @return 格式化的变化报告
     */
    public String getFormattedReport() {
        if (!hasChanges) {
            return "没有字段发生变化";
        }
        StringBuilder report = new StringBuilder();
        report.append(String.format("对象 %s 共有 %d 个字段发生变化：\n", className, getChangedFieldCount()));
        List<FieldChangeItem> changedFields = getChangedFields();
        for (int i = 0; i < changedFields.size(); i++) {
            FieldChangeItem item = changedFields.get(i);
            report.append(String.format("%d. %s\n", i + 1, item.getFormattedChange()));
        }
        return report.toString();
    }
    
    /**
     * 获取简化的变化报告
     * 
     * @return 简化的变化报告
     */
    public String getSimpleReport() {
        if (!hasChanges) {
            return "无变化";
        }
        return getChangedFields().stream()
                .map(FieldChangeItem::getSimpleChange)
                .collect(Collectors.joining("; "));
    }
    
    /**
     * 获取变化字段名列表
     * 
     * @return 变化字段名列表
     */
    public List<String> getChangedFieldNames() {
        return getChangedFields().stream()
                .map(FieldChangeItem::getFieldName)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取变化字段显示名列表
     *
     * @return 变化字段显示名列表
     */
    public List<String> getChangedFieldDisplayNames() {
        return getChangedFields().stream()
                .map(item -> item.getFieldDisplayName() != null ? item.getFieldDisplayName() : item.getFieldName())
                .collect(Collectors.toList());
    }

    /**
     * 获取变化字段的JSON数组
     *
     * @return JSON数组字符串
     */
    public String getChangedFieldsJson() {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            List<Map<String, Object>> changeList = new ArrayList<>();
            for (FieldChangeItem item : getChangedFields()) {
                Map<String, Object> changeMap = new HashMap<>();
                changeMap.put("fieldName", item.getFieldName());
                changeMap.put("fieldDisplayName", item.getFieldDisplayName());
                changeMap.put("fieldType", item.getFieldType());
                changeMap.put("oldValue", item.getOldValue());
                changeMap.put("newValue", item.getNewValue());
                changeMap.put("changed", item.isChanged());
                changeMap.put("formattedChange", item.getFormattedChange());
                changeList.add(changeMap);
            }
            return objectMapper.writeValueAsString(changeList);
        } catch (JsonProcessingException e) {
            return "[]";
        }
    }

    /**
     * 获取简化的变化字段JSON数组（只包含核心信息）
     *
     * @return 简化的JSON数组字符串
     */
    public String getSimpleChangedFieldsJson() {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            List<Map<String, Object>> changeList = new ArrayList<>();
            for (FieldChangeItem item : getChangedFields()) {
                Map<String, Object> changeMap = new HashMap<>();
                changeMap.put("field", item.getFieldDisplayName() != null ? item.getFieldDisplayName() : item.getFieldName());
                changeMap.put("oldValue", item.getOldValue());
                changeMap.put("newValue", item.getNewValue());
                changeList.add(changeMap);
            }
            return objectMapper.writeValueAsString(changeList);
        } catch (JsonProcessingException e) {
            return "[]";
        }
    }

    /**
     * 获取所有字段的JSON数组（包括未变化的字段）
     *
     * @return 所有字段的JSON数组字符串
     */
    public String getAllFieldsJson() {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            List<Map<String, Object>> fieldList = new ArrayList<>();
            for (FieldChangeItem item : fieldChanges) {
                Map<String, Object> fieldMap = new HashMap<>();
                fieldMap.put("fieldName", item.getFieldName());
                fieldMap.put("fieldDisplayName", item.getFieldDisplayName());
                fieldMap.put("fieldType", item.getFieldType());
                fieldMap.put("oldValue", item.getOldValue());
                fieldMap.put("newValue", item.getNewValue());
                fieldMap.put("changed", item.isChanged());
                if (item.isChanged()) {
                    fieldMap.put("formattedChange", item.getFormattedChange());
                }
                fieldList.add(fieldMap);
            }
            return objectMapper.writeValueAsString(fieldList);
        } catch (JsonProcessingException e) {
            return "[]";
        }
    }
}
