package com.lanhu.lims.gateway.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanhu.lims.gateway.admin.model.BusinessFlowApplication;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @description: ${DESCRIPTION}
 * @author: huangzheng
 * @date: 2025/6/17 15:28
 */
@Mapper
public interface BusinessFlowApplicationMapper extends BaseMapper<BusinessFlowApplication> {
    int updateBatch(@Param("list") List<BusinessFlowApplication> list);

    int updateBatchSelective(@Param("list") List<BusinessFlowApplication> list);

    int batchInsert(@Param("list") List<BusinessFlowApplication> list);

    int batchInsertOrUpdate(@Param("list") List<BusinessFlowApplication> list);

    int insertOnDuplicateUpdate(BusinessFlowApplication record);

    int insertOnDuplicateUpdateSelective(BusinessFlowApplication record);
}