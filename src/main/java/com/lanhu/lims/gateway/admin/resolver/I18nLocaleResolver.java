package com.lanhu.lims.gateway.admin.resolver;
import cn.hutool.core.util.StrUtil;
import com.lanhu.lims.gateway.admin.core.ProjectConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.LocaleResolver;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Locale;

@Slf4j
public class I18nLocaleResolver implements LocaleResolver {


    @Override
    public Locale resolveLocale(HttpServletRequest httpServletRequest) {
        String language = httpServletRequest.getHeader(ProjectConstant.HEADER_LANGUAGE_FLAG);
        Locale locale = Locale.SIMPLIFIED_CHINESE;
        if (StrUtil.isNotBlank(language)) {
            String[] split = language.split(ProjectConstant.UNDER_LINE);
            locale = new Locale(split[0], split[1]);
        }

        return locale;
    }

    @Override
    public void setLocale(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Locale locale) {
    }
}