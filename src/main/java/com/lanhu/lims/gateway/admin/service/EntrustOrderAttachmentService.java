package com.lanhu.lims.gateway.admin.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lanhu.lims.gateway.admin.annotation.LhTransaction;
import com.lanhu.lims.gateway.admin.enums.IsEffectEnum;
import com.lanhu.lims.gateway.admin.mapper.EntrustOrderAttachmentMapper;
import com.lanhu.lims.gateway.admin.model.EntrustOrderAttachment;
import com.lanhu.lims.gateway.admin.model.LoginUser;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @description: 委托单附件服务
 * @author: huangzheng
 * @date: 2025/6/11 10:49
 */

@Service
public class EntrustOrderAttachmentService {


    @Resource
    private EntrustOrderAttachmentMapper entrustOrderAttachmentMapperMapper;


    /**
     * 获取委托单附件列表
     */
    public List<EntrustOrderAttachment> selectListByEntrustOrderId(Long entrustOrderId) {


        LambdaQueryWrapper<EntrustOrderAttachment> wrapper = Wrappers.lambdaQuery();

        wrapper.eq(EntrustOrderAttachment::getEntrustOrderId, entrustOrderId);
        wrapper.eq(EntrustOrderAttachment::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.orderByDesc(EntrustOrderAttachment::getCreateTime);

        List<EntrustOrderAttachment> entrustOrderAttachmentList = entrustOrderAttachmentMapperMapper.selectList(wrapper);

        return entrustOrderAttachmentList;
    }


    /**
     * 删除指定委托单的附件信息
     */
    @DS("master_1")
    @LhTransaction
    public void delByEntrustOrderId(Long entrustOrderId, LoginUser loginUser) {


        LambdaUpdateWrapper<EntrustOrderAttachment> entrustOrderAttachmentLambdaUpdateWrapper = Wrappers.lambdaUpdate(EntrustOrderAttachment.class);


        entrustOrderAttachmentLambdaUpdateWrapper.eq(EntrustOrderAttachment::getEntrustOrderId, entrustOrderId);

        entrustOrderAttachmentLambdaUpdateWrapper.set(EntrustOrderAttachment::getIsEffect, IsEffectEnum.DELETE.getCode());

        entrustOrderAttachmentLambdaUpdateWrapper.set(EntrustOrderAttachment::getUpdateBy, loginUser.getUserId());

        entrustOrderAttachmentLambdaUpdateWrapper.set(EntrustOrderAttachment::getUpdateTime, new Date());

        entrustOrderAttachmentLambdaUpdateWrapper.set(EntrustOrderAttachment::getUpdateName, loginUser.getRealName());

        entrustOrderAttachmentMapperMapper.update(null, entrustOrderAttachmentLambdaUpdateWrapper);


    }
}
