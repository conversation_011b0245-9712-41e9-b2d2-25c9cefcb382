package com.lanhu.lims.gateway.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanhu.lims.gateway.admin.annotation.Log;
import com.lanhu.lims.gateway.admin.auth.constants.SecurityConstants;
import com.lanhu.lims.gateway.admin.auth.context.SecurityContextHolder;
import com.lanhu.lims.gateway.admin.core.BaseController;
import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.core.Result;
import com.lanhu.lims.gateway.admin.enums.LogBusinessType;
import com.lanhu.lims.gateway.admin.enums.LogOperatorType;
import com.lanhu.lims.gateway.admin.model.LoginUser;
import com.lanhu.lims.gateway.admin.service.ExecutionStandardService;
import com.lanhu.lims.gateway.admin.vo.req.*;
import com.lanhu.lims.gateway.admin.vo.resp.ExecutionStandardDetailVO;
import com.lanhu.lims.gateway.admin.vo.resp.ExecutionStandardListVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/********************************
 * @title ExecutionStandardController
 * @package com.lanhu.lims.gateway.admin.controller
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/14 16:18
 * @version 0.0.1
 *********************************/
@RestController
@Api(tags = "执行标准相关接口", value = "执行标准相关接口")
public class ExecutionStandardController extends BaseController {
    @Resource
    private ExecutionStandardService executionStandardService;

    /**
     * 查询执行标准列表
     */
    @ApiOperation(value = "查询执行标准列表")
    @PostMapping("/executionStandard/v1/list")
    public PcsResult<List<ExecutionStandardListVO>> list(@Validated @RequestBody ExecutionStandardListForm form) {
        return Result.ok(executionStandardService.list(form));
    }

    /**
     * 查询执行标准分页列表
     */
    @ApiOperation(value = "查询执行标准分页列表")
    @PostMapping("/executionStandard/v1/listPage")
    public PcsResult<IPage<ExecutionStandardListVO>> listPage(@Validated @RequestBody ExecutionStandardListPageForm form) {
        return Result.ok(executionStandardService.listPage(form));
    }

    /**
     * 查询执行标准详情
     */
    @ApiOperation(value = "查询执行标准详情")
    @PostMapping("/executionStandard/v1/detail")
    public PcsResult<ExecutionStandardDetailVO> detail(@Validated @RequestBody ExecutionStandardSingleForm form) {
        return Result.ok(executionStandardService.detail(form));
    }

    /**
     * 新增执行标准
     */
    @ApiOperation(value = "新增执行标准")
    @PostMapping("/executionStandard/v1/add")
    @Log(title = "新增执行标准", businessType = LogBusinessType.INSERT, operatorType = LogOperatorType.PC)
    public PcsResult<Void> add(@Validated @RequestBody ExecutionStandardAddForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        executionStandardService.add(form, loginUser.getSysUser());
        return Result.ok();
    }

    /**
     * 修改执行标准
     */
    @ApiOperation(value = "修改执行标准")
    @PostMapping("/executionStandard/v1/edit")
    @Log(title = "修改执行标准", businessType = LogBusinessType.UPDATE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> edit(@Validated @RequestBody ExecutionStandardEditForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        executionStandardService.edit(form, loginUser.getSysUser());
        return Result.ok();
    }

    /**
     * 删除执行标准
     */
    @ApiOperation(value = "删除执行标准")
    @PostMapping("/executionStandard/v1/del")
    @Log(title = "删除执行标准", businessType = LogBusinessType.LOGICDELETE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> del(@Validated @RequestBody ExecutionStandardDelForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        executionStandardService.del(form, loginUser.getSysUser());
        return Result.ok();
    }

    /**
     * 启用执行标准
     */
    @ApiOperation(value = "启用执行标准")
    @PostMapping("/executionStandard/v1/enable")
    @Log(title = "启用执行标准", businessType = LogBusinessType.ENABLE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> enable(@Validated @RequestBody ExecutionStandardSingleForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        executionStandardService.enable(form, loginUser.getSysUser());
        return Result.ok();
    }

    /**
     * 禁用执行标准
     */
    @ApiOperation(value = "禁用执行标准")
    @PostMapping("/executionStandard/v1/disable")
    @Log(title = "禁用执行标准", businessType = LogBusinessType.DISABLE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> disable(@Validated @RequestBody ExecutionStandardSingleForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        executionStandardService.disable(form, loginUser.getSysUser());
        return Result.ok();
    }

    /**
     * 新增执行标准版本
     */
    @ApiOperation(value = "新增执行标准版本")
    @PostMapping("/executionStandard/v1/addVersion")
    @Log(title = "新增执行标准版本", businessType = LogBusinessType.INSERT, operatorType = LogOperatorType.PC)
    public PcsResult<Void> addVersion(@Validated @RequestBody ExecutionStandardVersionAddForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        executionStandardService.addVersion(form, loginUser.getSysUser());
        return Result.ok();
    }
}
