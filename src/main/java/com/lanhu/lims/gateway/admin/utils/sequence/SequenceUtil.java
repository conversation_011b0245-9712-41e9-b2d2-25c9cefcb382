package com.lanhu.lims.gateway.admin.utils.sequence;

import cn.hutool.extra.spring.SpringUtil;
import com.lanhu.lims.gateway.admin.mapper.SequenceMapper;
import com.lanhu.lims.gateway.admin.model.Sequence;
import com.lanhu.lims.gateway.admin.utils.sequence.formatter.SequenceFormatter;
import com.lanhu.lims.gateway.admin.utils.sequence.generator.DynamicSequenceNameGenerator;

import java.util.ArrayList;
import java.util.List;

/********************************
 * @title SequenceUtil
 * @package com.lanhu.lims.gateway.admin.utils
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/9 21:28
 * @version 0.0.1
 *********************************/
public class SequenceUtil {
    /**
     * 获取下一个值
     *
     * @param name 序列名称
     * @return 下一个值
     */
    public synchronized static int getNextValue(String name) {
        return getNextValue(name, 1);
    }

    /**
     * 获取下一个值
     *
     * @param name 序列名称
     * @return 下一个值
     */
    public synchronized static int getNextValue(String name, int increment) {
        return getNextValue(name, 1, increment);
    }

    /**
     * 获取下一个值
     *
     * @param nameEnum 序列名称枚举
     * @return 下一个值
     */
    public synchronized static int getNextValue(SequenceNameEnum nameEnum) {
        return getNextValue(nameEnum.getName(), nameEnum.getCurrentValue(), nameEnum.getIncrement());
    }

    /**
     * 获取下一个格式化的值
     *
     * @param nameEnum 序列名称枚举
     * @return 格式化后的字符串
     */
    public synchronized static String getNextFormattedValue(SequenceNameEnum nameEnum) {
        int nextValue = getNextValue(nameEnum);
        return nameEnum.getFormatter().format(nextValue);
    }

    /**
     * 获取下一个格式化的值（自定义格式化器）
     *
     * @param name      序列名称
     * @param formatter 自定义格式化器
     * @return 格式化后的字符串
     */
    public synchronized static String getNextFormattedValue(String name, SequenceFormatter formatter) {
        int nextValue = getNextValue(name);
        return formatter.format(nextValue);
    }

    /**
     * 获取下一个格式化的值（自定义格式化器和参数）
     *
     * @param name         序列名称
     * @param currentValue 当前值
     * @param increment    增量
     * @param formatter    自定义格式化器
     * @return 格式化后的字符串
     */
    public synchronized static String getNextFormattedValue(String name, int currentValue, int increment, SequenceFormatter formatter) {
        int nextValue = getNextValue(name, currentValue, increment);
        return formatter.format(nextValue);
    }

    /**
     * 获取下一个动态序列值
     *
     * @param nameGenerator 动态序列名称生成器
     * @return 下一个值
     */
    public synchronized static int getNextDynamicValue(DynamicSequenceNameGenerator nameGenerator) {
        String dynamicName = nameGenerator.generateSequenceName();
        return getNextValue(dynamicName);
    }

    /**
     * 获取下一个动态序列值（指定参数）
     *
     * @param nameGenerator 动态序列名称生成器
     * @param currentValue  当前值
     * @param increment     增量
     * @return 下一个值
     */
    public synchronized static int getNextDynamicValue(DynamicSequenceNameGenerator nameGenerator, int currentValue, int increment) {
        String dynamicName = nameGenerator.generateSequenceName();
        return getNextValue(dynamicName, currentValue, increment);
    }

    /**
     * 获取下一个动态序列格式化值
     *
     * @param nameGenerator 动态序列名称生成器
     * @param formatter     格式化器
     * @return 格式化后的字符串
     */
    public synchronized static String getNextDynamicFormattedValue(DynamicSequenceNameGenerator nameGenerator, SequenceFormatter formatter) {
        int nextValue = getNextDynamicValue(nameGenerator);
        return formatter.format(nextValue);
    }

    /**
     * 获取下一个动态序列格式化值（指定参数）
     *
     * @param nameGenerator 动态序列名称生成器
     * @param currentValue  当前值
     * @param increment     增量
     * @param formatter     格式化器
     * @return 格式化后的字符串
     */
    public synchronized static String getNextDynamicFormattedValue(DynamicSequenceNameGenerator nameGenerator, int currentValue, int increment, SequenceFormatter formatter) {
        int nextValue = getNextDynamicValue(nameGenerator, currentValue, increment);
        return formatter.format(nextValue);
    }

    /**
     * 获取下一个值
     *
     * @param name 序列名称
     * @return 下一个值
     */
    public synchronized static int getNextValue(String name, int currentValue, int increment) {
        SequenceMapper sequenceMapper = SpringUtil.getBean(SequenceMapper.class);
        if (sequenceMapper != null) {
            Sequence sequence = sequenceMapper.selectByName(name);
            if (sequence == null) {
                sequence = new Sequence();
                sequence.setName(name);
                sequence.setCurrentValue(currentValue);
                sequence.setIncrement(increment);
                sequenceMapper.insert(sequence);
            } else {
                sequence.setCurrentValue(sequence.getCurrentValue() + sequence.getIncrement());
                sequenceMapper.updateById(sequence);
            }
            return sequence.getCurrentValue();
        }
        return 0;
    }

    // ================================== 批量获取序列方法 ==================================

    /**
     * 批量获取下一批序列值
     *
     * @param name  序列名称
     * @param count 获取数量
     * @return 序列值列表
     */
    public synchronized static List<Integer> getNextBatchValues(String name, int count) {
        return getNextBatchValues(name, 1, 1, count);
    }

    /**
     * 批量获取下一批序列值（指定参数）
     *
     * @param name         序列名称
     * @param currentValue 当前值
     * @param increment    增量
     * @param count        获取数量
     * @return 序列值列表
     */
    public synchronized static List<Integer> getNextBatchValues(String name, int currentValue, int increment, int count) {
        if (count <= 0) {
            return new ArrayList<>();
        }
        SequenceMapper sequenceMapper = SpringUtil.getBean(SequenceMapper.class);
        if (sequenceMapper == null) {
            return new ArrayList<>();
        }
        Sequence sequence = sequenceMapper.selectByName(name);
        if (sequence == null) {
            sequence = new Sequence();
            sequence.setName(name);
            sequence.setCurrentValue(currentValue);
            sequence.setIncrement(increment);
            sequenceMapper.insert(sequence);
        }
        List<Integer> result = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            sequence.setCurrentValue(sequence.getCurrentValue() + sequence.getIncrement());
            result.add(sequence.getCurrentValue());
        }
        // 批量更新序列值
        sequenceMapper.updateById(sequence);
        return result;
    }

    /**
     * 批量获取下一批序列值（使用枚举）
     *
     * @param nameEnum 序列名称枚举
     * @param count    获取数量
     * @return 序列值列表
     */
    public synchronized static List<Integer> getNextBatchValues(SequenceNameEnum nameEnum, int count) {
        return getNextBatchValues(nameEnum.getName(), nameEnum.getCurrentValue(), nameEnum.getIncrement(), count);
    }

    /**
     * 批量获取下一批格式化序列值
     *
     * @param name      序列名称
     * @param formatter 格式化器
     * @param count     获取数量
     * @return 格式化后的序列值列表
     */
    public synchronized static List<String> getNextBatchFormattedValues(String name, SequenceFormatter formatter, int count) {
        List<Integer> values = getNextBatchValues(name, count);
        List<String> result = new ArrayList<>();
        for (Integer value : values) {
            result.add(formatter.format(value));
        }
        return result;
    }

    /**
     * 批量获取下一批格式化序列值（指定参数）
     *
     * @param name         序列名称
     * @param currentValue 当前值
     * @param increment    增量
     * @param formatter    格式化器
     * @param count        获取数量
     * @return 格式化后的序列值列表
     */
    public synchronized static List<String> getNextBatchFormattedValues(String name, int currentValue, int increment, SequenceFormatter formatter, int count) {
        List<Integer> values = getNextBatchValues(name, currentValue, increment, count);
        List<String> result = new ArrayList<>();
        for (Integer value : values) {
            result.add(formatter.format(value));
        }
        return result;
    }

    /**
     * 批量获取下一批格式化序列值（使用枚举）
     *
     * @param nameEnum 序列名称枚举
     * @param count    获取数量
     * @return 格式化后的序列值列表
     */
    public synchronized static List<String> getNextBatchFormattedValues(SequenceNameEnum nameEnum, int count) {
        List<Integer> values = getNextBatchValues(nameEnum, count);
        List<String> result = new ArrayList<>();
        for (Integer value : values) {
            result.add(nameEnum.getFormatter().format(value));
        }
        return result;
    }

    /**
     * 批量获取下一批动态序列值
     *
     * @param nameGenerator 动态序列名称生成器
     * @param count         获取数量
     * @return 序列值列表
     */
    public synchronized static List<Integer> getNextBatchDynamicValues(DynamicSequenceNameGenerator nameGenerator, int count) {
        String dynamicName = nameGenerator.generateSequenceName();
        return getNextBatchValues(dynamicName, count);
    }

    /**
     * 批量获取下一批动态序列值（指定参数）
     *
     * @param nameGenerator 动态序列名称生成器
     * @param currentValue  当前值
     * @param increment     增量
     * @param count         获取数量
     * @return 序列值列表
     */
    public synchronized static List<Integer> getNextBatchDynamicValues(DynamicSequenceNameGenerator nameGenerator, int currentValue, int increment, int count) {
        String dynamicName = nameGenerator.generateSequenceName();
        return getNextBatchValues(dynamicName, currentValue, increment, count);
    }

    /**
     * 批量获取下一批动态序列格式化值
     *
     * @param nameGenerator 动态序列名称生成器
     * @param formatter     格式化器
     * @param count         获取数量
     * @return 格式化后的序列值列表
     */
    public synchronized static List<String> getNextBatchDynamicFormattedValues(DynamicSequenceNameGenerator nameGenerator, SequenceFormatter formatter, int count) {
        List<Integer> values = getNextBatchDynamicValues(nameGenerator, count);
        List<String> result = new ArrayList<>();
        for (Integer value : values) {
            result.add(formatter.format(value));
        }
        return result;
    }

    /**
     * 批量获取下一批动态序列格式化值（指定参数）
     *
     * @param nameGenerator 动态序列名称生成器
     * @param currentValue  当前值
     * @param increment     增量
     * @param formatter     格式化器
     * @param count         获取数量
     * @return 格式化后的序列值列表
     */
    public synchronized static List<String> getNextBatchDynamicFormattedValues(DynamicSequenceNameGenerator nameGenerator, int currentValue, int increment, SequenceFormatter formatter, int count) {
        List<Integer> values = getNextBatchDynamicValues(nameGenerator, currentValue, increment, count);
        List<String> result = new ArrayList<>();
        for (Integer value : values) {
            result.add(formatter.format(value));
        }
        return result;
    }
}
