package com.lanhu.lims.gateway.admin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanhu.lims.gateway.admin.auth.utils.SecurityUtils;
import com.lanhu.lims.gateway.admin.core.BaseController;
import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.model.LoginUser;
import com.lanhu.lims.gateway.admin.model.Sample;
import com.lanhu.lims.gateway.admin.service.SampleService;
import com.lanhu.lims.gateway.admin.vo.req.*;
import com.lanhu.lims.gateway.admin.vo.resp.SampleFormAndCountVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description: 样本相关接口
 * @author: huangzheng
 * @date: 2025/6/16 13:15
 */
@RestController
@Api(value = "样本相关接口", tags = "样本相关接口")
public class SampleController extends BaseController {



    @Resource
    private SampleService sampleService;




    @ApiOperation(value = "分页查询独立样本列表", notes = "分页查询独立样本列表")
    @PostMapping("/sample/v1/independentSample/listPage")
    public PcsResult<Page<Sample>> independentSampleListPage(@Validated @RequestBody IndependentSampleListPageForm form) {
        return sampleService.independentSampleListPage(form);
    }



    @ApiOperation(value = "查询委托单中的样本96孔板号列表", notes = "查询委托单中的样本96孔板号列表")
    @PostMapping("/sample/v1/plateList")
    public PcsResult<List<String>> plateList(@Validated @RequestBody SamplePlateListForm form) {
        return sampleService.plateList(form);
    }





    @ApiOperation(value = "根据板号分页查询样本", notes = "根据板号分页查询样本")
    @PostMapping("/sample/v1/plateSampleListPage")
    public PcsResult<Page<Sample>> plateSampleListPage(@Validated @RequestBody PlateSampleListPageForm form) {
        return sampleService.plateSampleListPage(form);
    }


    @ApiOperation(value = "查询委托单的样本形式和样本数量", notes = "查询委托单的样本形式和样本数量")
    @PostMapping("/sample/v1/SampleFormAndCount")
    public PcsResult<SampleFormAndCountVO> getSampleFormAndCount(@Validated @RequestBody SampleFormAndCountForm form) {
        return sampleService.getSampleFormAndCount(form);
    }



    @ApiOperation(value = "样本批量导入", notes = "样本批量导入")
    @PostMapping("/sample/v1/importPlateSample")
    public PcsResult batchImportSample(@Validated @RequestBody SampleBatchImportForm form) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        return sampleService.batchImportSample(form, loginUser);
    }



    /**
     * 物理删除样本
     */

    @ApiOperation(value = "物理删除样本", notes = "物理删除样本")
    @PostMapping("/sample/v1/physicalDel")
    public PcsResult physicalDelete(@Validated @RequestBody SamplePhysicalDeleteForm form) {
        return sampleService.physicalDelIndependentSample(form);
    }


    /**
     * 根据板号物理删除样本
     */
    @ApiOperation(value = "根据板号物理删除样本", notes = "根据板号物理删除样本")
    @PostMapping("/sample/v1/physicalDelPlate")
    public PcsResult physicalDelPlate(@Validated @RequestBody PlateSamplePhysicalDelForm form) {
        return sampleService.physicalDelPlateSample(form);
    }


































}
