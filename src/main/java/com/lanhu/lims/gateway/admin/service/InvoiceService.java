package com.lanhu.lims.gateway.admin.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lanhu.lims.gateway.admin.annotation.LhTransaction;
import com.lanhu.lims.gateway.admin.enums.IsEffectEnum;
import com.lanhu.lims.gateway.admin.mapper.InvoiceMapper;
import com.lanhu.lims.gateway.admin.model.Invoice;
import com.lanhu.lims.gateway.admin.model.LoginUser;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @description: 委托单发票相关服务
 * @author: huangzheng
 * @date: 2025/6/10 9:49
 */

@Service
public class InvoiceService {



    @Resource
    private InvoiceMapper invoiceMapper;


    /**
     * 查询指定委托单的发票信息
     */
    @DS("slave_1")
    public Invoice selectByEntrustOrderId(Long entrustOrderId){


        LambdaQueryWrapper<Invoice> invoiceLambdaQueryWrapper = Wrappers.lambdaQuery();


        invoiceLambdaQueryWrapper.eq(Invoice::getEntrustOrderId, entrustOrderId);

        invoiceLambdaQueryWrapper.eq(Invoice::getIsEffect, IsEffectEnum.NORMAL.getCode());

        return invoiceMapper.selectOne(invoiceLambdaQueryWrapper);



    }





    /**
     * 删除指定委托单的发票信息
     */
    @LhTransaction
    @DS("master_1")
    public void delByEntrustOrderId(Long entrustOrderId, LoginUser loginUser) {

        LambdaUpdateWrapper<Invoice> invoiceLambdaUpdateWrapper = Wrappers.lambdaUpdate(Invoice.class);


        invoiceLambdaUpdateWrapper.eq(Invoice::getEntrustOrderId, entrustOrderId);
        invoiceLambdaUpdateWrapper.set(Invoice::getIsEffect, IsEffectEnum.DELETE.getCode());
        invoiceLambdaUpdateWrapper.set(Invoice::getUpdateBy, loginUser.getUserId());
        invoiceLambdaUpdateWrapper.set(Invoice::getUpdateTime, new Date());
        invoiceLambdaUpdateWrapper.set(Invoice::getUpdateName, loginUser.getRealName());
        invoiceMapper.delete(invoiceLambdaUpdateWrapper);






    }
}
