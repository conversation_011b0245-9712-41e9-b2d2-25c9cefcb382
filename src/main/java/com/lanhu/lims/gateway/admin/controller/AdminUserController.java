package com.lanhu.lims.gateway.admin.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanhu.lims.gateway.admin.annotation.Log;
import com.lanhu.lims.gateway.admin.auth.annotation.IgnoreSecurity;
import com.lanhu.lims.gateway.admin.auth.constants.SecurityConstants;
import com.lanhu.lims.gateway.admin.auth.context.SecurityContextHolder;
import com.lanhu.lims.gateway.admin.core.BaseController;
import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.core.Result;
import com.lanhu.lims.gateway.admin.enums.LogBusinessType;
import com.lanhu.lims.gateway.admin.enums.LogOperatorType;
import com.lanhu.lims.gateway.admin.model.AdminUser;
import com.lanhu.lims.gateway.admin.model.LoginUser;
import com.lanhu.lims.gateway.admin.service.AdminUserService;
import com.lanhu.lims.gateway.admin.vo.req.*;
import com.lanhu.lims.gateway.admin.vo.resp.AdminUserVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


/**
 * @auth: huhouchun
 * @version: 1.0.0
 * @date: 2022/11/16 15:19
 */
@Slf4j
@Api(value = "lims-账号管理相关", tags = "lims-账号管理相关")
@RestController
public class AdminUserController extends BaseController {


    @Resource
    private AdminUserService adminUserService;



    @ApiOperation(value = "用户登录", notes = "用户登录")
    @IgnoreSecurity
    @PostMapping("/user/v1/login")
    public PcsResult<LoginUser> loginByUserName(@Validated @RequestBody UserLoginForm userLoginForm) {
        return adminUserService.loginByUserName(userLoginForm);

    }





    @ApiOperation(value = "用户退出", notes = "用户退出")
    @PostMapping("/user/v1/logout")
    @Log(title = "ADMIN用户PC退出", businessType = LogBusinessType.LOGOUT, operatorType = LogOperatorType.PC)
    public PcsResult logout() {
        return adminUserService.logout();

    }



    @ApiOperation(value = "用户修改密码", notes = "用户修改密码")
    @PostMapping("/user/v1/pwdModify")
    @Log(title = "用户修改密码", businessType = LogBusinessType.UPDATE, operatorType = LogOperatorType.PC)
    public PcsResult pwdModify(@Validated @RequestBody UserModifyPwdForm userModifyPwdForm) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER,LoginUser.class);
        return adminUserService.userPwdModify(loginUser.getSysUser(),userModifyPwdForm);
    }



    @ApiOperation(value = "用户新增", notes = "用户新增")
    @PostMapping("/user/v1/add")
    @Log(title = "用户新增", businessType = LogBusinessType.INSERT, operatorType = LogOperatorType.PC)
    public PcsResult add(@Validated @RequestBody UserAddForm plaPcUserAddForm) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER,LoginUser.class);
        return adminUserService.add(loginUser.getSysUser(),plaPcUserAddForm);
    }


    @ApiOperation(value = "用户信息", notes = "用户信息")
    @PostMapping("/user/v1/info")
    public PcsResult<LoginUser> info() {

        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER,LoginUser.class);

        return Result.ok(loginUser);
    }


    @ApiOperation(value = "用户详情", notes = "用户详情")
    @PostMapping("/user/v1/detail")
    public PcsResult<AdminUserVO> detail(@Validated @RequestBody UserDetailForm request) {

        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER,LoginUser.class);


        return adminUserService.detail(loginUser.getSysUser(),request);
    }


    @ApiOperation(value = "用户编辑", notes = "用户编辑")
    @PostMapping("/user/v1/edit")
    @Log(title = "用户编辑", businessType = LogBusinessType.UPDATE, operatorType = LogOperatorType.PC)
    public PcsResult edit(@Validated @RequestBody UserEditForm request) {

        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER,LoginUser.class);

        return adminUserService.edit(loginUser.getSysUser(),request);
    }


    @ApiOperation(value = "用户密码重置", notes = "用户修改重置")
    @PostMapping("/user/v1/pwdReset")
    @Log(title = "用户密码重置", businessType = LogBusinessType.RESET, operatorType = LogOperatorType.PC)
    public PcsResult pwdReset(@Validated @RequestBody UserResetPwdForm userResetPwdForm) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER,LoginUser.class);
        return adminUserService.userPwdReset(loginUser.getSysUser(),userResetPwdForm);
    }


    @ApiOperation(value = "用户恢复/删除", notes = "用户恢复/删除")
    @PostMapping("/user/v1/enable")
    @Log(title = "用户恢复/删除", businessType = LogBusinessType.UPDATE, operatorType = LogOperatorType.PC)
    public PcsResult enable(@Validated @RequestBody UserEnableForm userEnableForm) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER,LoginUser.class);
        return adminUserService.enable(loginUser.getSysUser(),userEnableForm);
    }


    @ApiOperation(value = "用户启用/禁用", notes = "用户启用/禁用")
    @PostMapping("/user/v1/frozen")
    @Log(title = "用户启用/禁用", businessType = LogBusinessType.UPDATE, operatorType = LogOperatorType.PC)
    public PcsResult frozen(@Validated @RequestBody UserFrozenForm userFrozenForm) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER,LoginUser.class);
        return adminUserService.frozen(loginUser.getSysUser(),userFrozenForm);
    }



    @ApiOperation(value = "用户分页查询", notes = "用户分页查询")
    @PostMapping("/user/v1/listPage")
    public PcsResult<IPage<AdminUser>> listPage(@Validated @RequestBody UserListPageForm request) {

        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER,LoginUser.class);


        return adminUserService.listPage(loginUser.getSysUser(),request);
    }



    @ApiOperation(value = "所有用户列表查询", notes = "所有用户列表查询")
    @PostMapping("/user/v1/list")
    public PcsResult<List<AdminUser>> list(@Validated @RequestBody UserListForm request) {

        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER,LoginUser.class);


        return adminUserService.list(loginUser.getSysUser(),request);
    }






    @ApiOperation(value = "用户批量删除", notes = "批量删除")
    @PostMapping("/user/v1/batchDel")
    @Log(title = "用户批量删除", businessType = LogBusinessType.LOGICDELETE, operatorType = LogOperatorType.PC)
    public PcsResult batchDel(@Validated @RequestBody UserBatchDelForm request) {

        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER,LoginUser.class);


        return adminUserService.batchDel(loginUser.getSysUser(),request);
    }


    @ApiOperation(value = "批量冻结", notes = "批量冻结")
    @PostMapping("/user/v1/batchFrozen")
    @Log(title = "用户批量冻结", businessType = LogBusinessType.UPDATE, operatorType = LogOperatorType.PC)
    public PcsResult batchFrozen(@Validated @RequestBody UserBatchFrozenForm request) {

        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER,LoginUser.class);


        return adminUserService.batchFrozen(loginUser.getSysUser(),request);
    }



    @ApiOperation(value = "用户设置休假", notes = "用户设置休假")
    @PostMapping("/user/v1/vacation/setting")
    @Log(title = "用户设置休假", businessType = LogBusinessType.UPDATE, operatorType = LogOperatorType.PC)
    public PcsResult vacationSetting(@Validated @RequestBody UserVacationSettingForm request) {

        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER,LoginUser.class);

        return adminUserService.vacationSetting(loginUser.getSysUser(),request);
    }


    @ApiOperation(value = "设置用户离职", notes = "设置用户离职")
    @PostMapping("/user/v1/resign/update")
    @Log(title = "用户设置休假", businessType = LogBusinessType.UPDATE, operatorType = LogOperatorType.PC)
    public PcsResult resignUpdate(@Validated @RequestBody UserResignSettingForm request) {

        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER,LoginUser.class);

        return adminUserService.resignUpdate(loginUser.getSysUser(),request);
    }


    @ApiOperation(value = "设置用户检测项", notes = "设置用户检测项")
    @PostMapping("/user/v1/inspectItem/update")
    @Log(title = "用户设置休假", businessType = LogBusinessType.UPDATE, operatorType = LogOperatorType.PC)
    public PcsResult inspectItemUpdate(@Validated @RequestBody UserInspectItemSettingForm request) {

        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER,LoginUser.class);

        return adminUserService.inspectItemUpdate(loginUser.getSysUser(),request);
    }

}
