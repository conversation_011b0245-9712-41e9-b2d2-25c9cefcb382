package com.lanhu.lims.gateway.admin.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/********************************
 * @title ReagentInventory
 * @package com.lanhu.lims.gateway.admin.model
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/17 19:40
 * @version 0.0.1
 *********************************/

@ApiModel(description = "试剂库存表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "t_reagent_inventory")
public class ReagentInventory {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 试剂瓶身ID
     */
    @TableField(value = "reagent_bottle_id")
    @ApiModelProperty(value = "试剂瓶身ID")
    private String reagentBottleId;

    /**
     * 试剂ID
     */
    @TableField(value = "reagent_id")
    @ApiModelProperty(value = "试剂ID")
    private Long reagentId;

    /**
     * 试剂规格ID
     */
    @TableField(value = "reagent_specification_id")
    @ApiModelProperty(value = "试剂规格ID")
    private Long reagentSpecificationId;

    /**
     * 存储位置id，多级位置，例如 1-2-3
     */
    @TableField(value = "store_position")
    @ApiModelProperty(value = "存储位置id，多级位置，例如 1-2-3")
    private String storePosition;

    /**
     * 入库总数,累加值，当入库时需要增加
     */
    @TableField(value = "inventory_total")
    @ApiModelProperty(value = "入库总数,累加值，当入库时需要增加")
    private BigDecimal inventoryTotal;

    /**
     * 库存余额，当入库时需要增加
     */
    @TableField(value = "inventory_balance")
    @ApiModelProperty(value = "库存余额，当入库时需要增加")
    private BigDecimal inventoryBalance;

    /**
     * 是否有效，0：正常，1：删除
     */
    @TableField(value = "is_effect")
    @ApiModelProperty(value = "是否有效，0：正常，1：删除")
    private Integer isEffect;

    /**
     * 创建人ID
     */
    @TableField(value = "create_by")
    @ApiModelProperty(value = "创建人ID")
    private Long createBy;

    /**
     * 创建人姓名
     */
    @TableField(value = "create_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createName;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 修改人ID
     */
    @TableField(value = "update_by")
    @ApiModelProperty(value = "修改人ID")
    private Long updateBy;

    /**
     * 修改人姓名
     */
    @TableField(value = "update_name")
    @ApiModelProperty(value = "修改人姓名")
    private String updateName;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 试剂编号
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "试剂编号")
    private String reagentCode;

    /**
     * 试剂名称
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "试剂名称")
    private String reagentName;

    /**
     * 试剂简称
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "试剂简称")
    private String shortName;

    /**
     * 品牌
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "品牌")
    private String brand;

    /**
     * 货号
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "货号")
    private String catalogNumber;

    /**
     * 规格
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "规格")
    private String specification;

    /**
     * 计量单位
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "计量单位")
    private String unit;
}