package com.lanhu.lims.gateway.admin.exception;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.lanhu.lims.gateway.admin.auth.exception.AuthException;
import com.lanhu.lims.gateway.admin.core.BusinessException;
import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.core.PcsResultCode;
import com.lanhu.lims.gateway.admin.core.Result;
import com.lanhu.lims.gateway.admin.file.exception.UploadException;
import com.lanhu.lims.gateway.admin.utils.MessageUtil;
import lombok.extern.slf4j.Slf4j;
import org.dromara.warm.flow.core.exception.FlowException;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MultipartException;
import org.springframework.web.multipart.support.MissingServletRequestPartException;
import org.springframework.web.servlet.NoHandlerFoundException;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;

/********************************
 * @title GlobalExceptionHandler
 * @package com.lanhu.gateway.client.exception
 * @description description
 *
 * @date 2022/11/17 2:19 下午
 * @version 0.0.1
 *********************************/
@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {


    @ExceptionHandler(value = BusinessException.class)
    @ResponseBody
    public <T> PcsResult<T> handler(BusinessException exception) {
        return this.assemblyPcsResult(PcsResultCode.convert(exception.getErrorCode()), exception.getMessage(), exception);
    }


    @ExceptionHandler(value = AuthException.class)
    @ResponseBody
    public <T> PcsResult<T> handler(AuthException exception) {
        return this.assemblyPcsResult(PcsResultCode.convert(exception.getErrorCode()), exception.getMessage(), exception);
    }

    @ExceptionHandler(value = UploadException.class)
    @ResponseBody
    public <T> PcsResult<T> handler(UploadException exception) {
        return this.assemblyPcsResult(PcsResultCode.convert(exception.getErrorCode()), exception.getMessage(), exception);
    }


    /**
     * 参数校验异常处理
     *
     * @param exception
     * @return
     */
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    @ResponseBody
    public  <T> PcsResult<T> handler(MethodArgumentNotValidException exception) {

        return this.assemblyPcsResult(PcsResultCode.PARAM_LOSE,ObjectUtil.defaultIfNull(exception.getBindingResult().getFieldError().getDefaultMessage(), PcsResultCode.PARAM_LOSE.getDesc()) , exception,exception.getBindingResult().getFieldError().getField());


    }

    @ExceptionHandler(value = BindException.class)
    @ResponseBody
    public <T> PcsResult<T> handler(BindException exception) {
        return this.assemblyPcsResult(PcsResultCode.PARAM_LOSE, ObjectUtil.defaultIfNull(exception.getBindingResult().getFieldError().getDefaultMessage(), PcsResultCode.PARAM_LOSE.getDesc()), exception,exception.getBindingResult().getFieldError().getField());
    }

    @ExceptionHandler(value = ConstraintViolationException.class)
    @ResponseBody
    public <T> PcsResult<T> handler(ConstraintViolationException exception) {
        ConstraintViolation<?> constraintViolation = exception.getConstraintViolations().iterator().next();
        return this.assemblyPcsResult(PcsResultCode.PARAM_LOSE, ObjectUtil.defaultIfNull(constraintViolation.getMessage(), PcsResultCode.PARAM_LOSE.getDesc()), exception);
    }




    @ExceptionHandler(value = HttpRequestMethodNotSupportedException.class)
    @ResponseBody
    public <T> PcsResult<T> handler(HttpRequestMethodNotSupportedException exception) {
        return this.assemblyPcsResult(PcsResultCode.METHOD_NOT_SUPPORT, StrUtil.EMPTY, exception);
    }

    @ExceptionHandler(value = NoHandlerFoundException.class)
    @ResponseBody
    public <T> PcsResult<T> handler(NoHandlerFoundException exception) {
        return this.assemblyPcsResult(PcsResultCode.NOT_FOUND_HANDLER, StrUtil.EMPTY, exception);
    }

    @ExceptionHandler(value = MissingServletRequestParameterException.class)
    @ResponseBody
    public <T> PcsResult<T> handler(MissingServletRequestParameterException exception) {
        return this.assemblyPcsResult(PcsResultCode.PARAM_LOSE, StrUtil.EMPTY, exception);
    }

    @ExceptionHandler(value = MissingServletRequestPartException.class)
    @ResponseBody
    public <T> PcsResult<T> handler(MissingServletRequestPartException exception) {
        return this.assemblyPcsResult(PcsResultCode.PARAM_LOSE, StrUtil.EMPTY, exception);
    }

    @ExceptionHandler(value = MultipartException.class)
    @ResponseBody
    public <T> PcsResult<T> handler(MultipartException exception) {
        return this.assemblyPcsResult(PcsResultCode.FILE_UPLOAD_ERROR, StrUtil.EMPTY, exception);
    }

    @ExceptionHandler(value = HttpMediaTypeNotSupportedException.class)
    @ResponseBody
    public <T> PcsResult<T> handler(HttpServletRequest req, HttpMediaTypeNotSupportedException exception) {
        return this.assemblyPcsResult(PcsResultCode.MEDIA_TYPE_NOT_SUPPORT,StrUtil.EMPTY, exception);
    }

    @ExceptionHandler(value = HttpMessageNotReadableException.class)
    @ResponseBody
    public <T> PcsResult<T> handler(HttpMessageNotReadableException exception) {
        return this.assemblyPcsResult(PcsResultCode.PARAM_LOSE,StrUtil.EMPTY, exception);
    }

    @ExceptionHandler(value = MethodArgumentTypeMismatchException.class)
    @ResponseBody
    public <T> PcsResult<T> handler(MethodArgumentTypeMismatchException exception) {
        return this.assemblyPcsResult(PcsResultCode.PARAM_TYPE_NOT_SUPPORT,  StrUtil.EMPTY, exception);
    }

    @ExceptionHandler(value = FlowException.class)
    @ResponseBody
    public <T> PcsResult<T> handler(FlowException exception) {
        return this.assemblyPcsResult(PcsResultCode.FLOW_EXCEPTION, exception);
    }




    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    public <T> PcsResult<T> handler(Exception exception) {
        return this.assemblyPcsResult(PcsResultCode.ERROR_OPERATE, StrUtil.EMPTY, exception);
    }






    // ================================================================================== 私有方法区 ================================================================================== //





    /**
     * @param message   异常信息
     * @param exception 异常类
     * @param args      占位符
     * @return
     * @ResultCode
     */
    private  <T> PcsResult<T> assemblyPcsResult(PcsResultCode pcsResultCode, String message, Exception exception, Object... args) {
        log.error("============>>>>>> 全局异常 <<<<<<============", exception);


        PcsResult<T> pcsResult = Result.error();
        if (ObjectUtil.isNotNull(pcsResultCode)) {
            pcsResult.setCode(pcsResultCode.getCode());
            pcsResult.setMessage(MessageUtil.message(pcsResultCode.getDesc(),args));
        }
        if (StrUtil.isNotBlank(message)) {
            pcsResult.setMessage(MessageUtil.message(message,args));
        }
        return pcsResult;


    }


    /**
     * @param exception 异常类
     * @return
     * @ResultCode
     */
    private  <T> PcsResult<T> assemblyPcsResult(PcsResultCode pcsResultCode, Exception exception) {
        log.error("============>>>>>> 流程引擎异常 <<<<<<============", exception);


        PcsResult<T> pcsResult = Result.error();

        if (ObjectUtil.isNotNull(pcsResultCode)) {
            pcsResult.setCode(pcsResultCode.getCode());
            pcsResult.setMessage(pcsResultCode.getDesc());
        }

        if (exception!=null) {
            pcsResult.setMessage(exception.getMessage());
        }


        return pcsResult;


    }
}
