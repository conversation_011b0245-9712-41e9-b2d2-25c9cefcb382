package com.lanhu.lims.gateway.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanhu.lims.gateway.admin.model.Dept;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface DeptMapper extends BaseMapper<Dept> {
    int updateBatch(List<Dept> list);

    int updateBatchSelective(List<Dept> list);

    int batchInsert(@Param("list") List<Dept> list);

    int insertOrUpdate(Dept record);

    int insertOrUpdateSelective(Dept record);


}