package com.lanhu.lims.gateway.admin.utils;

import cn.hutool.core.util.ObjectUtil;

import java.util.HashMap;
import java.util.Map;

public class MemberUtil {

    private static final Map<String, String[]> MEMBER_PAY_NAME_ARR_MAP = new HashMap<String, String[]>() {{
        put("zh", new String[]{"会员卡支付"});
        put("jp", new String[]{"会員カード払い"});
        put("en", new String[]{"会员卡支付"});
    }};

    public static String resolveLanguage(String local , int position) {
        Map<String, String[]> map = MEMBER_PAY_NAME_ARR_MAP;
        String[] arr = map.get(local);
        if (ObjectUtil.isNull(arr)) {
            return map.get("zh")[position];
        }
        return arr[position];
    }
}
