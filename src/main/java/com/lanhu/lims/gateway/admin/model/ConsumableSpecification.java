package com.lanhu.lims.gateway.admin.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/********************************
 * @title ConsumableSpecification
 * @package com.lanhu.lims.gateway.admin.model
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/17 13:48
 * @version 0.0.1
 *********************************/

@ApiModel(description = "耗材规格表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "t_consumable_specification")
public class ConsumableSpecification {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 关联耗材ID
     */
    @TableField(value = "consumable_id")
    @ApiModelProperty(value = "关联耗材ID")
    private Long consumableId;

    /**
     * 品牌
     */
    @TableField(value = "brand")
    @ApiModelProperty(value = "品牌")
    private String brand;

    /**
     * 货号
     */
    @TableField(value = "catalog_number")
    @ApiModelProperty(value = "货号")
    private String catalogNumber;

    /**
     * 规格
     */
    @TableField(value = "specification")
    @ApiModelProperty(value = "规格")
    private String specification;

    /**
     * 计量单位
     */
    @TableField(value = "unit")
    @ApiModelProperty(value = "计量单位")
    private String unit;

    /**
     * 存储条件
     */
    @TableField(value = "storage_condition")
    @ApiModelProperty(value = "存储条件")
    private String storageCondition;

    /**
     * 材质 玻璃..
     */
    @TableField(value = "material")
    @ApiModelProperty(value = "材质 玻璃..")
    private String material;

    /**
     * 是否有效，0：正常，1：删除
     */
    @TableField(value = "is_effect")
    @ApiModelProperty(value = "是否有效，0：正常，1：删除")
    private Integer isEffect;

    /**
     * 创建人ID
     */
    @TableField(value = "create_by")
    @ApiModelProperty(value = "创建人ID")
    private Long createBy;

    /**
     * 创建人姓名
     */
    @TableField(value = "create_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createName;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 修改人ID
     */
    @TableField(value = "update_by")
    @ApiModelProperty(value = "修改人ID")
    private Long updateBy;

    /**
     * 修改人姓名
     */
    @TableField(value = "update_name")
    @ApiModelProperty(value = "修改人姓名")
    private String updateName;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 审核状态(-2: 审核不通过 0:待审核 1: 审核中 2:审核通过/正常)
     */
    @TableField(value = "audit_status")
    @ApiModelProperty(value = "审核状态(-2: 审核不通过 0:待审核 1: 审核中 2:审核通过/正常)")
    private Integer auditStatus;

    /**
     * 最近一次审核人ID
     */
    @TableField(value = "audit_by")
    @ApiModelProperty(value = "最近一次审核人ID")
    private Long auditBy;

    /**
     * 最近一次审核时间
     */
    @TableField(value = "audit_time")
    @ApiModelProperty(value = "最近一次审核时间")
    private Date auditTime;

    /**
     * 最近一次审核人名称
     */
    @TableField(value = "audit_name")
    @ApiModelProperty(value = "最近一次审核人名称")
    private String auditName;

    /**
     * 审核备注/审核不通过原因
     */
    @TableField(value = "audit_remark")
    @ApiModelProperty(value = "审核备注/审核不通过原因")
    private String auditRemark;
}