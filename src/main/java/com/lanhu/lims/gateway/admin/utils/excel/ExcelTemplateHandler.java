package com.lanhu.lims.gateway.admin.utils.excel;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.idev.excel.ExcelWriter;
import cn.idev.excel.FastExcel;
import cn.idev.excel.write.metadata.WriteSheet;
import cn.idev.excel.write.metadata.fill.FillConfig;
import cn.idev.excel.write.metadata.fill.FillWrapper;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/********************************
 * @title ExcelTemplateHandler
 * @package com.lanhu.lims.gateway.admin.utils.excel
 * @description Excel模板处理工具类，支持模板填充、动态数据绑定、复杂表格生成等功能
 *
 * <AUTHOR>
 * @date 2025/1/27 10:50
 * @version 0.0.1
 *********************************/
@Slf4j
public class ExcelTemplateHandler {

    /**
     * 使用模板填充数据并导出到HTTP响应
     *
     * @param response            HTTP响应对象
     * @param templateInputStream 模板输入流
     * @param fillData            填充数据
     * @param fileName            文件名
     */
    public static void fillTemplateToResponse(HttpServletResponse response, InputStream templateInputStream, Map<String, Object> fillData, String fileName) {
        try {
            setResponseHeaders(response, fileName);
            fillTemplate(response.getOutputStream(), templateInputStream, fillData);
            response.flushBuffer();
        } catch (IOException e) {
            log.error("模板填充导出失败: {}", e.getMessage(), e);
            throw new RuntimeException("模板填充导出失败", e);
        } finally {
            IoUtil.close(templateInputStream);
        }
    }

    /**
     * 使用模板填充数据到输出流
     *
     * @param outputStream        输出流
     * @param templateInputStream 模板输入流
     * @param fillData            填充数据
     */
    public static void fillTemplate(OutputStream outputStream, InputStream templateInputStream, Map<String, Object> fillData) {
        try {
            ExcelWriter excelWriter = FastExcel.write(outputStream).withTemplate(templateInputStream).build();
            WriteSheet writeSheet = FastExcel.writerSheet().build();
            excelWriter.fill(fillData, writeSheet);
            excelWriter.finish();
        } catch (Exception e) {
            log.error("模板填充失败: {}", e.getMessage(), e);
            throw new RuntimeException("模板填充失败", e);
        } finally {
            IoUtil.close(templateInputStream);
        }
    }

    /**
     * 使用模板填充列表数据并导出到HTTP响应
     *
     * @param response            HTTP响应对象
     * @param templateInputStream 模板输入流
     * @param listData            列表数据
     * @param fileName            文件名
     */
    public static <T> void fillTemplateListToResponse(HttpServletResponse response, InputStream templateInputStream, List<T> listData, String fileName) {
        try {
            setResponseHeaders(response, fileName);
            fillTemplateList(response.getOutputStream(), templateInputStream, listData);
            response.flushBuffer();
        } catch (IOException e) {
            log.error("模板列表填充导出失败: {}", e.getMessage(), e);
            throw new RuntimeException("模板列表填充导出失败", e);
        } finally {
            IoUtil.close(templateInputStream);
        }
    }

    /**
     * 使用模板填充列表数据到输出流
     *
     * @param outputStream        输出流
     * @param templateInputStream 模板输入流
     * @param listData            列表数据
     */
    public static <T> void fillTemplateList(OutputStream outputStream, InputStream templateInputStream, List<T> listData) {
        try {
            // FastExcel 1.2.0的模板填充方式
            ExcelWriter excelWriter = FastExcel.write(outputStream).withTemplate(templateInputStream).build();
            WriteSheet writeSheet = FastExcel.writerSheet().build();

            // 填充列表数据
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            excelWriter.fill(new FillWrapper("data", listData), fillConfig, writeSheet);
            excelWriter.finish();
        } catch (Exception e) {
            log.error("模板列表填充失败: {}", e.getMessage(), e);
            throw new RuntimeException("模板列表填充失败", e);
        } finally {
            IoUtil.close(templateInputStream);
        }
    }

    /**
     * 使用模板填充复合数据（包含单个数据和列表数据）
     *
     * @param response            HTTP响应对象
     * @param templateInputStream 模板输入流
     * @param singleData          单个数据
     * @param listDataMap         列表数据映射
     * @param fileName            文件名
     */
    public static void fillComplexTemplateToResponse(HttpServletResponse response, InputStream templateInputStream, Map<String, Object> singleData, Map<String, List<?>> listDataMap, String fileName) {
        try {
            setResponseHeaders(response, fileName);
            fillComplexTemplate(response.getOutputStream(), templateInputStream, singleData, listDataMap);
            response.flushBuffer();
        } catch (IOException e) {
            log.error("复合模板填充导出失败: {}", e.getMessage(), e);
            throw new RuntimeException("复合模板填充导出失败", e);
        } finally {
            IoUtil.close(templateInputStream);
        }
    }

    /**
     * 使用模板填充复合数据到输出流
     *
     * @param outputStream        输出流
     * @param templateInputStream 模板输入流
     * @param singleData          单个数据
     * @param listDataMap         列表数据映射
     */
    public static void fillComplexTemplate(OutputStream outputStream, InputStream templateInputStream, Map<String, Object> singleData, Map<String, List<?>> listDataMap) {
        try {
            ExcelWriter excelWriter = FastExcel.write(outputStream).withTemplate(templateInputStream).build();
            WriteSheet writeSheet = FastExcel.writerSheet().build();

            // 填充单个数据
            if (ObjectUtil.isNotEmpty(singleData)) {
                excelWriter.fill(singleData, writeSheet);
            }

            // 填充列表数据
            if (ObjectUtil.isNotEmpty(listDataMap)) {
                FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
                for (Map.Entry<String, List<?>> entry : listDataMap.entrySet()) {
                    excelWriter.fill(new FillWrapper(entry.getKey(), entry.getValue()), fillConfig, writeSheet);
                }
            }

            excelWriter.finish();
        } catch (Exception e) {
            log.error("复合模板填充失败: {}", e.getMessage(), e);
            throw new RuntimeException("复合模板填充失败", e);
        } finally {
            IoUtil.close(templateInputStream);
        }
    }

    /**
     * 创建基础填充数据
     *
     * @param title      标题
     * @param exportTime 导出时间
     * @param exportUser 导出用户
     * @return 基础填充数据
     */
    public static Map<String, Object> createBaseFillData(String title, String exportTime, String exportUser) {
        Map<String, Object> fillData = new HashMap<>();
        fillData.put("title", StrUtil.blankToDefault(title, "数据报表"));
        fillData.put("exportTime", StrUtil.blankToDefault(exportTime, DateUtil.now()));
        fillData.put("exportUser", StrUtil.blankToDefault(exportUser, "系统"));
        return fillData;
    }

    /**
     * 创建统计填充数据
     *
     * @param totalCount   总数量
     * @param validCount   有效数量
     * @param invalidCount 无效数量
     * @return 统计填充数据
     */
    public static Map<String, Object> createStatisticsFillData(int totalCount, int validCount, int invalidCount) {
        Map<String, Object> fillData = new HashMap<>();
        fillData.put("totalCount", totalCount);
        fillData.put("validCount", validCount);
        fillData.put("invalidCount", invalidCount);
        fillData.put("validRate", totalCount > 0 ? String.format("%.2f%%", (double) validCount / totalCount * 100) : "0.00%");
        return fillData;
    }

    /**
     * 合并填充数据
     *
     * @param dataMaps 数据映射数组
     * @return 合并后的数据
     */
    @SafeVarargs
    public static Map<String, Object> mergeFillData(Map<String, Object>... dataMaps) {
        Map<String, Object> mergedData = new HashMap<>();
        if (ObjectUtil.isNotEmpty(dataMaps)) {
            for (Map<String, Object> dataMap : dataMaps) {
                if (ObjectUtil.isNotEmpty(dataMap)) {
                    mergedData.putAll(dataMap);
                }
            }
        }
        return mergedData;
    }

    /**
     * 创建多工作表模板填充数据
     *
     * @param sheetDataMap 工作表数据映射
     * @return 多工作表填充数据
     */
    public static Map<String, SheetFillData> createMultiSheetFillData(Map<String, SheetFillData> sheetDataMap) {
        return ObjectUtil.defaultIfNull(sheetDataMap, new HashMap<>());
    }

    /**
     * 使用模板填充多工作表数据
     *
     * @param response            HTTP响应对象
     * @param templateInputStream 模板输入流
     * @param sheetDataMap        工作表数据映射
     * @param fileName            文件名
     */
    public static void fillMultiSheetTemplateToResponse(HttpServletResponse response, InputStream templateInputStream, Map<String, SheetFillData> sheetDataMap, String fileName) {
        try {
            setResponseHeaders(response, fileName);
            fillMultiSheetTemplate(response.getOutputStream(), templateInputStream, sheetDataMap);
            response.flushBuffer();
        } catch (IOException e) {
            log.error("多工作表模板填充导出失败: {}", e.getMessage(), e);
            throw new RuntimeException("多工作表模板填充导出失败", e);
        } finally {
            IoUtil.close(templateInputStream);
        }
    }

    /**
     * 使用模板填充多工作表数据到输出流
     *
     * @param outputStream        输出流
     * @param templateInputStream 模板输入流
     * @param sheetDataMap        工作表数据映射
     */
    public static void fillMultiSheetTemplate(OutputStream outputStream, InputStream templateInputStream, Map<String, SheetFillData> sheetDataMap) {
        try {
            ExcelWriter excelWriter = FastExcel.write(outputStream).withTemplate(templateInputStream).build();

            for (Map.Entry<String, SheetFillData> entry : sheetDataMap.entrySet()) {
                String sheetName = entry.getKey();
                SheetFillData sheetData = entry.getValue();
                WriteSheet writeSheet = FastExcel.writerSheet(sheetName).build();

                // 填充单个数据
                if (ObjectUtil.isNotEmpty(sheetData.getSingleData())) {
                    excelWriter.fill(sheetData.getSingleData(), writeSheet);
                }

                // 填充列表数据
                if (ObjectUtil.isNotEmpty(sheetData.getListDataMap())) {
                    FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
                    for (Map.Entry<String, List<?>> listEntry : sheetData.getListDataMap().entrySet()) {
                        excelWriter.fill(new FillWrapper(listEntry.getKey(), listEntry.getValue()), fillConfig, writeSheet);
                    }
                }
            }

            excelWriter.finish();
        } catch (Exception e) {
            log.error("多工作表模板填充失败: {}", e.getMessage(), e);
            throw new RuntimeException("多工作表模板填充失败", e);
        } finally {
            IoUtil.close(templateInputStream);
        }
    }

    /**
     * 设置HTTP响应头
     *
     * @param response HTTP响应对象
     * @param fileName 文件名
     */
    private static void setResponseHeaders(HttpServletResponse response, String fileName) throws IOException {
        String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
    }

    /**
     * 工作表填充数据类
     */
    @Setter
    @Getter
    public static class SheetFillData {
        private Map<String, Object> singleData;
        private Map<String, List<?>> listDataMap;

        public SheetFillData() {
            this.singleData = new HashMap<>();
            this.listDataMap = new HashMap<>();
        }

        public SheetFillData(Map<String, Object> singleData, Map<String, List<?>> listDataMap) {
            this.singleData = ObjectUtil.defaultIfNull(singleData, new HashMap<>());
            this.listDataMap = ObjectUtil.defaultIfNull(listDataMap, new HashMap<>());
        }

        public SheetFillData addSingleData(String key, Object value) {
            this.singleData.put(key, value);
            return this;
        }

        public SheetFillData addListData(String key, List<?> value) {
            this.listDataMap.put(key, value);
            return this;
        }
    }
}
