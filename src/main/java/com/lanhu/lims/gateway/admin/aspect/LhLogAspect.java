package com.lanhu.lims.gateway.admin.aspect;

import cn.hutool.core.util.ArrayUtil;
import com.lanhu.lims.gateway.admin.utils.JacksonUtils;
import com.lanhu.lims.gateway.admin.utils.NetUtil;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/********************************
 * @title LhLogAspect
 * @package com.lanhu.imenu.gateway.client.aspect
 * @description description
 *
 * @date 2022/11/17 2:19 下午
 * @version 0.0.1
 *********************************/
@Aspect
@Component
public class LhLogAspect {


    private static final Logger LOG = LoggerFactory.getLogger(LhLogAspect.class);



    @Value("${log.print.response:false}")
    private boolean printResponse;


    @Value("${log.print.request:true}")
    private boolean printRequest;



    @Pointcut("execution(public * com.lanhu.lims.gateway.admin.controller.*.*(..))")
    public void controllerPointcut() {

    }

    @Pointcut("execution(public * com.lanhu.lims.gateway.admin.flow.controller.*.*(..))")
    public void flowControllerPointcut() {

    }




    /**
     * 在切点之前织入
     *
     * @param joinPoint 切点
     */
    @Before("controllerPointcut()||flowControllerPointcut()")
    public void doBefore(JoinPoint joinPoint) {
        // 开始打印请求日志
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = Objects.requireNonNull(attributes).getRequest();
        String requestUrl = request.getRequestURL().toString();
        String language = request.getHeader("LANGUAGE");

        // 打印请求相关参数
        LOG.info("========================================== Start ==========================================");
        // 打印请求 url
        LOG.info("URL                       : {}", requestUrl);
        // 打印 Http method
        LOG.info("HTTP Method               : {}", request.getMethod());
        // 打印调用 controller 的全路径以及执行方法
        LOG.info("Class Method              : {}.{}", joinPoint.getSignature().getDeclaringTypeName(), joinPoint.getSignature().getName());
        // 打印请求的 IP
        LOG.info("IP                        : {}", NetUtil.getRemoteIpAddr(request));
        // 打印请求的 语言
        LOG.info("Language                  : {}",language);

        if(printRequest){
            // 打印请求入参
            Object[] args = joinPoint.getArgs();
            List<Object> filterArgs = filterArgs(args);
            if (filterArgs.size() > 0) {
                LOG.info("Request Args              : {}", serialization(filterArgs));
            } else {
                LOG.info("Request Args              : {}", "[]");
            }
        }

    }

    /**
     * 环绕
     *
     * @param proceedingJoinPoint 切点
     * @return object
     * @throws Throwable 异常
     */
    @Around("controllerPointcut()||flowControllerPointcut()")
    public Object doAround(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Object result = proceedingJoinPoint.proceed();
        stopWatch.stop();
        long timeConsuming = stopWatch.getTotalTimeMillis();

        if(printResponse){
            // 打印出参
            LOG.info("Response Args             : {}", serialization(result));
            // 执行耗时
            LOG.info("Time-Consuming            : {} ms", timeConsuming);
        }


        // 接口结束后换行，方便分割查看
        LOG.info("=========================================== End ===========================================");
        return result;
    }

    // ======================================================== 私用方法 ======================================================== //

    /**
     * 序列化
     *
     * @param obj 待序列化对象
     * @return 序列化的json字符串
     */
    private String serialization(Object obj) {
        try {
            return JacksonUtils.serialize(obj);
        } catch (Exception e) {
            LOG.error("JSON序列化出现错误！", e);
            return "json serialization error";
        }
    }

    /**
     * 过滤不可序列化的参数
     *
     * @param args 参数
     * @return 过滤后的参数
     */
    private List<Object> filterArgs(Object[] args) {
        return streamOf(args)
                .filter(arg -> (!(arg instanceof HttpServletRequest) && !(arg instanceof HttpServletResponse)))
                .collect(Collectors.toList());
    }

    /**
     * stream流
     *
     * @param array 数组
     * @param <T>   类型
     * @return stream流
     */
    private static <T> Stream<T> streamOf(T[] array) {
        return ArrayUtil.isEmpty(array) ? Stream.empty() : Arrays.stream(array);
    }
}
