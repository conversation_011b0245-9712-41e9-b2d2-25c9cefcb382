package com.lanhu.lims.gateway.admin.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.lanhu.lims.gateway.admin.annotation.LhTransaction;
import com.lanhu.lims.gateway.admin.auth.constants.SecurityConstants;
import com.lanhu.lims.gateway.admin.auth.context.SecurityContextHolder;
import com.lanhu.lims.gateway.admin.auth.utils.SecurityUtils;
import com.lanhu.lims.gateway.admin.auth.utils.StringUtils;
import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.core.PcsResultCode;
import com.lanhu.lims.gateway.admin.core.ProjectConstant;
import com.lanhu.lims.gateway.admin.core.Result;
import com.lanhu.lims.gateway.admin.flow.enums.FlowDefinitionEnum;
import com.lanhu.lims.gateway.admin.flow.enums.FlowSkipTypeEnum;
import com.lanhu.lims.gateway.admin.mapper.BusinessFlowApplicationMapper;
import com.lanhu.lims.gateway.admin.model.AdminUser;
import com.lanhu.lims.gateway.admin.model.BusinessFlowApplication;
import com.lanhu.lims.gateway.admin.model.Leave;
import com.lanhu.lims.gateway.admin.model.LoginUser;
import com.lanhu.lims.gateway.admin.vo.req.LeaveAddForm;
import com.lanhu.lims.gateway.admin.vo.req.LeaveHandleForm;
import com.lanhu.lims.gateway.admin.vo.req.LeaveRejectLastForm;
import org.dromara.warm.flow.core.FlowEngine;
import org.dromara.warm.flow.core.dto.FlowParams;
import org.dromara.warm.flow.core.entity.*;
import org.dromara.warm.flow.core.service.DefService;
import org.dromara.warm.flow.core.service.InsService;
import org.dromara.warm.flow.core.service.TaskService;
import org.dromara.warm.flow.core.utils.IdUtils;
import org.dromara.warm.flow.orm.entity.FlowDefinition;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @description: 请假业务相关服务
 * @author: huangzheng
 * @date: 2025/5/7 16:48
 */

@Service
public class LeaveService {


    @Resource
    private BusinessFlowApplicationMapper businessFlowApplicationMapper;

    @Resource
    private InsService insService;

    @Resource
    private TaskService taskService;

    @Resource
    private DefService defService;




    @LhTransaction
    @DS("master_1")
    public PcsResult addLeave(LeaveAddForm leaveAddForm, AdminUser adminUser) {

        BusinessFlowApplication leaveFlowApplication = new BusinessFlowApplication();
        // 设置流转参数
        String id = IdUtils.nextIdStr();
        leaveFlowApplication.setId(Long.valueOf(id));

        Integer category = leaveAddForm.getCategory();
        // leaveType 对应流程定义的category  根据leaveType获取流程编码
        Definition definition = defService.getOne(new FlowDefinition().setCategory(category.toString()).setIsPublish(FlowDefinitionEnum.PUBLISH.getCode()));
        String flowCode = definition.getFlowCode() ;
        // 传递流程编码，绑定流程定义 【必传】
        FlowParams flowParams = FlowParams.build().flowCode(flowCode);
        // 设置办理人唯一标识，保存为流程实例的创建人 【必传】
        flowParams.handler(adminUser.getId().toString());
        // 流程变量
        Map<String, Object> variable = new HashMap<>();
        // 流程变量传递业务数据，按实际业务需求传递 【按需传】

        // 条件表达式替换，判断是否满足某个任务的跳转条件  【按需传】
        variable.put(ProjectConstant.FLAG, String.valueOf(leaveAddForm.getLeaveDays()));


        flowParams.variable(variable);

        // 发起流程
        Instance instance = insService.start(id, flowParams);

        leaveFlowApplication.setInstanceId(instance.getId());
        leaveFlowApplication.setNodeCode(instance.getNodeCode());
        leaveFlowApplication.setNodeName(instance.getNodeName());
        leaveFlowApplication.setNodeType(instance.getNodeType());
        leaveFlowApplication.setFlowStatus(Integer.valueOf(instance.getFlowStatus()));
        leaveFlowApplication.setCreateTime(DateUtil.date());
        leaveFlowApplication.setCreateBy(adminUser.getId());
        leaveFlowApplication.setBusType(category);
        leaveFlowApplication.setUpdateBy(adminUser.getId());

        Leave leave = BeanUtil.copyProperties(leaveAddForm, Leave.class);
        leave.setId(Long.valueOf(leaveFlowApplication.getId()));
        leave.setLeaveUser(adminUser.getRealName());

//        leaveFlowApplication.setBusInfo(JSONUtil.toJsonStr(leave));

        // 新增抄送人方法  【按需】
        if (CollectionUtil.isNotEmpty(leaveAddForm.getAdditionalHandler())) {
            List<User> users = FlowEngine.userService().structureUser(instance.getId()
                    , leaveAddForm.getAdditionalHandler(), "4");
            FlowEngine.userService().saveBatch(users);
        }

        businessFlowApplicationMapper.insert(leaveFlowApplication);


        // TODO 此处可以发送消息通知，比如短信通知，邮件通知等，代码自己实现
        return Result.ok();

    }


    @LhTransaction
    @DS("master_1")
    public PcsResult handleLeave(LeaveHandleForm leaveHandleForm) {

        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        AdminUser adminUser =  loginUser.getSysUser();
        // 设置流转参数
        // 是通过流程还是退回流程 【必传】
        String skipType = leaveHandleForm.getSkipType();
        FlowParams flowParams = FlowParams.build().skipType(skipType);

        flowParams.handler(adminUser.getId().toString());

        // 如果需要任意跳转流程，传入此参数 ，此处需要判断
        // 驳回节点的节点是否在当前节点之后，如果在之后不允许驳回，
        // 通过的节点是否在当前节点之前，如果在之前不允许操作


        String skipNodeCode = leaveHandleForm.getNodeCode();


        if (StringUtils.isNotBlank(skipNodeCode)) {
            Task curTask = FlowEngine.taskService().getById(leaveHandleForm.getTaskId());
            String curNodeCode = curTask.getNodeCode();
            // 根据当前任务id获取流程定义id
            Long definitionId = FlowEngine.taskService().getById(leaveHandleForm.getTaskId()).getDefinitionId();

            if (skipType.equals(FlowSkipTypeEnum.PASS.getCode())) {
                // 判断通过的节点是否在当前节点之前，在之前不允许操作
                List<Node> preNodes = FlowEngine.nodeService().previousNodeList(definitionId, curNodeCode);

                Set<String> preNodeCodes = preNodes.stream().map(Node::getNodeCode).collect(Collectors.toSet());

                if(preNodeCodes.contains(skipNodeCode)){
                    return Result.error(PcsResultCode.PASS_TO_PREVIOUS_NODE);
                }
            }


            if (skipType.equals(FlowSkipTypeEnum.REJECT.getCode())) {
                List<Node> sufNodes = FlowEngine.nodeService().suffixNodeList(definitionId, curNodeCode);

                Set<String> suffixNodeCodes = sufNodes.stream().map(Node::getNodeCode).collect(Collectors.toSet());

                if (suffixNodeCodes.contains(skipNodeCode)) {
                    // 驳回节点在当前节点之后
                    return Result.error(PcsResultCode.REJECT_TO_SUFFIX_NODE);
                }
            }

            // 加入跳转节点
            flowParams.nodeCode(skipNodeCode);

        }


        // 作为审批意见保存到历史记录表  【按需传】
        flowParams.message(leaveHandleForm.getMessage());

        // 设置办理人拥有的权限，办理中需要校验是否有权限办理
        List<String> flowPermissions = loginUser.getFlowPermissions();
        flowParams.permissionFlag(flowPermissions);

        BusinessFlowApplication businessFlowApplication = businessFlowApplicationMapper.selectById(leaveHandleForm.getBusinessId());

        // 流程变量
        Map<String, Object> variable = new HashMap<>();
        // 流程变量传递业务数据，按实际业务需求传递  【按需传】
//        variable.put("businessType", "Leave");
        // 办理人表达式替换  【按需传】
        variable.put(ProjectConstant.FLAG, String.valueOf(leaveHandleForm.getLeaveDays()));
        flowParams.variable(variable);

        // 请假信息存入flowParams,方便查看历史审批数据  【按需传】
        flowParams.hisTaskExt(JSON.toJSONString(businessFlowApplication));
        Instance instance = taskService.skip(leaveHandleForm.getTaskId(), flowParams);

        // 更新业务表
        businessFlowApplication.setNodeCode(instance.getNodeCode());
        businessFlowApplication.setNodeName(instance.getNodeName());
        businessFlowApplication.setNodeType(instance.getNodeType());
        businessFlowApplication.setFlowStatus(Integer.valueOf(instance.getFlowStatus()));
        businessFlowApplication.setUpdateBy(adminUser.getId());
        businessFlowApplicationMapper.updateById(businessFlowApplication);

        return Result.ok();
    }


    @LhTransaction
    @DS("master_1")
    public PcsResult rejectLast(LeaveRejectLastForm rejectLastForm) {
        // 设置流转参数
        LoginUser user = SecurityUtils.getLoginUser();

        FlowParams flowParams = FlowParams.build();
        // 作为办理人保存到历史记录表 【必传】
        flowParams.handler(user.getUserId().toString());
        // 作为审批意见保存到历史记录表  【按需传】
        flowParams.message(rejectLastForm.getMessage());

        // 流程变量
        Map<String, Object> variable = new HashMap<>();

        variable.put(ProjectConstant.FLAG, String.valueOf(rejectLastForm.getDays()));
        flowParams.variable(variable);

        BusinessFlowApplication businessFlowApplication = businessFlowApplicationMapper.selectById(rejectLastForm.getBusinessId());


        // 请假信息存入flowParams,方便查看历史审批数据  【按需传】
        flowParams.hisTaskExt(JSON.toJSONString(businessFlowApplication));
        Instance instance = taskService.rejectLast(rejectLastForm.getTaskId(), flowParams);

        // 更新业务表
        businessFlowApplication.setNodeCode(instance.getNodeCode());
        businessFlowApplication.setNodeName(instance.getNodeName());
        businessFlowApplication.setNodeType(instance.getNodeType());
        businessFlowApplication.setFlowStatus(Integer.valueOf(instance.getFlowStatus()));
        businessFlowApplicationMapper.updateById(businessFlowApplication);

        return Result.ok();
    }
















}
