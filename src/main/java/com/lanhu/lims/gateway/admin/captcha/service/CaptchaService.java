package com.lanhu.lims.gateway.admin.captcha.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.lanhu.lims.gateway.admin.captcha.enums.CaptchaModeEnum;
import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.core.ProjectConstant;
import com.lanhu.lims.gateway.admin.core.Result;
import com.lanhu.lims.gateway.admin.mapper.CaptchaMapper;
import com.lanhu.lims.gateway.admin.model.Captcha;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfigureOrder;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2025/5/22 12:23
 */

@Service
@Slf4j
public class CaptchaService {


    @Autowired
    private CaptchaMapper captchaMapper;

    /**
     * @description: 发送图形验证码
     * @param: [captchaSendMailForm]
     * @return: void
     * @author: liuyi
     * @date: 3:20 下午 2023/1/12
     */

    @DS("master_1")
    public PcsResult<String> sendPicCaptcha(String captchaCode){


        Captcha captcha = Captcha.builder()
                .id(IdUtil.getSnowflakeNextId())
                .uid(IdUtil.fastSimpleUUID())
                .captcha(captchaCode)
                .createAt(DateUtil.date())
                .mobile(ProjectConstant.EMPTY)
                .email(ProjectConstant.EMPTY)
                .captchaMode(CaptchaModeEnum.PIC.getMode())
                .captchaPeriod(120)
                .useCounter(0)
                .build();

        captcha.setExpireTime(DateUtil.offsetSecond(captcha.getCreateAt(), 120));

        captchaMapper.insert(captcha);


        return Result.ok(captcha.getUid());

    }
}
