package com.lanhu.lims.gateway.admin.utils;

import java.time.DateTimeException;

/**
 * 时间段
 * @auth: huh<PERSON>chun
 * @version: 1.0.0
 * @date: 2022/10/31 17:09
 */
public class TimePairUtil {
    public TimePairUtil(long start, long end) {
        if(end<start){
            throw new DateTimeException("结束时间不能小于开始时间");
        }
        this.start = start;
        this.end = end;
    }

    private long start;

    private long end;

    public long getStart() {
        return start;
    }

    public void setStart(long start) {
        this.start = start;
    }

    public long getEnd() {
        return end;
    }

    public void setEnd(long end) {
        this.end = end;
    }
}
