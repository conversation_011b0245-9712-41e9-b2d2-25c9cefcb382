package com.lanhu.lims.gateway.admin.controller;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanhu.lims.gateway.admin.annotation.Log;
import com.lanhu.lims.gateway.admin.auth.constants.SecurityConstants;
import com.lanhu.lims.gateway.admin.auth.context.SecurityContextHolder;
import com.lanhu.lims.gateway.admin.core.BaseController;
import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.core.Result;
import com.lanhu.lims.gateway.admin.enums.LogBusinessType;
import com.lanhu.lims.gateway.admin.enums.LogOperatorType;
import com.lanhu.lims.gateway.admin.model.LoginUser;
import com.lanhu.lims.gateway.admin.service.SystemFileService;
import com.lanhu.lims.gateway.admin.service.SystemFileTypeService;
import com.lanhu.lims.gateway.admin.vo.req.*;
import com.lanhu.lims.gateway.admin.vo.resp.SystemFileDetailVO;
import com.lanhu.lims.gateway.admin.vo.resp.SystemFileListVO;
import com.lanhu.lims.gateway.admin.vo.resp.SystemFileTypeDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/********************************
 * @title SystemFileController
 * @package com.lanhu.lims.gateway.admin.controller
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/10 00:19
 * @version 0.0.1
 *********************************/
@RestController
@Api(tags = "文件管理相关接口", value = "文件管理相关接口")
public class SystemFileController extends BaseController {
    @Resource
    private SystemFileTypeService systemFileTypeService;

    @Resource
    private SystemFileService systemFileService;

    // ================================== 文件类型管理接口 ==================================

    /**
     * 查询文件类型树形列表
     */
    @ApiOperation(value = "查询文件类型树形列表")
    @PostMapping("/systemFile/type/v1/treeList")
    public PcsResult<List<Tree<Long>>> typeTreeList() {
        return Result.ok(systemFileTypeService.treeList());
    }

    /**
     * 查询文件类型详情
     */
    @ApiOperation(value = "查询文件类型详情")
    @PostMapping("/systemFile/type/v1/detail")
    public PcsResult<SystemFileTypeDetailVO> typeDetail(@Validated @RequestBody SystemFileTypeSingleForm form) {
        return Result.ok(systemFileTypeService.detail(form));
    }

    /**
     * 新增文件类型
     */
    @ApiOperation(value = "新增文件类型")
    @PostMapping("/systemFile/type/v1/add")
    @Log(title = "新增文件类型", businessType = LogBusinessType.INSERT, operatorType = LogOperatorType.PC)
    public PcsResult<Void> typeAdd(@Validated @RequestBody SystemFileTypeAddForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        systemFileTypeService.add(form, loginUser.getSysUser());
        return Result.ok();
    }

    /**
     * 修改文件类型
     */
    @ApiOperation(value = "修改文件类型")
    @PostMapping("/systemFile/type/v1/edit")
    @Log(title = "修改文件类型", businessType = LogBusinessType.UPDATE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> typeEdit(@Validated @RequestBody SystemFileTypeEditForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        systemFileTypeService.edit(form, loginUser.getSysUser());
        return Result.ok();
    }

    /**
     * 删除文件类型
     */
    @ApiOperation(value = "删除文件类型")
    @PostMapping("/systemFile/type/v1/del")
    @Log(title = "删除文件类型", businessType = LogBusinessType.LOGICDELETE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> typeDel(@Validated @RequestBody SystemFileTypeSingleForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        systemFileTypeService.del(form, loginUser.getSysUser());
        return Result.ok();
    }

    // ================================== 文件管理接口 ==================================

    /**
     * 查询文件列表
     */
    @ApiOperation(value = "查询文件列表")
    @PostMapping("/systemFile/v1/list")
    public PcsResult<List<SystemFileListVO>> fileList(@Validated @RequestBody SystemFileListForm form) {
        return Result.ok(systemFileService.list(form));
    }

    /**
     * 查询文件分页列表
     */
    @ApiOperation(value = "查询文件分页列表")
    @PostMapping("/systemFile/v1/listPage")
    public PcsResult<IPage<SystemFileListVO>> fileListPage(@Validated @RequestBody SystemFileListPageForm form) {
        return Result.ok(systemFileService.listPage(form));
    }

    /**
     * 查询文件详情
     */
    @ApiOperation(value = "查询文件详情")
    @PostMapping("/systemFile/v1/detail")
    public PcsResult<SystemFileDetailVO> fileDetail(@Validated @RequestBody SystemFileSingleForm form) {
        return Result.ok(systemFileService.detail(form));
    }

    /**
     * 新增文件
     */
    @ApiOperation(value = "新增文件")
    @PostMapping("/systemFile/v1/add")
    @Log(title = "新增文件", businessType = LogBusinessType.INSERT, operatorType = LogOperatorType.PC)
    public PcsResult<Void> fileAdd(@Validated @RequestBody SystemFileAddForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        systemFileService.add(form, loginUser.getSysUser());
        return Result.ok();
    }

    /**
     * 修改文件
     */
    @ApiOperation(value = "修改文件")
    @PostMapping("/systemFile/v1/edit")
    @Log(title = "修改文件", businessType = LogBusinessType.UPDATE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> fileEdit(@Validated @RequestBody SystemFileEditForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        systemFileService.edit(form, loginUser.getSysUser());
        return Result.ok();
    }

    /**
     * 删除文件
     */
    @ApiOperation(value = "删除文件")
    @PostMapping("/systemFile/v1/del")
    @Log(title = "删除文件", businessType = LogBusinessType.LOGICDELETE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> fileDel(@Validated @RequestBody SystemFileDelForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        systemFileService.del(form, loginUser.getSysUser());
        return Result.ok();
    }

    // ================================== 文件版本管理接口 ==================================

    /**
     * 新增文件版本
     */
    @ApiOperation(value = "新增文件版本")
    @PostMapping("/systemFile/version/v1/add")
    @Log(title = "新增文件版本", businessType = LogBusinessType.INSERT, operatorType = LogOperatorType.PC)
    public PcsResult<Void> fileAddVersion(@Validated @RequestBody SystemFileVersionAddForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        systemFileService.addVersion(form, loginUser.getSysUser());
        return Result.ok();
    }
}
