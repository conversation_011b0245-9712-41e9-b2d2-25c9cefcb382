package com.lanhu.lims.gateway.admin.utils.change;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/********************************
 * @title FieldChangeItem
 * @package com.lanhu.lims.gateway.admin.utils.change
 * @description 字段变化项
 *
 * <AUTHOR>
 * @date 2025/6/8 16:36
 * @version 1.0.0
 *********************************/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FieldChangeItem {
    
    /**
     * 字段名
     */
    private String fieldName;
    
    /**
     * 字段显示名（中文注释）
     */
    private String fieldDisplayName;
    
    /**
     * 更新前的值
     */
    private Object oldValue;
    
    /**
     * 更新后的值
     */
    private Object newValue;
    
    /**
     * 字段类型
     */
    private String fieldType;
    
    /**
     * 是否发生变化
     */
    private boolean changed;
    
    /**
     * 获取格式化的变化描述
     * 
     * @return 格式化的变化描述，例如："字段：文件类型名，更新前：设备使用规范，更新后：设备使用手册"
     */
    public String getFormattedChange() {
        if (!changed) {
            return null;
        }
        String displayName = fieldDisplayName != null ? fieldDisplayName : fieldName;
        String oldValueStr = oldValue != null ? oldValue.toString() : "空";
        String newValueStr = newValue != null ? newValue.toString() : "空";
        return String.format("字段:【%s】 更新前:【%s】 更新后:【%s】", displayName, oldValueStr, newValueStr);
    }
    
    /**
     * 获取简化的变化描述
     * 
     * @return 简化的变化描述
     */
    public String getSimpleChange() {
        if (!changed) {
            return null;
        }
        String displayName = fieldDisplayName != null ? fieldDisplayName : fieldName;
        String oldValueStr = oldValue != null ? oldValue.toString() : "空";
        String newValueStr = newValue != null ? newValue.toString() : "空";
        return String.format("%s: %s → %s", displayName, oldValueStr, newValueStr);
    }
}
