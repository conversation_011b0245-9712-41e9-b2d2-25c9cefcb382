package com.lanhu.lims.gateway.admin.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanhu.lims.gateway.admin.core.BusinessException;
import com.lanhu.lims.gateway.admin.core.PcsResultCode;
import com.lanhu.lims.gateway.admin.enums.*;
import com.lanhu.lims.gateway.admin.mapper.*;
import com.lanhu.lims.gateway.admin.model.*;
import com.lanhu.lims.gateway.admin.utils.ConvertUtil;
import com.lanhu.lims.gateway.admin.utils.sequence.SequenceNameEnum;
import com.lanhu.lims.gateway.admin.utils.sequence.SequenceUtil;
import com.lanhu.lims.gateway.admin.vo.req.*;
import com.lanhu.lims.gateway.admin.vo.resp.EquipmentDetailVO;
import com.lanhu.lims.gateway.admin.vo.resp.EquipmentListVO;
import com.lanhu.lims.gateway.admin.vo.resp.MaterialAttachmentVO;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/********************************
 * @title EquipmentService
 * @package com.lanhu.lims.gateway.admin.service
 * @description 仪器设备服务类
 *
 * <AUTHOR>
 * @date 2025/6/14 22:25
 * @version 0.0.1
 *********************************/
@Service
public class EquipmentService {
    @Resource
    private EquipmentMapper equipmentMapper;

    @Resource
    private EquipmentMaintenanceRecordMapper equipmentMaintenanceRecordMapper;

    @Resource
    private DetectionMethodMapper detectionMethodMapper;

    @Resource
    private DictDataMapper dictDataMapper;

    @Resource
    private FileRecordMapper fileRecordMapper;

    @Resource
    private MaterialAttachmentMapper materialAttachmentMapper;

    @Resource(name = "commonExecutor")
    private ThreadPoolTaskExecutor commonExecutor;

    /**
     * 查询仪器设备列表
     *
     * @param form 查询入参
     * @return 列表
     */
    @DS("slave_1")
    public List<EquipmentListVO> list(EquipmentListForm form) {
        LambdaQueryWrapper<Equipment> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Equipment::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.like(StrUtil.isNotBlank(form.getEquipmentCode()), Equipment::getEquipmentCode, form.getEquipmentCode());
        wrapper.like(StrUtil.isNotBlank(form.getEquipmentName()), Equipment::getEquipmentName, form.getEquipmentName());
        wrapper.like(StrUtil.isNotBlank(form.getBrand()), Equipment::getBrand, form.getBrand());
        wrapper.like(StrUtil.isNotBlank(form.getModel()), Equipment::getModel, form.getModel());
        wrapper.eq(form.getStatus() != null, Equipment::getStatus, form.getStatus());
        wrapper.orderByDesc(Equipment::getCreateTime);
        List<Equipment> equipmentList = equipmentMapper.selectList(wrapper);
        return convertList2VoList(equipmentList);
    }

    /**
     * 分页查询仪器设备
     *
     * @param form 查询入参
     * @return 分页列表
     */
    @DS("slave_1")
    public IPage<EquipmentListVO> listPage(EquipmentListPageForm form) {
        LambdaQueryWrapper<Equipment> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Equipment::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.like(StrUtil.isNotBlank(form.getEquipmentCode()), Equipment::getEquipmentCode, form.getEquipmentCode());
        wrapper.like(StrUtil.isNotBlank(form.getEquipmentName()), Equipment::getEquipmentName, form.getEquipmentName());
        wrapper.like(StrUtil.isNotBlank(form.getBrand()), Equipment::getBrand, form.getBrand());
        wrapper.like(StrUtil.isNotBlank(form.getModel()), Equipment::getModel, form.getModel());
        wrapper.eq(form.getStatus() != null, Equipment::getStatus, form.getStatus());
        wrapper.eq(form.getOwnershipStatus() != null, Equipment::getOwnershipStatus, form.getOwnershipStatus());
        wrapper.orderByDesc(Equipment::getCreateTime);
        Page<Equipment> page = new Page<>(form.getPageIndex(), form.getPageSize());
        IPage<Equipment> pageInfo = equipmentMapper.selectPage(page, wrapper);
        return ConvertUtil.createPageWithRecords(convertList2VoList(pageInfo.getRecords()), pageInfo);
    }

    /**
     * 查询仪器设备详情
     *
     * @param form 查询入参
     * @return 详情
     */
    @DS("slave_1")
    public EquipmentDetailVO detail(EquipmentSingleForm form) {
        Equipment equipment = equipmentMapper.selectById(form.getId());
        if (equipment == null || ObjectUtil.notEqual(IsEffectEnum.NORMAL.getCode(), equipment.getIsEffect())) {
            throw new BusinessException(PcsResultCode.EQUIPMENT_NOT_EXIST);
        }
        return ConvertUtil.convert(equipment, entity -> {
            EquipmentDetailVO vo = BeanUtil.copyProperties(entity, EquipmentDetailVO.class);
            if (StrUtil.isNotBlank(entity.getUsage())) {
                vo.setUsage(Arrays.stream(entity.getUsage().split(",")).collect(Collectors.toList()));
            }
            if (StrUtil.isNotBlank(entity.getInspectItem())) {
                vo.setInspectItem(Arrays.stream(entity.getInspectItem().split("/")).map(Long::valueOf).collect(Collectors.toList()));
            }
            // 查询附件
            List<MaterialAttachmentVO> attachmentVos = ConvertUtil.convertList(materialAttachmentMapper.selectList(Wrappers.<MaterialAttachment>lambdaQuery().eq(MaterialAttachment::getMaterialId, vo.getId()).orderByDesc(MaterialAttachment::getCreateTime)), MaterialAttachmentVO.class);
            vo.setAttachments(attachmentVos);
            return vo;
        });
    }

    /**
     * 新增仪器设备
     *
     * @param form      新增入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void add(EquipmentAddForm form, AdminUser adminUser) {
        // 生成设备编号
        String equipmentCode = SequenceUtil.getNextFormattedValue(SequenceNameEnum.EQUIPMENT_CODE);
        // 获取文件记录
        List<FileRecord> fileRecords = CollUtil.newArrayList();
        if (CollUtil.isNotEmpty(form.getFileRecordIds())) {
            for (Long fileRecordId : form.getFileRecordIds()) {
                FileRecord fileRecord = fileRecordMapper.selectById(fileRecordId);
                if (fileRecord == null) {
                    throw new BusinessException(PcsResultCode.FILE_RECORD_NOT_EXIST);
                }
                fileRecords.add(fileRecord);
            }
        }
        // 新增仪器设备
        Equipment equipment = new Equipment();
        equipment.setEquipmentCode(equipmentCode);
        equipment.setEquipmentName(form.getEquipmentName());
        equipment.setUsage(CollUtil.isNotEmpty(form.getUsage()) ? StrUtil.join(",", form.getUsage()) : StrUtil.EMPTY);
        equipment.setBrand(StrUtil.blankToDefault(form.getBrand(), StrUtil.EMPTY));
        equipment.setModel(StrUtil.blankToDefault(form.getModel(), StrUtil.EMPTY));
        equipment.setOwnershipStatus(form.getOwnershipStatus());
        equipment.setInspectItem(CollUtil.isNotEmpty(form.getInspectItem()) ? StrUtil.join("/", form.getInspectItem()) : StrUtil.EMPTY);
        equipment.setConsignee(StrUtil.blankToDefault(form.getConsignee(), StrUtil.EMPTY));
        equipment.setReceiptDate(StrUtil.blankToDefault(form.getReceiptDate(), StrUtil.EMPTY));
        equipment.setStatus(EquipmentStatusEnum.NORMAL.getCode());
        equipment.setIsEffect(IsEffectEnum.NORMAL.getCode());
        equipment.setCreateBy(adminUser.getId());
        equipment.setCreateName(adminUser.getRealName());
        equipment.setCreateTime(DateUtil.date());
        equipment.setUpdateBy(adminUser.getId());
        equipment.setUpdateName(adminUser.getRealName());
        equipment.setUpdateTime(equipment.getCreateTime());
        equipment.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        equipment.setAuditName(StrUtil.EMPTY);
        equipment.setAuditBy(null);
        equipment.setAuditTime(null);
        equipment.setAuditRemark(StrUtil.EMPTY);
        equipmentMapper.insert(equipment);
        // 保存附件
        for (FileRecord fileRecord : fileRecords) {
            MaterialAttachment materialAttachment = new MaterialAttachment();
            materialAttachment.setId(IdUtil.getSnowflakeNextId());
            materialAttachment.setMaterialId(equipment.getId());
            materialAttachment.setMaterialType(MaterialTypeEnum.EQUIPMENT.getCode());
            materialAttachment.setFileId(fileRecord.getId());
            materialAttachment.setAttachmentName(fileRecord.getAttachmentName());
            materialAttachment.setAttachmentUrl(fileRecord.getAttachmentUrl());
            materialAttachment.setAttachmentSize(fileRecord.getAttachmentSize());
            materialAttachment.setAttachmentSuffix(fileRecord.getAttachmentSuffix());
            materialAttachment.setIsEffect(IsEffectEnum.NORMAL.getCode());
            materialAttachment.setCreateBy(adminUser.getId());
            materialAttachment.setCreateName(adminUser.getRealName());
            materialAttachment.setCreateTime(DateUtil.date());
            materialAttachment.setUpdateBy(adminUser.getId());
            materialAttachment.setUpdateName(adminUser.getRealName());
            materialAttachment.setUpdateTime(materialAttachment.getCreateTime());
            materialAttachmentMapper.insert(materialAttachment);
        }
        // todo 发起流程
    }

    /**
     * 修改仪器设备
     *
     * @param form      修改入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void edit(EquipmentEditForm form, AdminUser adminUser) {
        // 校验数据
        Equipment equipment = checkCanEditOrDelete(form.getId());
        // 修改仪器设备
        equipment.setEquipmentName(form.getEquipmentName());
        equipment.setUsage(CollUtil.isNotEmpty(form.getUsage()) ? StrUtil.join(",", form.getUsage()) : StrUtil.EMPTY);
        equipment.setBrand(StrUtil.blankToDefault(form.getBrand(), StrUtil.EMPTY));
        equipment.setModel(StrUtil.blankToDefault(form.getModel(), StrUtil.EMPTY));
        equipment.setOwnershipStatus(form.getOwnershipStatus());
        equipment.setInspectItem(CollUtil.isNotEmpty(form.getInspectItem()) ? StrUtil.join("/", form.getInspectItem()) : StrUtil.EMPTY);
        equipment.setConsignee(StrUtil.blankToDefault(form.getConsignee(), StrUtil.EMPTY));
        equipment.setReceiptDate(StrUtil.blankToDefault(form.getReceiptDate(), StrUtil.EMPTY));
        equipment.setUpdateBy(adminUser.getId());
        equipment.setUpdateName(adminUser.getRealName());
        equipment.setUpdateTime(DateUtil.date());
        equipment.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        equipmentMapper.updateById(equipment);
        // 先查询出原有的附件记录
        List<MaterialAttachment> oldAttachments = materialAttachmentMapper.selectList(Wrappers.<MaterialAttachment>lambdaQuery().eq(MaterialAttachment::getMaterialId, equipment.getId()));
        // 再删除附件
        materialAttachmentMapper.update(null, Wrappers.<MaterialAttachment>lambdaUpdate().eq(MaterialAttachment::getMaterialId, equipment.getId()).eq(CollUtil.isNotEmpty(form.getFileRecordIds()), MaterialAttachment::getFileId, form.getFileRecordIds()).set(MaterialAttachment::getIsEffect, IsEffectEnum.DELETE.getCode()));
        // 再新增附件
        if (CollUtil.isNotEmpty(form.getFileRecordIds())) {
            for (Long fileRecordId : form.getFileRecordIds()) {
                FileRecord fileRecord = fileRecordMapper.selectById(fileRecordId);
                if (fileRecord == null) {
                    throw new BusinessException(PcsResultCode.FILE_RECORD_NOT_EXIST);
                }
                MaterialAttachment equipmentAttachment = new MaterialAttachment();
                equipmentAttachment.setId(IdUtil.getSnowflakeNextId());
                equipmentAttachment.setMaterialId(equipment.getId());
                equipmentAttachment.setMaterialType(MaterialTypeEnum.EQUIPMENT.getCode());
                equipmentAttachment.setFileId(fileRecordId);
                equipmentAttachment.setAttachmentName(fileRecord.getAttachmentName());
                equipmentAttachment.setAttachmentUrl(fileRecord.getAttachmentUrl());
                equipmentAttachment.setAttachmentSize(fileRecord.getAttachmentSize());
                equipmentAttachment.setAttachmentSuffix(fileRecord.getAttachmentSuffix());
                equipmentAttachment.setIsEffect(IsEffectEnum.NORMAL.getCode());
                equipmentAttachment.setCreateBy(adminUser.getId());
                equipmentAttachment.setCreateName(adminUser.getRealName());
                equipmentAttachment.setCreateTime(DateUtil.date());
                equipmentAttachment.setUpdateBy(adminUser.getId());
                equipmentAttachment.setUpdateName(adminUser.getRealName());
                equipmentAttachment.setUpdateTime(equipmentAttachment.getCreateTime());
                materialAttachmentMapper.insert(equipmentAttachment);
            }
        }
        // todo 发起流程
    }

    /**
     * 删除仪器设备
     *
     * @param form      删除入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void del(EquipmentDelForm form, AdminUser adminUser) {
        // 校验数据
        Equipment equipment = checkCanEditOrDelete(form.getId());
        // 删除仪器设备
        equipment.setIsEffect(IsEffectEnum.DELETE.getCode());
        equipment.setUpdateBy(adminUser.getId());
        equipment.setUpdateName(adminUser.getRealName());
        equipment.setUpdateTime(DateUtil.date());
        equipment.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        equipmentMapper.updateById(equipment);
        // todo 发起流程
    }

    /**
     * 设备维护
     *
     * @param form      维护入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public void maintenance(EquipmentMaintenanceForm form, AdminUser adminUser) {
        // 校验数据
        Equipment equipment = equipmentMapper.selectById(form.getId());
        if (equipment == null || ObjectUtil.notEqual(IsEffectEnum.NORMAL.getCode(), equipment.getIsEffect())) {
            throw new BusinessException(PcsResultCode.EQUIPMENT_NOT_EXIST);
        }
        // 创建维护记录
        EquipmentMaintenanceRecord maintenanceRecord = new EquipmentMaintenanceRecord();
        maintenanceRecord.setEquipmentId(form.getId());
        maintenanceRecord.setMaintainerId(adminUser.getId());
        maintenanceRecord.setMaintainerName(adminUser.getRealName());
        maintenanceRecord.setMaintenanceContent(form.getMaintenanceContent());
        maintenanceRecord.setMaintenanceTime(form.getMaintenanceTime());
        maintenanceRecord.setNextMaintenanceTime(form.getNextMaintenanceTime());
        maintenanceRecord.setIsEffect(IsEffectEnum.NORMAL.getCode());
        maintenanceRecord.setCreateBy(adminUser.getId());
        maintenanceRecord.setCreateName(adminUser.getRealName());
        maintenanceRecord.setCreateTime(DateUtil.date());
        maintenanceRecord.setUpdateBy(adminUser.getId());
        maintenanceRecord.setUpdateName(adminUser.getRealName());
        maintenanceRecord.setUpdateTime(maintenanceRecord.getCreateTime());
        equipmentMaintenanceRecordMapper.insert(maintenanceRecord);
        // 更新维护人
        equipment.setMaintenanceUser(adminUser.getRealName());
        equipment.setUpdateBy(adminUser.getId());
        equipment.setUpdateName(adminUser.getRealName());
        equipment.setUpdateTime(DateUtil.date());
        equipmentMapper.updateById(equipment);
    }

    // =================================================================== 工作流回调 =================================================================== //

    // =================================================================== 私有方法区 =================================================================== //

    /**
     * 转换执行标准列表
     *
     * @param list 执行标准列表
     * @return 列表
     */
    private List<EquipmentListVO> convertList2VoList(List<Equipment> list) {
        List<EquipmentListVO> vos = ConvertUtil.convertList(list, EquipmentListVO.class);
        if (CollUtil.isNotEmpty(vos)) {
            Map<String, String> dictMap = getDictDataMap();
            // 异步加载检测方法
            CompletableFuture.allOf(vos.stream().map(vo -> CompletableFuture.runAsync(() -> {
                Equipment equipment = list.stream().filter(item -> item.getId().equals(vo.getId())).findFirst().orElse(null);
                if (equipment == null) {
                    return;
                }
                // 设置仪器用途名称列表
                if (StrUtil.isNotBlank(equipment.getUsage())) {
                    List<String> usageNameList = Arrays.stream(equipment.getUsage().split(",")).map(usage -> dictMap.getOrDefault(DictDataTypeEnum.EQUIPMENT_USAGE.getCode() + "_" + usage, StrUtil.EMPTY)).collect(Collectors.toList());
                    vo.setUsage(usageNameList);
                }
                // 设置仪器所属名称
                if (equipment.getOwnershipStatus() != null) {
                    vo.setOwnershipStatus(dictMap.getOrDefault(DictDataTypeEnum.EQUIPMENT_OWNERSHIP_STATUS.getCode() + "_" + equipment.getOwnershipStatus(), StrUtil.EMPTY));
                }
                // 设置检测能力
                if (StrUtil.isNotBlank(equipment.getInspectItem())) {
                    List<Long> inspectItemNameList = Arrays.stream(equipment.getInspectItem().split("/")).map(Long::parseLong).distinct().collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(inspectItemNameList)) {
                        List<DetectionMethod> detectionMethods = detectionMethodMapper.selectList(Wrappers.<DetectionMethod>lambdaQuery().in(DetectionMethod::getId, inspectItemNameList).select(DetectionMethod::getMethodName));
                        detectionMethods.sort(Comparator.comparingInt(o -> inspectItemNameList.indexOf(o.getId())));
                        vo.setInspectItem(detectionMethods.stream().map(DetectionMethod::getMethodName).collect(Collectors.toList()));
                    }
                }
                // 设置状态名称
                if (equipment.getStatus() != null) {
                    vo.setStatus(dictMap.getOrDefault(DictDataTypeEnum.EQUIPMENT_STATUS.getCode() + "_" + equipment.getStatus(), StrUtil.EMPTY));
                }
            }, commonExecutor)).toArray(CompletableFuture[]::new)).join();
        }
        return vos;
    }

    /**
     * 校验是否可以编辑或删除
     *
     * @param id 仪器设备ID
     * @return 仪器设备信息
     */
    private Equipment checkCanEditOrDelete(Long id) {
        Equipment equipment = equipmentMapper.selectById(id);
        if (equipment == null || ObjectUtil.notEqual(IsEffectEnum.NORMAL.getCode(), equipment.getIsEffect())) {
            throw new BusinessException(PcsResultCode.EQUIPMENT_NOT_EXIST);
        }
        if (ObjectUtil.notEqual(AuditStatusEnum.PASS.getCode(), equipment.getAuditStatus())) {
            throw new BusinessException(PcsResultCode.DATA_WAS_AUDITING_CANT_EDIT_OR_DELETE);
        }
        return equipment;
    }

    /**
     * 获取字典数据映射
     *
     * @return 字典数据映射 key: 字典类型_字典值, value: 字典标签
     */
    private Map<String, String> getDictDataMap() {
        // 查询所有有效的字典数据
        LambdaQueryWrapper<DictData> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(DictData::getIsEffect, IsEffectEnum.NORMAL.getCode());
        List<DictData> dictDataList = dictDataMapper.selectList(wrapper);
        // 构建父级字典映射 (字典值 -> ID)
        Map<String, Long> parentDictMap = dictDataList.stream().filter(dict -> dict.getParentId() == 0L).collect(Collectors.toMap(DictData::getDictValue, DictData::getId));
        // 构建子级字典映射 (父级字典值_子级字典值 -> 子级字典标签)
        return dictDataList.stream().filter(dict -> dict.getParentId() != 0L).filter(dict -> parentDictMap.containsValue(dict.getParentId())).collect(Collectors.toMap(dict -> {
            // 找到父级字典值
            String parentDictValue = parentDictMap.entrySet().stream().filter(entry -> entry.getValue().equals(dict.getParentId())).map(Map.Entry::getKey).findFirst().orElse(StrUtil.EMPTY);
            return parentDictValue + "_" + dict.getDictValue();
        }, DictData::getDictLabel, (existing, replacement) -> existing));
    }
}