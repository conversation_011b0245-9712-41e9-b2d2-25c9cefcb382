package com.lanhu.lims.gateway.admin;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.lanhu.lims.gateway.admin.diff.util.DiffUtil;
import com.lanhu.lims.gateway.admin.diff.vo.ChangeResult;
import com.lanhu.lims.gateway.admin.model.AdminUser;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2025/6/13 12:02
 */
@SpringBootTest
public class DiffService {

    @Test
    public void test(){
        AdminUser oldValue = AdminUser.builder()
                .realName("liuyi1")
                .mobile("0/1")
                .gender(0)
                .build();

        AdminUser newValue = AdminUser.builder()
                .realName("liuyi2")
                .mobile("")
                .gender(1)
                .build();

        ChangeResult changeResult = DiffUtil.compare(oldValue,newValue);

        System.out.println(JSONUtil.toJsonStr(changeResult));
    }
}
