package com.lanhu.lims.gateway.admin;

import cn.hutool.core.collection.CollUtil;
import com.lanhu.lims.gateway.admin.model.Equipment;
import org.junit.jupiter.api.Test;

import java.util.List;

/********************************
 * @title CusTest
 * @package com.lanhu.lims.gateway.admin
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/16 22:55
 * @version 0.0.1
 *********************************/
public class CusTest {
    @Test
    void test1() {
        Equipment equipment1 = new Equipment();
        equipment1.setUsage("0,1,2");

        Equipment equipment2 = new Equipment();
        equipment2.setUsage("0,1,2,3");

        Equipment equipment3 = new Equipment();
        equipment3.setUsage("0,1,2,3");

        List<Equipment> list = CollUtil.newArrayList(equipment1, equipment2);
        System.out.println(list.contains(equipment3));
    }
}
